// PROCESS CONFIGURATION .tin FILE
// 98.08.14 R1.2 -> R2.0 Up Grade
// -------------------------------------------------------------------

// -------------------------------------------------------------------------------
// MONITOR PROCESS
// -------------------------------------------------------------------------------

/= process_no           1001
 = process_type         1
 = process_name         248_5TELCO_REPORT_MONITOR_MMS_K
 = execute_directory    /user/neomms/command_5telco_key/bin/
 = execute_program      mnt
 = command_line_1       
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             730000
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

// -------------------------------------------------------------------------------
// LOGGER PROCESS
// -------------------------------------------------------------------------------

/= process_no           2001
 = process_type         2
 = process_name         248_5TELCO_LOG_GEM________MMS_K
 = execute_directory    /user/neomms/command_5telco_key/bin
 = execute_program      log
 = command_line_1       /data/log_mms/
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             730001
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

// -------------------------------------------------------------------------------
// CUSTOM PROCESS

/= process_no           3001
 = process_type         3
 = process_name         248_5TELCO_GEM_SND_F1_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F1_SND.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3002
 = process_type         3
 = process_name         248_5TELCO_GEM_RPT_F1_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F1_RPT.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1
 
/= process_no           3003
 = process_type         3
 = process_name         248_5TELCO_GEM_SND_F2_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F2_SND.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3004
 = process_type         3
 = process_name         248_5TELCO_GEM_RPT_F2_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F2_RPT.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3005
 = process_type         3
 = process_name         248_5TELCO_GEM_SND_F3_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F3_SND.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3006
 = process_type         3
 = process_name         248_5TELCO_GEM_RPT_F3_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F3_RPT.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3007
 = process_type         3
 = process_name         248_5TELCO_GEM_SND_F4_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F4_SND.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3008
 = process_type         3
 = process_name         248_5TELCO_GEM_RPT_F4_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F4_RPT.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3009
 = process_type         3
 = process_name         248_5TELCO_GEM_SND_F5_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F5_SND.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3010
 = process_type         3
 = process_name         248_5TELCO_GEM_RPT_F5_____MMS_K
 = execute_directory    /user/neomms/daemon_gemtek_key/bin
 = execute_program      telco_gemtek_mms
 = command_line_1       /user/neomms/daemon_gemtek_key/cfg/GEMTEK_MMS_F5_RPT.conf
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

// -------------------------------------------------------------------------------
// WATCH DOG
// -------------------------------------------------------------------------------

/= process_no           9999
 = process_type         5
 = process_name         248_5TELCO_WATCHDOG_______MMS_K
 = execute_directory    /user/neomms/command_5telco_key/bin
 = execute_program      dog
 = command_line_1       1
 = command_line_2       
 = command_line_3       
 = command_line_4       
 = command_line_5       
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/* END */
