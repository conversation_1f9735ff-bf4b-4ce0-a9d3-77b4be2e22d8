/*
 * Program Name : process_config.h
 * Comments     : process configuration information layout
 * -----------------------------------------------------------------------------
 * History
 *
 *      1) Initial Coding 97.12.31 IM,Song
 */

#ifndef __PROCESS_CONFIG_H__
#define __PROCESS_CONFIG_H__

#ifdef __cplusplus
extern "C" {
#endif

struct _process_config
{
    char process_no             [  7];
    char process_type           [  2];
    char process_name           [ 32];
    char execute_directory      [256];
    char execute_program        [256];
    char command_line_1         [256];
    char command_line_2         [256];
    char command_line_3         [256];
    char command_line_4         [256];
    char command_line_5         [256];
    char send_group_no          [  7];
    char monitor_process_no     [  7];
    char logger_process_no      [  7];
    char my_q_key               [ 10];
    char shm_key                [ 10];
    char sema_key               [ 10];
    char priority               [  3];
    char start_up_flag          [  1];
};

struct _process_config          process_config;

#ifdef __cplusplus
}
#endif

#endif /*  __PROCESS_CONFIG_H__  */

