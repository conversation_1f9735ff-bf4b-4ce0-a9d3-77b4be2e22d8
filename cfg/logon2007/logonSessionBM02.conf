
#   listen 할 주소및 포트 : logon 정보와 일치하는지 체크시 사용
gw.ip = *************
gw.port = 28406

# 해당 (micro sec) 초만큼 휴식후 새연결을 대기한다.
process.sleeptime = 10000

# logon 정보를 요청하는 domain socket 정보
domain.logondb = /user/neomms/domain/DB_logon_1

# 생성 시킬 프로세스(sender/report) 가 사용할 기본 domain socket  정보
domain.path = /user/neomms/domain

# 생성 시킬 프로세스 (sender/report) 실행파일 위치
process.bindir = /user/neomms/daemon_logon7/bin

# 생성 시킬 프로세스 (sender/report) 가 사용할 conf file path
#( 프로그램 생성시 4번째 인자로 프로세스 이름.conf 로 사용된다. )
process.cfgdir = /user/neomms/cfg/logon2007

#생성 시킬 프로세스(sender/report) 가 사용할 로그 디렉토리
log.path = /data/log_mms

db.requesttimeout = 7
# senderDB 요청시 ack 실패 줄때까지 재시도 하는 sec
# 모듈에서 ack 실패 timeout 보다 길어서는 안된다. / 프로세스 처리시간까지 고려 한다면 최소 2초이상 작아야 된다.
# 현재 윈도우 모듈(logon5) 의경우 socket timeout 옵션에 의해서 timeout 처리 되며, timeout 시간은 15초이다.

path.mmscontent = /data/neomms/CNT

# sender 정보를 요청하는 domain socket 정보
domain.senderdb = /user/neomms/domain/DB_MMS_sender_PART

# telco QueueID : LMS|MMS|COLOR
db.smsTelcoInfo = 27|27|27



