//============================================================================
// Name        : telco_sky_new.cpp
// Author      : KskyB Inc.
// Version     :
// Copyright   : Copyright@KSKYB
// Description : Hello World in C++, Ansi-style
//============================================================================

#include <iostream>
using namespace std;
#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include "Properties.h"
#include "SocketTCP.h"
#include "PacketCtrlSKY.h"
#include "myException.h"
#include "DatabaseORA.h"


#include <string.h>
#include <stdlib.h>
#include <stdio.h>

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

//#define MAX_TCP_BUF 8192
//#define MAX_TCP_BUF 1024*1024*3
#define MAX_TCP_BUF 1024*640*2+1024

typedef struct _THREAD_PARAM
{
	int sockfd;
	pthread_t tid;
	sql_context ctx;
	time_t sThisT, sLastT;
	time_t rThisT, rLastT;
	time_t rDbThisT, rDbLastT;
	vector<string> vtBuff;
	char buff[MAX_TCP_BUF];
	char logMsg[1024];
} ThreadParam;

KSKYB::CProperties g_prop;
KSKYB::CDatabaseORA g_oracle;
int activeProcess = true;
char PROCESS_NO[ 7], PROCESS_NAME[36];
char szUid[42]; //복호화된 DB 접속정보

//char encKey[42];
char encKey[110];
char bindType[2];
//char encDataKey[42];
char encDataKey[32+1];
char certKey[65];
char serverIP    [40];
char deliverPort [ 8];
char reportPort  [ 8];

/* 20130710 OJK CodeTable 추가  */
CODETABLE* pstCodeTable = NULL; /**< 각 해당 이통사 에러 코드 설명 테이블 */
int nCntCode = 0; /**< 에러 코드 테이블의 총 Row */

/* 비씨카드의 경우 extern
extern struct _message_info message_info;
extern struct _shm_info *shm_info;
*/

struct _message_info message_info;
struct _shm_info *shm_info;

void* procSend(void* param);
void* procRept(void* param);
int CheckThreadStatus(ThreadParam** param, int nCnt);
//int BindGateway(ThreadParam* tp, char* szTp, char* szKnd, char* offset, char* encPw, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst);
int BindGateway(ThreadParam* tp, char* szTp, char* szKnd, char* offset, char* encPw, char* encDataKey, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst);
int CertGateway(ThreadParam* tp, char *offset, char *encPw, char *encDataKey, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst);
int ClassifyResponse(ThreadParam* tp, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst);
int findCodeTab(CODETABLE* pstCodeTable, char* tCode,int cnt);
int getAllocResultTable(char* filename, CODETABLE** ppstCodeTable);

void Init_Server();
void CloseProcess(int sig);
void mnt(char *buf, int st, int err);
void log(char *buf, int st, int err);

int main(int argc, char* argv[])
{
	int idx;
	g_prop.load(argv[1]);
	int nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	int nThreadTyp = g_prop.getPropertyInt("gw.type");
	
	Init_Server();
	cout << "TREAD CNT:[" << nThreadCnt << "], TYP:[" << nThreadTyp << "]" << endl;
	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0)<0) 
	{
		cout << "ml_sub_init Error." << endl;
		return 0;
	}
	if (g_oracle.setEnableThreads()<0)
		return 0;
	ThreadParam pParam[nThreadCnt];
	nCntCode = getAllocResultTable(g_prop.getProperty("code.reportCodeFile"), &pstCodeTable);
	
	cout << "getAllocResultTable CNT:[" << nCntCode << "]" << endl;
	
	if (nCntCode <= 0)
	{
				cout << "getAllocResultTable Error" << endl;
	}
	if (nThreadTyp == 0 || nThreadTyp == 1) 
	{
		for (idx = 0; idx < nThreadCnt; idx++) 
		{
			memset(&pParam[idx], 0x00, sizeof(ThreadParam));
			pParam[idx].sockfd = idx;
			if (g_oracle.initThread(pParam[idx].ctx)<0 || pParam[idx].ctx==NULL) 
			{
				cout << "g_oracle.initThread Error" << endl;
				continue;
			}
			pthread_create(&pParam[idx].tid, NULL, nThreadTyp ? procRept : procSend, &pParam[idx]);
		}
		CheckThreadStatus((ThreadParam**)pParam, nThreadCnt);
	}
	ml_sub_end();
	return 0;
}

int CheckThreadStatus(ThreadParam** param, int nThreadCnt)
{
	char logMsg[256];
	int idx, status;
	KSKYB::CSocketTCP sockInst;
	ThreadParam tpSub[nThreadCnt];
	memcpy(tpSub, param, sizeof(ThreadParam) * nThreadCnt);

	while (true) {
		sockInst.Wait_A_Moment(0, 10);
		for (idx = 0; idx < nThreadCnt; idx++) 
		{
			if (pthread_kill(tpSub[idx].tid, 0) != 0) 
			{
				pthread_join(tpSub[idx].tid, (void**)&status);
				activeProcess = false;
				break;
			}
		}
		if (activeProcess == false) break;
	}
	for (idx = 0; idx < nThreadCnt; idx++) 
	{
		if (pthread_kill(tpSub[idx].tid, 0) != 0) 
		{
			pthread_join(tpSub[idx].tid, (void**)&status);
			g_oracle.freeThread(tpSub[idx].ctx);
			sprintf(logMsg, "pWorkThread::[%d] Closed..", idx);
			mnt(logMsg, 0, 0);
		}
		if( pstCodeTable != NULL)
		{
			free(pstCodeTable);
			pstCodeTable = NULL;
		}

	}
	return 0;
}

void* procSend(void* param)
{
	ThreadParam* tp = (ThreadParam*)param;
	cout << "START THREAD[" << tp->sockfd << "]" << endl;

	int msgSize = 0, nRet = 0;
	long long mms_id= 0;
	char szSubject[90];
	
	memset(encKey,0x00,sizeof(encKey));
	memset(bindType,0x00,sizeof(bindType));
	
	CPacketCtrlSKY packetSKY;

	KSKYB::CSocketTCP sockCert(g_prop.getPropertyInt("gw.port"));
	// 1. Cert 서버 연결
	sockCert.connectToServer(g_prop.getProperty("gw.addr"));
	sockCert.setLingerSeconds(1, 0);
	cout << "connect Cert Server Ok!![" << sockCert.getSocketId() << "]" << endl;

	//KSKYB::CSocketTCP sockInst(g_prop.getPropertyInt("gw.port"));
	// 1. 서버 연결
	//sockInst.connectToServer(g_prop.getProperty("gw.addr"));
	//sockInst.setLingerSeconds(1, 0);
	//cout << "connectToServer Ok!![" << tp->sockfd << "]" << endl;
	// 2. DB 연결
	if (g_oracle.connectToOracle(tp->ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0) 
	{
		cout << "connectToOracle Fail!![" << tp->sockfd << "]" << endl;
		activeProcess = false;
	}
	
	sprintf(bindType, g_prop.getProperty("gw.bindType")); 
	
	try {
		// 인증 요청
		if (CertGateway(tp, g_prop.getProperty("gw.offset"), encKey, encDataKey, packetSKY, sockCert) < 0)
			throw new myException(0, "CertGateway Error...");
		
		// Cert 서버 연결 종료
		sockCert.SocketClose();
	}
	catch (myException* excp) 
	{
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
		g_oracle.closeFromOracle(tp->ctx);
		sockCert.SocketClose();
		return NULL;
	}
	
	int liDeliverPort = atoi(deliverPort);	
	cout << "deliverPort : [" << liDeliverPort << "]" << endl;
	cout << "serverIP : [" << serverIP << "]" << endl;

	KSKYB::CSocketTCP sockInst(liDeliverPort);

	// Deliver Server 연결
	sockInst.connectToServer(serverIP);
	sockInst.setLingerSeconds(1, 0);

	cout << "connect Deliver Server Ok!![" << sockInst.getSocketId() << "]" << endl;
		
	try {
		//cout << "BindGateway before nCntCode ["<<nCntCode <<"]\n"<< endl;	
		
		// 2. 젬텍 접속
		//if (BindGateway(tp, 0, packetSKY, sockInst) < 0)
		if(BindGateway(tp, bindType, g_prop.getProperty("gw.kind"), g_prop.getProperty("gw.offset"), encKey, encDataKey, packetSKY, sockInst) < 0)
			throw new myException(0, "BindGateway Error...");
			
		cout << "encDataKey [" << encDataKey << "]["<<nCntCode <<"]\n"<< endl;	
		time(&tp->sLastT);

		while (activeProcess) {
			sockInst.Wait_A_Moment(0, 1000);
			// 3. 메시지 수신 확인
			//nRet = sockInst.checkSelect(100);
			nRet = sockInst.checkSelect(1000);
			if (nRet > 0) 
			{
				memset(tp->buff, 0x00, sizeof(tp->buff));
				if (sockInst.recieveMessage(tp->buff) < 0)
					throw new myException(0, "procSend recieveMessage Error...");
				if (ClassifyResponse(tp, packetSKY, sockInst) < 0)
					throw new myException(0, "procSend ClassifyResponse Error...");
			}
			else if (nRet < 0)
				throw new myException(0, "procSend Error Occurred(sockInst.checkSelect)....");
			
			mms_id= 0;
			map<string, string> mapSendMsg;
			//mapSendMsg.clear();
				
			// 4. 메시지 전송
			// 4.1. 메시지 추출
			if ((mms_id = g_oracle.getSendData(tp->ctx, g_prop.getProperty("gw.qunm"), mapSendMsg)) > 0) 
			{
				//sprintf(tp->logMsg, "[%s:%d]::SND[%lld][%s][%s]\n[%s][%d]", PROCESS_NAME, tp->sockfd, mms_id, "" /*mapSendMsg["ctn_id"].c_str()*/, mapSendMsg["callback"].c_str(),"" /* mapSendMsg["s_mms_id"].c_str()*/,nCntCode);
				sprintf(tp->logMsg, "[%s:%d]::SND[%lld][%s][%s]\n[%s][%d][%s]", PROCESS_NAME, tp->sockfd, mms_id, 
								"" /*mapSendMsg["ctn_id"].c_str()*/, mapSendMsg["callback"].c_str(),
								"" /* mapSendMsg["s_mms_id"].c_str()*/,nCntCode, mapSendMsg["id_code"].c_str());
				log(tp->logMsg, 0, 0);
				
				memset(tp->buff, 0x00, sizeof(tp->buff));
				
				memset(szSubject,0x00,sizeof(szSubject));
				
				strncpy(szSubject, mapSendMsg["msgTitle"].c_str(),sizeof(szSubject)-1); 	
				// memSet Log
				
#ifdef DEBUG
					//sprintf(tp->logMsg, "[%s:%d]::SND : after memset", PROCESS_NAME, tp->sockfd, mms_id, mapSendMsg["ctn_id"].c_str(), mapSendMsg["callback"].c_str(), mapSendMsg["s_mms_id"].c_str());
					//sprintf(tp->logMsg, "[%s:%d]::SND : after memset", PROCESS_NAME, tp->sockfd, mms_id, string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str(), string(*itrData).c_str());
					//log(tp->logMsg, 0, 0);
#endif
				// 4.2. DELIVER 전문 생성
					msgSize = packetSKY.getMsg_DELIVER_REQ(tp->buff, mms_id, g_prop.getProperty("gw.expired"), szSubject, mapSendMsg);
					
					/*if(msgSize == -2){
						char szSendResult[4+1];
						
						memset(szSendResult,0x00,sizeof(szSendResult));
						
						sprintf(szSendResult, g_prop.getProperty("gw.rptCode"));
						
						if (g_oracle.setSndMsgReportData(g_prop.getPropertyInt("gw.quid"), szSendResult, tp->ctx, mapSendMsg) < 0)
						throw new myException(0, "CDatabaseORA::setSndMsgReportData Error...");
					}else{2000byte초과시*/				  
#if (DEBUG >= 5)			
					sprintf(tp->logMsg, "[%s:%d]::SND[%lld][%s][%s]\n[%s][%.500s...] : after getMse_DELIVER_REQ", 
						PROCESS_NAME, tp->sockfd, 
					    mms_id, mapSendMsg["ctn_id"].c_str(), mapSendMsg["callback"].c_str(), 
					    mapSendMsg["s_mms_id"].c_str(), tp->buff);
					//sprintf(tp->logMsg, "[%s:%d]::SND[%lld][%s][%s]\n[%s] : after getMse_DELIVER_REQ", PROCESS_NAME, tp->sockfd, mms_id, string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str(), string(*itrData).c_str());
					log(tp->logMsg, 0, 0);
#endif
				//tp->vtBuff.clear();
#if (DEBUG >= 6)
				sprintf(tp->logMsg, "[%s:%d]::SND : after vecter clear", PROCESS_NAME, tp->sockfd);
					log(tp->logMsg, 0, 0);
#endif	
       							
				// 4.3. 전송
				if (sockInst.sendMessage(tp->buff, msgSize) < 0){throw new myException(0, "sendMessage Error...");}
#if (DEBUG >= 6)
					sprintf(tp->logMsg, "[%s:%d]::SND : after sendMessage ", PROCESS_NAME, tp->sockfd);
					log(tp->logMsg, 0, 0);
#endif			
				
				memset(tp->buff, 0x00, sizeof(tp->buff));
#if (DEBUG >= 6)
				sprintf(tp->logMsg, "[%s:%d]::SND : after memSet ", PROCESS_NAME, tp->sockfd);
				log(tp->logMsg, 0, 0);
#endif			
				
				// 4.4. 전송ACK 수신
				if (sockInst.recieveMessage(tp->buff) < 0){throw new myException(0, "procSend recieveMessage2 Error...");}
#if (DEBUG >= 6)
					sprintf(tp->logMsg, "[%s:%d]::SND : after recieveMessage ", PROCESS_NAME, tp->sockfd);
					log(tp->logMsg, 0, 0);
#endif			
				
				// 4.5. 전송ACK 처리
				if (ClassifyResponse(tp, packetSKY, sockInst) < 0){throw new myException(0, "ClassifyResponse Error...");}
#if (DEBUG >= 6)
					sprintf(tp->logMsg, "[%s:%d]::SND : after ClassifyResponse ", PROCESS_NAME, tp->sockfd);
					log(tp->logMsg, 0, 0);
#endif
				//2000byte초과시}				
			}
			else if (mms_id < 0) 
			{
				activeProcess = false;
				continue;
			}
			
			mapSendMsg.clear();
				
			time(&tp->sThisT);
			if (difftime(tp->sThisT,tp->sLastT) > 50) 
			{
				memset(tp->buff, 0x00, sizeof(tp->buff));
				// 5. LINK 체크
				// 5.1. LINK 전문 생성
				msgSize = packetSKY.getMsg_PING_REQ(tp->buff);
				// 5.2. LINK 전문 전송
				if (sockInst.sendMessage(tp->buff, msgSize) < 0)
					throw new myException(0, "sendMessage Error...");
					
				sprintf(tp->logMsg, "TID[%d]::SND[PING]", tp->sockfd);
				mnt(tp->logMsg, 0, 0);
				
				time(&tp->sLastT);
			}
		}
	}
	catch (myException* excp) 
	{
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
	}
	if( pstCodeTable != NULL)
	{
		free(pstCodeTable);
		pstCodeTable = NULL;
	}
	g_oracle.closeFromOracle(tp->ctx);
	sockInst.SocketClose();
	activeProcess = false;
	cout << "OUT THREAD[" << tp->sockfd << "]" << endl;
	return NULL;
}

void* procRept(void* param)
{
	ThreadParam* tp = (ThreadParam*)param;
	cout << "START THREAD[" << tp->sockfd << "]" << endl;

	int msgSize = 0, nRet = 0;
	memset(encKey,0x00,sizeof(encKey));
	memset(bindType,0x00,sizeof(bindType));
	CPacketCtrlSKY packetSKY;
	KSKYB::CSocketTCP sockCert(g_prop.getPropertyInt("gw.port"));
	sockCert.connectToServer(g_prop.getProperty("gw.addr"));
	sockCert.setLingerSeconds(1, 0);
	//cout << "connectToServer Ok!![" << tp->sockfd << "]" << endl;
	cout << "connect Cert Server Ok!![" << sockCert.getSocketId() << "]" << endl;
	
	//KSKYB::CSocketTCP sockInst(g_prop.getPropertyInt("gw.port"));
	//sockInst.connectToServer(g_prop.getProperty("gw.addr"));
	//sockInst.setLingerSeconds(1, 0);
	if (g_oracle.connectToOracle(tp->ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0) 
	{
		cout << "connectToOracle Fail!![" << tp->sockfd << "]" << endl;
		activeProcess = false;
	}
	
	sprintf(bindType, g_prop.getProperty("gw.bindType")); 

	try {
		memset(encDataKey, 0x00, sizeof(encDataKey));
		// 인증 요청
		if (CertGateway(tp, g_prop.getProperty("gw.offset"), encKey, encDataKey, packetSKY, sockCert) < 0)
			throw new myException(0, "CertGateway Error...");
		
		// Cert 서버 연결 종료
		sockCert.SocketClose();
	}
	catch (myException* excp) 
	{
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
		g_oracle.closeFromOracle(tp->ctx);
		sockCert.SocketClose();
		return NULL;
	}
	
	int liReportPort = atoi(reportPort);
	cout << "reportPort : [" << liReportPort << "]" << endl;
	cout << "serverIP : [" << serverIP << "]" << endl;

	KSKYB::CSocketTCP sockInst(liReportPort);
	// Report Server 연결
	sockInst.connectToServer(serverIP);
	sockInst.setLingerSeconds(1, 0);

	cout << "connect Report Server Ok!![" << sockInst.getSocketId() << "]" << endl;

	try 
	{
		if(BindGateway(tp, bindType, g_prop.getProperty("gw.kind"), g_prop.getProperty("gw.offset"), encKey, encDataKey, packetSKY, sockInst) < 0)
			throw new myException(0, "BindGateway Error...");
		
		time(&tp->rLastT);
		time(&tp->rDbLastT);
		while (activeProcess) {
			sockInst.Wait_A_Moment(0, 1000);
			nRet = sockInst.checkSelect(100);
			if (nRet > 0) 
			{
				memset(tp->buff, 0x00, sizeof(tp->buff));
				if (sockInst.recieveMessage(tp->buff) < 0)
					throw new myException(0, "procRept recieveMessage Error...");
				if (ClassifyResponse(tp, packetSKY, sockInst) < 0)
					throw new myException(0, "procRept ClassifyResponse Error...");
			}
			else if (nRet < 0)
				throw new myException(0, "procRept Error Occurred(sockInst.checkSelect)....");
			
			time(&tp->rThisT);
			if (difftime(tp->rThisT,tp->rLastT)> 50 ) 
			{
				memset(tp->buff, 0x00, sizeof(tp->buff));
				msgSize = packetSKY.getMsg_PING_REQ(tp->buff);
				if (sockInst.sendMessage(tp->buff, msgSize) < 0)
					throw new myException(0, "sendMessage Error...");
					sprintf(tp->logMsg, "TID[%d]::RPT[PING]", tp->sockfd);
					mnt(tp->logMsg, 0, 0);
				time(&tp->rLastT);
			}
			
			time(&tp->rDbThisT);
			if (difftime(tp->rDbThisT,tp->rDbLastT)>=1800) 
			{
				//memset(tp->buff, 0x00, sizeof(tp->buff));
				if (g_oracle.getCheckReport(tp->ctx) < 0)
					throw new myException(0, "CDatabaseORA::setReportCheck Error...");
					sprintf(tp->logMsg, "TID[%d]::setReportCheck", tp->sockfd);
					mnt(tp->logMsg, 0, 0);
				time(&tp->rDbLastT);
			}
		}
	}
	catch (myException* excp) 
	{
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
	}

	g_oracle.closeFromOracle(tp->ctx);
	sockInst.SocketClose();
	activeProcess = false;
	cout << "OUT THREAD[" << tp->sockfd << "]" << endl;
	return NULL;
}

int BindGateway(ThreadParam* tp, char* szTp, char* szKnd, char* offset, char* encPw, char* encDataKey, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst)
{
	int msgSize = 0;
	//char szSID[10], szPWD[10];
	char szSID[20], szPWD[110];
	try 
	{
		//cout << "BindGateway1 encDataKey ["<<encDataKey <<"]\n"<< endl;	
		sprintf(szSID, "gw.sid%d", tp->sockfd + 1);
		//sprintf(szPWD, "gw.pwd%d", tp->sockfd + 1);
		memset(tp->buff, 0x00, sizeof(tp->buff));
		//memset(encPw, 0x00, sizeof(encPw));
		//memset(encDataKey, 0x00, sizeof(encDataKey));
		//msgSize = packetSKY.getMsg_BIND_REQ(tp->buff, g_prop.getProperty(szSID), g_prop.getProperty(szPWD), szTp, szKnd, offset, encPw);
		msgSize = packetSKY.getMsg_BIND_REQ(tp->buff, g_prop.getProperty(szSID), encPw, szTp, szKnd, offset, certKey);
		cout<< "1_getMsg_BIND_REQ"<<tp->buff<<endl;
		//cout<< "1_getMsg_BIND_REQ"<<*tp->buff<<endl;
		//cout<< "1_getMsg_BIND_REQ"<<&tp->buff<<endl;
		//cout << "BindGateway encPw ["<<encPw <<"]\n"<< endl;	

		
		if (sockInst.sendMessage(tp->buff, msgSize) < 0)
			throw new myException(0, "BindGateway::sendMessage Error...");
		memset(tp->buff, 0x00, sizeof(tp->buff));

		if (sockInst.recieveMessage(tp->buff) < 0)
			throw new myException(0, "BindGateway::recieveMessage Error...");
		if (ClassifyResponse(tp, packetSKY, sockInst) < 0)
					throw new myException(0, "ClassifyResponse Error...");
					
		//if(packetSKY.getMsgEncKey(encDataKey,g_prop.getProperty(szSID), g_prop.getProperty(szPWD),nCntCode) < 0)
		//	throw new myException(0, "BindGateway getMsgEncKey Error...");	
			
		//cout << "BindGateway encDataKey ["<<encDataKey <<"]\n"<< endl;			
	}
	catch (myException* excp) 
	{
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
		return -1;
	}
	return 1;
}

int ClassifyResponse(ThreadParam* tp, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst)
{
	int msgSize = 0;
	int ret = 0;
	
	
	/*cout<<"packetSKY.getMsgCode(tp->buff):"<<packetSKY.getMsgCode(tp->buff)<<endl;	// 확인용 출력 코드 */
	try {
		switch (packetSKY.getMsgCode(tp->buff)) {
		case CPacketCtrlSKY::TYPE_CERT_ACK: 
		{
			if (packetSKY.getData_CertAck(tp->buff, tp->vtBuff) < 0)
				throw new myException(0, "CPacketCtrlSKY::getData_CertAck Error...");

 			vector<string>::iterator itrData;
			itrData = tp->vtBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::CERT ACK[%s][%s][%s][%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData+1)).c_str(), string(*(itrData+2)).c_str(), string(*(itrData+3)).c_str(), string(*(itrData+4)).c_str());
			log(tp->logMsg, 0, 0);

			if(strcmp((char *)string(*itrData).c_str(),"0") != 0) 
			{
				sprintf(tp->logMsg, "[%s:%d]::CERT ACK error [%s]", PROCESS_NAME, tp->sockfd, string(*(itrData+2)).c_str());
				log(tp->logMsg, 0, 0);

				throw new myException(0, "CDatabaseORA::Cert Result Error...");
			}
			else
			{
				memset(certKey, 0x00, sizeof(certKey));
				sprintf(certKey, "%s", string(*(itrData+1)).c_str());

				memset(serverIP, 0x00, sizeof(serverIP));
				sprintf(serverIP, "%s", string(*(itrData+2)).c_str());

				memset(deliverPort, 0x00, sizeof(deliverPort));
				sprintf(deliverPort, "%s", string(*(itrData+3)).c_str());

				memset(reportPort, 0x00, sizeof(reportPort));
				sprintf(reportPort, "%s", string(*(itrData+4)).c_str());
			}
			tp->vtBuff.clear();

			break;
		}
		case CPacketCtrlSKY::TYPE_BIND_ACK: 
		{
			if (packetSKY.getData_BndAck(tp->buff, tp->vtBuff) < 0)
				throw new myException(0, "CPacketCtrlSKY::getData_BndAck Error...");
 			vector<string>::iterator itrData;
			itrData = tp->vtBuff.begin();			
			sprintf(tp->logMsg, "[%s:%d]::BND[%s][%s][%s][%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData+1)).c_str(), string(*(itrData+2)).c_str(), string(*(itrData+3)).c_str(), string(*(itrData+4)).c_str());
			log(tp->logMsg, 0, 0);
			
			if (atoi(string(*itrData).c_str()) != 0) {				
				sprintf(tp->logMsg, "[%s:%d]::Bind Error [%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData+4)).c_str());
				log(tp->logMsg, 0, 0);
				throw new myException(0, "CPacketCtrlSKY::Bind Result Value Error...");
			}
			tp->vtBuff.clear();
			break;
		}
		case CPacketCtrlSKY::TYPE_PING:
//			sprintf(tp->logMsg, "TID[%d]::PING", tp->sockfd);
//			mnt(tp->logMsg, 0, 0);
			msgSize = packetSKY.getMsg_PING_RES(tp->buff);
			if (sockInst.sendMessage(tp->buff, msgSize) < 0)
				throw new myException(0, "sendMessage Error...");
			break;
		case CPacketCtrlSKY::TYPE_PONG:
				sprintf(tp->logMsg, "TID[%d]::PONG", tp->sockfd);
  				mnt(tp->logMsg, 0, 0);
			break;
		case CPacketCtrlSKY::TYPE_DELIVER_ACK: 
		{
			
#if (DEBUG > 6)		
				sprintf(tp->logMsg, "[%s:%d]::ACK[] : TYPE_DELIVER_ACK Case start", PROCESS_NAME, tp->sockfd);
				log(tp->logMsg, 0, 0);
#endif		
				if (packetSKY.getData_SndAck(tp->buff, tp->vtBuff) < 0){throw new myException(0, "CPacketCtrlSKY::getData_SndAck Error...");}
			
				
#if (DEBUG > 6)		
				sprintf(tp->logMsg, "[%s:%d]::ACK[] : after getData_SndAck", PROCESS_NAME, tp->sockfd);
				log(tp->logMsg, 0, 0);
#endif
			vector<string>::iterator itrData;
			itrData = tp->vtBuff.begin();
				
#if (DEBUG > 6)		
				sprintf(tp->logMsg, "[%s:%d]::ACK[] : before ACK LOG", PROCESS_NAME, tp->sockfd);
				log(tp->logMsg, 0, 0);
#endif		
			sprintf(tp->logMsg, "[%s:%d]::ACK[%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData + 2)).c_str());
			log(tp->logMsg, 0, 0);
#if (DEBUG > 6)		
			sprintf(tp->logMsg, "[%s:%d]::ACK[] : after ACK LOG", PROCESS_NAME, tp->sockfd);
			log(tp->logMsg, 0, 0);
#endif

#ifdef _RETRY_TEST
			*itrData = "302";
#endif
			
			if(strcmp((char *)string(*itrData).c_str(),"0") != 0) 
			{

				if(strcmp((char *)string(*itrData).c_str(),"100") == 0 ||strcmp((char *)string(*itrData).c_str(),"101") == 0 ||
					 strcmp((char *)string(*itrData).c_str(),"110") == 0 ||strcmp((char *)string(*itrData).c_str(),"212") == 0 ||
					 strcmp((char *)string(*itrData).c_str(),"300") == 0 ||strcmp((char *)string(*itrData).c_str(),"301") == 0 ||
					 strcmp((char *)string(*itrData).c_str(),"302") == 0 ||strcmp((char *)string(*itrData).c_str(),"303") == 0) 
				{
					if (g_oracle._putMsgRetryDB(atoll(string(*(itrData + 2)).c_str()),g_prop.getProperty("gw.qunm"), tp->ctx) < 0)
					throw new myException(0, "CDatabaseORA::_putMsgRetryDB Error...");
				}else{
					ret = findCodeTab(pstCodeTable, (char *)string(*itrData).c_str(), nCntCode);
					
					if(ret >= 0)
					{
				
						sprintf(tp->logMsg, "[%s:%d]::ACK REPORT [%s][%s][%s]", PROCESS_NAME, tp->sockfd,pstCodeTable[ret].tCode,pstCodeTable[ret].kCode, string(*(itrData + 2)).c_str());
						log(tp->logMsg, 0, 0);
						
						if (g_oracle.setSndAckData(g_prop.getPropertyInt("gw.quid"), pstCodeTable[ret].kCode, tp->ctx, tp->vtBuff) < 0)
						throw new myException(0, "CDatabaseORA::setSndAckData1 Error...");
					
						if (g_oracle.setSndAckReportData(g_prop.getPropertyInt("gw.quid"), pstCodeTable[ret].kCode, tp->ctx, tp->vtBuff) < 0)
						throw new myException(0, "CDatabaseORA::setSndAckReportData1 Error...");
					}else{
						char szAckResult[4+1];
						
						memset(szAckResult,0x00,sizeof(szAckResult));
						
						sprintf(szAckResult, "9999");
						
						sprintf(tp->logMsg, "[%s:%d]::ACK REPORT CODE Error[%s][%s]", PROCESS_NAME, tp->sockfd, szAckResult, string(*(itrData + 2)).c_str());
						log(tp->logMsg, 0, 0);
						
						if (g_oracle.setSndAckData(g_prop.getPropertyInt("gw.quid"), szAckResult, tp->ctx, tp->vtBuff) < 0)
						throw new myException(0, "CDatabaseORA::setSndAckData2 Error...");
						
						if (g_oracle.setSndAckReportData(g_prop.getPropertyInt("gw.quid"), szAckResult, tp->ctx, tp->vtBuff) < 0)
						throw new myException(0, "CDatabaseORA::setSndAckReportData2 Error...");
					}
				}
			}else{
				
				ret = findCodeTab(pstCodeTable, (char *)string(*itrData).c_str(), nCntCode);
					
					if(ret >= 0)
					{
						if (g_oracle.setSndAckData(g_prop.getPropertyInt("gw.quid"), pstCodeTable[ret].kCode, tp->ctx, tp->vtBuff) < 0)
							throw new myException(0, "CDatabaseORA::setSndAckData3 Error...");
					}else{
						char szAckResult[4+1];
						
						memset(szAckResult,0x00,sizeof(szAckResult));
						
						sprintf(szAckResult, "9999");
						
						sprintf(tp->logMsg, "[%s:%d]::ACK CODE Error[%s][%s]", PROCESS_NAME, tp->sockfd, szAckResult, string(*(itrData + 2)).c_str());
						log(tp->logMsg, 0, 0);
						
						if (g_oracle.setSndAckData(g_prop.getPropertyInt("gw.quid"), szAckResult, tp->ctx, tp->vtBuff) < 0)
						throw new myException(0, "CDatabaseORA::setSndAckData4 Error...");
					}
			}
			
			tp->vtBuff.clear();
#if (DEBUG > 6)		
				sprintf(tp->logMsg, "[%s:%d]::ACK[] : TYPE_DELIVER_ACK Case (clear) end", PROCESS_NAME, tp->sockfd);
				log(tp->logMsg, 0, 0);
#endif			
			break;
		}
		case CPacketCtrlSKY::TYPE_REPORT: 
		{
			if (packetSKY.getData_Report(tp->buff, tp->vtBuff) < 0)
				throw new myException(0, "CPacketCtrlSKY::getData_Report Error...");
			
			
//			tp->vtBuff.push_back("8");
			vector<string>::iterator itrData;
			itrData = tp->vtBuff.begin();
			
			sprintf(tp->logMsg, "[%s:%d]::RPT[%s][%s][%s][%s][%s][%s]", PROCESS_NAME, tp->sockfd, (char*)string(*itrData).c_str(), "-"/*string(*(itrData + 1)).c_str()*/, (char*)string(*(itrData + 2)).c_str(), (char*)string(*(itrData + 3)).c_str(), (char*)string(*(itrData + 4)).c_str(), (char*)string(*(itrData + 5)).c_str());
			log(tp->logMsg, 0, 0);
			
			//kskyb 코드
			ret = findCodeTab(pstCodeTable, (char *)string(*itrData).c_str(), nCntCode);
		
			if(ret >= 0)
			{
				if (g_oracle.setReportData(g_prop.getPropertyInt("gw.quid"), pstCodeTable[ret].kCode, tp->ctx, tp->vtBuff, "report") < 0)
				throw new myException(0, "CDatabaseORA::setReportData Error...");
			}else{
				char szReportResult[4+1];
						
				memset(szReportResult,0x00,sizeof(szReportResult));
						
				sprintf(szReportResult, "9999");
				
				sprintf(tp->logMsg, "[%s:%d]::REPORT CODE Error[%s][%s]", PROCESS_NAME, tp->sockfd, szReportResult, string(*(itrData + 2)).c_str());
						log(tp->logMsg, 0, 0);
				
				if (g_oracle.setReportData(g_prop.getPropertyInt("gw.quid"), szReportResult, tp->ctx, tp->vtBuff, "report") < 0)
				throw new myException(0, "CDatabaseORA::setReportData Error...");
				
			}
			memset(tp->buff, 0x00, sizeof(tp->buff));
			
			msgSize = packetSKY.getMsg_REPORT_ACK(tp->buff,(char *)string(*itrData).c_str(),(char *)string(*(itrData+2)).c_str(),(char *)string(*(itrData+3)).c_str());
			
			if (sockInst.sendMessage(tp->buff, msgSize) < 0)
				throw new myException(0, "getMsg_REPORT_ACK sendMessage Error...");
				
			sprintf(tp->logMsg, "[%s:%d]::RPTACK[%s]", PROCESS_NAME, tp->sockfd, (char*)string(*(itrData + 2)).c_str());
			log(tp->logMsg, 0, 0);
				
			tp->vtBuff.clear();
			break;
		}
		default:
			break;
		}
	}
	catch (myException* excp) {
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
		return -1;
	}
	return 1;
}

void Init_Server()
{
	setpgrp();
	cout << "Process Running.. Please wait 3 seconds." << endl;
	sleep(3);
	signal(SIGHUP, CloseProcess);
	signal(SIGCLD, SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess);
	signal(SIGINT, CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess);
}

void CloseProcess(int sig)
{
	cout << "CloseProcess Start & Exit [" << sig << "]" << endl;
	activeProcess = false;
}

void log(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) 
	{
		printf("%s ml_sub_send_log error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

void mnt(char *buf, int st, int err)
{
	if (ml_sub_send_moni(buf, strlen(buf), 3, st, err) <= 0) 
	{
		printf("%s ml_sub_send_moni error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

int getAllocResultTable(char* filename,CODETABLE** ppstCodeTable)
{

    FILE* fp=NULL;
    char buff[1024];
    int cnt=0;
    char* szTmp;

    fp = fopen(filename,"rt");
    if( fp == NULL )
    {
        printf("ERROR [%s]\n",strerror(errno));
        return -1;
    }
		
// count
    memset(buff,0x00,sizeof buff);
    while(fgets(buff,sizeof(buff),fp) )
    {
        if(buff[0] == '=')
            cnt++;
    }

    *ppstCodeTable = (CODETABLE*)malloc(sizeof(CODETABLE)*cnt);
    memset(*ppstCodeTable,0x00,sizeof(CODETABLE)*cnt);

    fseek(fp,0L,SEEK_SET);
    cnt=0;
    memset(buff,0x00,sizeof buff);
// set data
    while(fgets(buff,sizeof(buff),fp) )
    {
        if(buff[0] != '=')
            continue;

        szTmp = (char*)strtok(buff," =");
        if( szTmp == NULL)
            continue;

        memcpy((*ppstCodeTable)[cnt].tCode, szTmp,strlen(szTmp));
        szTmp = (char*)strtok(NULL," =");
        //cout<<"ppstCodeTable tCode ["<<(*ppstCodeTable)[cnt].tCode <<"]\n"<<endl;
       
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].kCode, szTmp,strlen(szTmp));

				//cout<<"ppstCodeTable kCode ["<<(*ppstCodeTable)[cnt].kCode <<"]\n"<<endl;

        szTmp = (char*)strtok(NULL,"=");
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].desc, szTmp,strlen(szTmp));
        cnt++;
    }


    fclose(fp);

    return cnt;
}

int findCodeTab(CODETABLE* pstCodeTable, char* tCode, int cnt)
{
    int i=0;

    for(i=0;i<cnt;i++)
    {
			if(strcmp(pstCodeTable[i].tCode, tCode) == 0)return i;
    }
    for(i=0;i<cnt;i++)			/* wiseCan Unknown Code "20" 테이블에 값이 없으면 Unknown Code를 찾아 인덱스값 반환 */
    {
			if(strcmp(pstCodeTable[i].tCode, "20") == 0)return i;
    }
    return -1;
}

int CertGateway(ThreadParam* tp, char *offset, char *encPw, char *encDataKey, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst)
{
	int msgSize = 0;
	char szSID[20], szPWD[20];

	try
	{
		sprintf(szSID, "gw.sid%d", tp->sockfd + 1);
		sprintf(szPWD, "gw.pwd%d", tp->sockfd + 1);

		memset(tp->buff, 0x00, sizeof(tp->buff));
		memset(encPw, 0x00, sizeof(encPw));

		memset(encDataKey, 0x00, sizeof(encDataKey));

		msgSize = packetSKY.getMsg_CERT_REQ(tp->buff, g_prop.getProperty(szSID), g_prop.getProperty(szPWD), offset, encPw, encDataKey);

		cout<< "1_getMsg_CERT_REQ tp->buff "<<tp->buff<<endl;
		cout<< "1_getMsg_CERT_REQ &tp->buff "<<&tp->buff<<endl;
		cout << "CertGateway encPw ["<<encPw <<"]\n"<< endl;
		cout << "CertGateway encDataKey ["<<encDataKey <<"]"<< endl;

		if (sockInst.sendMessage(tp->buff, msgSize) < 0)
			throw new myException(0, "CertGateway::sendMessage Error...");

		if (sockInst.recieveMessage(tp->buff) < 0)
			throw new myException(0, "CertGateway::recieveMessage Error...");

		if (ClassifyResponse(tp, packetSKY, sockInst) < 0)
			throw new myException(0, "CertGateway::ClassifyResponse Error...");
	}
	catch (myException* excp)
	{
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
		return -1;
	}

	return 1;
}