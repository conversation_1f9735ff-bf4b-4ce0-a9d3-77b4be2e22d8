COPY=cp
#SMS_DBSTRING=NEO228
SMS_DBSTRING=NEO223
SMS_DBID=neomms2
SMS_DBPASS=password

PROC=proc
CC=g++
RM=rm
LINT=lint
#CFLAGS = -g -Wall -DDEBUG=5
CFLAGS = -std=gnu++98 -g -Wall -DDEBUG=5
#CFLAGS = -std=gnu++11 -g -Wall -DDEBUG=5
CURLFLAGS = -g -Wall -D_URL_MODE -DDEBUG
CMMSFLAGS = -g -Wall -D_MMS_MODE

#ORG_D=${HOME}/daemon_gemtek_key
ORG_D=${HOME}/CLionProjects/neomms_248/daemon_gemtek_key
#BIN_D=${ORG_D}/bin
BIN_D=${ORG_D}/bin_tmp
INST_D=$(ORG_D)/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

#EXT_LIB=${HOME}/command_5telco_key/obj/sms_ctrlsub++.o
EXT_LIB=${HOME}/CLionProjects/neomms_248/command_5telco_key/obj/sms_ctrlsub++.o
#EXT_INC=${HOME}/command_5telco_key/inc
EXT_INC=${HOME}/CLionProjects/neomms_248/command_5telco_key/inc

INCLUDE = -I$(INC_D) -I$(LIB_D)

ORACLE_VERSION  = 11
#ORACLE_INCLUDES = -I$(ORACLE_HOME)/rdbms/demo -I$(ORACLE_HOME)/rdbms/public
ORACLE_INCLUDES = -I/usr/include/oracle/21/client64
ORACLE_LIBS     = -L$(ORACLE_HOME)/lib

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
#ORA_INC = ${ORACLE_HOME}/precomp/public
ORA_INC = /usr/include/oracle/21/client64

GEN_FLAGS    =  -fno-exceptions -fno-rtti -D_REENTRANT=1
GEN_INCLUDES =
GEN_LIBS     = -ldl -lclntsh -lpthread

INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(LIB_D) -I$(ORA_INC)
#LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3)
ORALIB = -lclntsh -lcrypto -lssl

all: telco_gemtek_mms

telco_gemtek_mms : $(OBJ_D)/telco_gemtek_new.o $(OBJ_D)/Properties.o $(OBJ_D)/SocketTCP.o $(OBJ_D)/PacketCtrlSKY.o $(OBJ_D)/DatabaseORA.o $(OBJ_D)/myException.o $(OBJ_D)/base64.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) $(LIBD) ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} ${LIBS} -lpthread $(ORALIB) -o $(BIN_D)/telco_gemtek_mms_tmp

$(OBJ_D)/sha1.o: $(LIB_D)/sha1.cpp
	$(RM) -rf $(OBJ_D)/sha1.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^
	
$(OBJ_D)/telco_gemtek_new.o: $(SRC_D)/telco_gemtek_new.cpp
	$(RM) -rf $(OBJ_D)/telco_gemtek_new.*
	$(COPY) $(SRC_D)/telco_gemtek_new.cpp $(OBJ_D)/telco_gemtek_new.pc
	$(PROC) MODE=ORACLE DBMS=V8 UNSAFE_NULL=YES iname=$(OBJ_D)/telco_gemtek_new.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(SMS_DBID)/$(SMS_DBPASS)@$(SMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/telco_gemtek_new.o $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/telco_gemtek_new.cpp

$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/SocketTCP.o: $(LIB_D)/SocketTCP.cpp
	$(RM) -rf $(OBJ_D)/SocketTCP.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^


	
$(OBJ_D)/PacketCtrlSKY.o: $(LIB_D)/PacketCtrlSKY.cpp
	$(RM) -rf $(OBJ_D)/PacketCtrlSKY.*
	$(CC) -o $@ $(CFLAGS) $(OBJ_D)/SocketTCP.o -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/DatabaseORA.o: $(LIB_D)/DatabaseORA.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA.*
	$(COPY) $(LIB_D)/DatabaseORA.cpp $(OBJ_D)/DatabaseORA.pc
	$(PROC) MODE=ORACLE DBMS=V8 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=10 \
		define=__sparc SQLCHECK=FULL userid=$(SMS_DBID)/$(SMS_DBPASS)@$(SMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA.o $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^


$(OBJ_D)/base64.o: $(LIB_D)/base64.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^
		
clean:
	rm -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/telco_gemtek_mms_tmp $(INST_D)/telco_gemtek_mms
	#rm tp*
