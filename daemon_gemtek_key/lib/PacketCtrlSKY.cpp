/*
 * PacketCtrlSKY.cpp
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#include "PacketCtrlSKY.h"
#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <time.h>
#include <openssl/sha.h>
#include <openssl/aes.h>
#include <openssl/des.h>
#include <openssl/rand.h>
#include <string.h>
#include <sys/stat.h>
#include <errno.h>
#include "base64.h"

#define BLOCK_SIZE 16

char tmpLog2[MAX_BUFF];
char tmpLog[MAX_BUFF*MAX_BUFF+1];
long nmSize = 0; //text size  

void log2(char *buf, int st, int err);
int SHA1_Hash(char* data, int data_size, char** result);
int SHA256_Hash(char* data, int data_size, char* result);
int PKCS5_Padding(char *source, int size, int block_size, char **dest);
int PKCS5_UnPadding(char* src, int size, int block_size, char** result);
//string SHA1_Hash(char* data);
static void hex_print(const void* pv, size_t len);


int CPacketCtrlSKY::getMsg_CERT_REQ(char* pBuff, char* id, char* pw, char* offset, char* encPw2, char * encDataKey)
{
	//BIND_REQ BindSnd;
	CERT_REQ CertSnd;
	//string encPw;
	//char* encPw[20];
	char encPw[100];
	//string strPw;
	//strPw = pw;
	
	int pwLen = 0;
	
	int pwReultLen = 0;
	
	memset(encPw, 0x00, sizeof(encPw));
	memset((char*)&CertSnd, 0x00, sizeof(CERT_REQ));
	sprintf(CertSnd.header.headType, "%d",TYPE_CERT_REQ);
	sprintf(CertSnd.header.msgLeng, "%d", (int)(sizeof(CERT_REQ) - sizeof(HEADER)));
	sprintf(CertSnd.szCID, id);
	//sprintf(BindSnd.szType, szTp);
	//sprintf(BindSnd.szKind, szKnd);
	//sprintf(BindSnd.szVersion, "PE.1.0");
	
	pwLen = strlen(pw);
	
	//pwReultLen = SHA1_Hash(pw,pwLen,encPw);
	pwReultLen = SHA256_Hash(pw,pwLen,encPw);

	cout << "SHA256 encPw [" << encPw << "]" << endl;
	cout << "SHA256 pwReultLen [" << pwReultLen << "]" << endl;

	// Get encDataKey
	int liOffset = 0;
	liOffset = atoi(offset);

	cout << "enckey offset = " << liOffset << endl;

	memcpy(encDataKey, encPw + liOffset, 32);

	cout << "encDataKey  = [" << encDataKey << "][" << strlen(encDataKey) << "]" << endl;

	//AES256
	AES_KEY enc_key, dec_key;
	AES_set_encrypt_key((unsigned char*)encDataKey, 256, &enc_key);

	char *pad_req = NULL;
	int paddedSize = 0;

	//paddedSize = PKCS5_Padding(encPw, sizeof(encPw), 16, &pad_req);
	paddedSize = PKCS5_Padding(encPw, pwReultLen, 16, &pad_req);

	unsigned char enc_out[80];
	unsigned char dec_out[80];
	unsigned char iv_enc[AES_BLOCK_SIZE], iv_dec[AES_BLOCK_SIZE];

	memset(enc_out, 0x00, sizeof(enc_out));
	memset(iv_enc, 0x00, AES_BLOCK_SIZE);
	memset(iv_dec, 0x00, AES_BLOCK_SIZE);

	AES_cbc_encrypt((unsigned char*)pad_req, enc_out, paddedSize, &enc_key, iv_enc, AES_ENCRYPT);

	//cout<<"enc_out ["<<enc_out<<"]\n"<<endl;
	cout << "enc_out [";

    for(int i =0 ; i<paddedSize ; i++)
    {
      //cout<<pad_req[i]<<endl;
      printf("%.02x ",enc_out[i]);
    }
    cout<<"]\n"<<endl;

	// Base64 Encoding
	unsigned char* encBase64;
	int encoding_num = 0;
	encBase64 = base64_encode((unsigned char *)enc_out, paddedSize, &encoding_num);

	cout << "base64 encode [" << encBase64 << "]" << endl;
	cout << "encoding_num [" << encoding_num << "]" << endl;

#if (5 <= DEBUG)
	unsigned char*decBase64;
	int decoding_num = 0;
	//decBase64 = base64_decode((unsigned char *)encBase64, strlen((char *)encBase64), &decoding_num);
	decBase64 = base64_decode((unsigned char *)encBase64, encoding_num, &decoding_num);

	//cout << "base64 decode [" << decBase64 << "]" << endl;
	cout << "decoding_num [" << decoding_num << "]" << endl;

	AES_set_decrypt_key((unsigned char*)encDataKey, 256, &dec_key);
	memset(dec_out, 0x00, sizeof(dec_out));
	//AES_cbc_encrypt(enc_out, dec_out, paddedSize, &dec_key, iv_dec, AES_DECRYPT);
	AES_cbc_encrypt(decBase64, dec_out, decoding_num, &dec_key, iv_dec, AES_DECRYPT);

	//cout<<"dec_out ["<<dec_out<<"]\n"<<endl;

    cout<<"dec_out ["<<endl;
    //for(int i =0 ; i<paddedSize ; i++)
	for (int i = 0; i < decoding_num; i++)
    {
      //cout<<pad_req[i]<<endl;
      printf("%.02x ",dec_out[i]);
    }
    cout<<"]\n"<<endl;
    
	int resSize = 0;
	char * pad_res = NULL;

    //resSize = PKCS5_UnPadding((char*)dec_out, paddedSize, 16, &pad_res);
	resSize = PKCS5_UnPadding((char *)dec_out, decoding_num, 16, &pad_res);
    
    //cout<<"pad_res ["<<pad_res<<"]\n"<<endl;
	printf("pad res [%.*s][%d]\n", resSize, pad_res, resSize);
    
     cout<<"pad_res ["<<endl;
    //for(int i =0 ; i<paddedSize ; i++)
	for(int i =0 ; i<resSize ; i++)
    {
      //cout<<pad_req[i]<<endl;
      printf("%.02x ",pad_res[i]);
    }
    cout<<"]\n"<<endl;
	free(pad_res);
	free(decBase64);
#endif

	sprintf(CertSnd.szPWD, "%.*s", encoding_num, encBase64);
	sprintf(encPw2,	CertSnd.szPWD);
	sprintf(CertSnd.szKeypos, offset);
	sprintf(CertSnd.szVersion, PROTOCOL_VER);

	cout << "CertSnd.szCID [" << CertSnd.szCID << "]" << endl;
	cout << "CertSnd.szPWD [" << CertSnd.szPWD << "]" << endl;
	cout << "CertSnd.szKeypos [" << CertSnd.szKeypos << "]" << endl;
	cout << "CertSnd.szVersion [" << CertSnd.szVersion << "]" << endl;
	
	//memcpy(pBuff, (char*)&BindSnd, sizeof(BIND_REQ));
	memcpy(pBuff, (char*)&CertSnd, sizeof(CERT_REQ));

	free(pad_req);
	free(encBase64);
	
	return sizeof(CERT_REQ);
}

//int CPacketCtrlSKY::getMsg_BIND_REQ(char* pBuff, char* id, char* pw, char* szTp, char* szKnd, char* offset, char* encPw2)
int CPacketCtrlSKY::getMsg_BIND_REQ(char* pBuff, char* id, char* pw, char* szTp, char* szKnd, char* offset, char *certKey)
{
	BIND_REQ BindSnd;
	//string encPw;
	char* encPw[20];
	
	//string strPw;
	
	//strPw = pw;
	
	int pwLen = 0;
	
	int pwReultLen = 0;
	
	//memset(encPw, 0x00, sizeof(41));
	memset((char*)&BindSnd, 0x00, sizeof(BIND_REQ));
	sprintf(BindSnd.header.headType, "%d",TYPE_BIND_REQ);
	//sprintf(BindSnd.header.msgLeng, "%d",106);
	sprintf(BindSnd.header.msgLeng, "%d", (int)(sizeof(BIND_REQ) - sizeof(HEADER)));
	sprintf(BindSnd.szCID, id);
	sprintf(BindSnd.szPWD, pw);
	sprintf(BindSnd.szCertKey, certKey);
	sprintf(BindSnd.szType, szTp);
	sprintf(BindSnd.szKind, szKnd);
	//sprintf(BindSnd.szVersion, "PE.1.0");
	sprintf(BindSnd.szVersion, PROTOCOL_VER);
	sprintf(BindSnd.szKeypos, offset);
	
	//pwLen = strlen(pw);
	
	//pwReultLen = SHA1_Hash(pw,pwLen,encPw);
	
	//sprintf(BindSnd.szPWD,*encPw);
	//sprintf(encPw2,BindSnd.szPWD);
	
	memcpy(pBuff, (char*)&BindSnd, sizeof(BIND_REQ));
	
	return sizeof(BIND_REQ);
}


int CPacketCtrlSKY::getMsg_DELIVER_REQ(char* pBuff, long long nMsgSeq, char* szExpiredTime, char* szSubject, map<string,string> &mapSend )
{
	bool isColor=false;
	int nImgCnt = 0;
	char szTemp[MAX_BUFF];
	char MsgData[MAX_BUFF];
	int sizeTrnsReq=0;
	int sizeTrnsMedia=0;
	int nCtnCnt = 0;     
	int nMediaCnt = 0;     
	char szYYYYMM[32];
	char szCtnFullPath[255+1];
	FILE* fp;    
	FILE* fp2;  
	FILE* fp3;       
	int	nReadSize = 0;
	struct stat s1;
	int	nImgSize1 = 0;
	int	nImgSize2 = 0;
	int	nImgSize3 = 0;
	
	int sizeTrnsMInfo = 0;
	int sizeTrnsMText = 0;
	int sizeTrnsMImg1 = 0;
	int sizeTrnsMImg2 = 0;
	int sizeTrnsMImg3 = 0;
	
	int returnLen = 0;
	
	char hexaData[2+1];
	//char totData[65535+1];
	string totData;
		
	
	memset(szTemp,0x00,sizeof(szTemp));
	memset(MsgData,0x00,sizeof(MsgData));
	memset(hexaData,0x00,sizeof(hexaData));
	//memset(totData,0x00,sizeof(totData));
	
	//TextMsg
	sprintf(szTemp, mapSend["txt_path"].c_str());
	
	if (GetLMSText(szTemp, MsgData, &isColor, nImgCnt) < 0)
	{
		sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() GetLMSText() ERROR MMS_ID : [%s]", mapSend["s_mms_id"].c_str());
		log2(tmpLog2, 0, 0);
		return -1;
	}
	
	/*if(nmSize > 2000)
	{
		sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() GetLMSText() ERROR OVER 2000 BYTE : [%s][%d]", mapSend["s_mms_id"].c_str(),nmSize);
		log2(tmpLog2, 0, 0);
		return -2;
		
	}*/
	
	nCtnCnt = atoi(mapSend["ctn_cnt"].c_str());
	
	//cout<<"ctn_cnt["<<mapSend["ctn_cnt"]<<"]\n"<<endl;
	
	memset(szYYYYMM, 0x00, sizeof(szYYYYMM)); //CCL(szYYYYMM);
	get_timestring("%04d%02d", time(NULL), szYYYYMM);
	trim(szYYYYMM, strlen(szYYYYMM));
	
	if(mapSend["ctn_name1"].length() > 0)
	{
		memset(szCtnFullPath,0x00,sizeof(szCtnFullPath));
	
		strcpy(szCtnFullPath, "/data/neomms/CNT/");                         
		strcat(szCtnFullPath, mapSend["ctn_name1"].c_str());
		
		fp = fopen(szCtnFullPath, "rb");  
		
		if(fp == NULL){
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE1[%s] open Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}
		
		if (fstat(fileno(fp), &s1) < 0) {
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE1[%s] fstat Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}
	
		
		//nReadSize = s1.st_size;
		fseek(fp, 0l, SEEK_END);
		nReadSize = ftell(fp);
	
		//데이터 구하기
		rewind(fp);
		
		this->m_ptrBuff1 = NULL;
		this->m_ptrBuff1 = (char*)malloc(nReadSize+1);
		
		memset(this->m_ptrBuff1, 0x00, nReadSize+1);
		
		int nReadByte = fread(this->m_ptrBuff1,  sizeof(char), nReadSize, fp);
		//int nReadByte = fread(this->m_ptrBuff1,  1, nReadSize, fp);
		
		if(nReadByte != nReadSize){
			if(this->m_ptrBuff1 != NULL) 	free(this->m_ptrBuff1);
			fclose(fp);
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE1[%s] Read Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}	
		
		nImgSize1 = nReadByte;
	}	
	
	if(mapSend["ctn_name2"].length() > 0)
	{
		memset(szCtnFullPath,0x00,sizeof(szCtnFullPath));
	
		strcpy(szCtnFullPath, "/data/neomms/CNT/");                         
		strcat(szCtnFullPath, mapSend["ctn_name2"].c_str());
		
		fp2 = fopen(szCtnFullPath, "rb");                     
		
		if(fp2 == NULL){
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE2[%s] open Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}
		
		if (fstat(fileno(fp2), &s1) < 0) {
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE2[%s] fstat Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}

		nReadSize = s1.st_size;
		this->m_ptrBuff2 = NULL;
		this->m_ptrBuff2 = (char*)malloc(nReadSize+1);
		
		memset(this->m_ptrBuff2, 0x00, nReadSize+1);
		
		int nReadByte = fread(this->m_ptrBuff2,  sizeof(char), nReadSize, fp2);
		
		if(nReadByte != nReadSize){
			if(this->m_ptrBuff2 != NULL) 	free(this->m_ptrBuff2);
			fclose(fp2);
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE2[%s] Read Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}	
		
		nImgSize2 = nReadByte;
	}
	
	if(mapSend["ctn_name3"].length() > 0)
	{
		memset(szCtnFullPath,0x00,sizeof(szCtnFullPath));
	
		strcpy(szCtnFullPath, "/data/neomms/CNT/");                         
		strcat(szCtnFullPath, mapSend["ctn_name3"].c_str());
		
		fp3 = fopen(szCtnFullPath, "rb");                     
		
		if(fp3 == NULL){
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE3[%s] open Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}
		
		if (fstat(fileno(fp3), &s1) < 0) {
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE3[%s] fstat Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}

		nReadSize = s1.st_size;
		this->m_ptrBuff3 = NULL;
		this->m_ptrBuff3 = (char*)malloc(nReadSize+1);
		
		memset(this->m_ptrBuff3, 0x00, nReadSize+1);
		
		int nReadByte = fread(this->m_ptrBuff3,  sizeof(char), nReadSize, fp3);
		
		if(nReadByte != nReadSize){
			if(this->m_ptrBuff3 != NULL) 	free(this->m_ptrBuff3);
			fclose(fp3);
			sprintf(tmpLog2, "CPacketCtrlSKY::getMsg_DELIVER_REQ() FILE3[%s] Read Error", szCtnFullPath);
			log2(tmpLog2, 0, 0);
			return -1;
		}	
		
		nImgSize3 = nReadByte;
	}
	
	//sizeTrnsMInfo = sizeof(char)*3+sizeof(char)*50+sizeof(char)*10;
	sizeTrnsMInfo = sizeof(char)*3+sizeof(char)*2+sizeof(char)*10;
	
	sizeTrnsMText = sizeof(char)*nmSize;
	
	sizeTrnsMImg1 = sizeof(char)*nImgSize1;
	
	sizeTrnsMImg2 = sizeof(char)*nImgSize2;
	
	sizeTrnsMImg3 = sizeof(char)*nImgSize3;
	
	DELIVER_REQ* TrnsReq = (DELIVER_REQ*)malloc(sizeof(DELIVER_REQ));
	
	memset(TrnsReq->header.headType,0x00,sizeof(TrnsReq->header.headType));
	memset(TrnsReq->header.msgLeng,0x00,sizeof(TrnsReq->header.msgLeng));
	memset(TrnsReq->szMsgType,0x00,sizeof(TrnsReq->szMsgType));
	memset(TrnsReq->szCallBack,0x00,sizeof(TrnsReq->szCallBack));
	memset(TrnsReq->szEncoding, 0x00, sizeof(TrnsReq->szEncoding));
	memset(TrnsReq->szSubject,0x00,sizeof(TrnsReq->szSubject));
	memset(TrnsReq->szSerial,0x00,sizeof(TrnsReq->szSerial));
	memset(TrnsReq->szSenderCode, 0x00, sizeof(TrnsReq->szSenderCode));
	//memset(TrnsReq->szExpiredTime,0x00,sizeof(TrnsReq->szExpiredTime));
	//memset(TrnsReq->szReport,0x00,sizeof(TrnsReq->szReport));
	//memset(TrnsReq->szDaCnt,0x00,sizeof(TrnsReq->szDaCnt));
	memset(TrnsReq->szDaAddr,0x00,sizeof(TrnsReq->szDaAddr));
	memset(TrnsReq->szMediaCnt,0x00,sizeof(TrnsReq->szMediaCnt));
	memset(TrnsReq->szExtSize, 0x00, sizeof(TrnsReq->szExtSize));
	
	sprintf(TrnsReq->header.headType, "%d", TYPE_DELIVER_REQ);
	
	
	if(nCtnCnt > 0)
	{
		//sprintf(TrnsReq->szMsgType,"2");
		sprintf(TrnsReq->szMsgType,"M");
	}else{
		//sprintf(TrnsReq->szMsgType,"0");
		sprintf(TrnsReq->szMsgType,"L");
	}
	
	strncpy(TrnsReq->szDaAddr, mapSend["dst_addr"].c_str(),sizeof(TrnsReq->szDaAddr));
	strncpy(TrnsReq->szCallBack, mapSend["callback"].c_str(),sizeof(TrnsReq->szCallBack));

	// 인코딩 타입 0:EUC-KR, 1: UTF-8
	sprintf(TrnsReq->szEncoding,"0");
	strncpy(TrnsReq->szSubject, szSubject, sizeof(TrnsReq->szSubject));
	sprintf(TrnsReq->szSerial,"%lld", nMsgSeq);
	// 식별코드 202309 add
	strncpy(TrnsReq->szSenderCode,	mapSend["id_code"].c_str(), sizeof(TrnsReq->szSenderCode));
	// 202309 delete
	//sprintf(TrnsReq->szExpiredTime,"%s",szExpiredTime);
	//sprintf(TrnsReq->szReport,"Y");
	//sprintf(TrnsReq->szDaCnt,"1");
	
	// 확장필드 크기
	sprintf(TrnsReq->szExtSize,"0");
	
	if(nCtnCnt > 0)
	{
		if(nmSize > 0)
		{
			nMediaCnt += 1;
		}
		
		nMediaCnt += nCtnCnt;
		
		sprintf(TrnsReq->szMediaCnt,"%d",nMediaCnt);
	}
	else
	{
		sprintf(TrnsReq->szMediaCnt,"1");
	}
	
	DELIVER_MEDIA* media[4];
	
	if(nmSize > 0 )
	{
		media[0] = (DELIVER_MEDIA*) malloc (sizeof(DELIVER_MEDIA)+sizeTrnsMText);
		
		memset(media[0],0x00,sizeof(DELIVER_MEDIA)+sizeTrnsMText);
		
		sprintf(media[0]->szMType,"11");
		sprintf(media[0]->szEncoding,"0");

		//snprintf(media[0]->szMFileName,sizeof(media[0]->szMFileName),"%s",szTemp);
		
		sprintf(media[0]->szMFileLen,"%ld",sizeof(char)*nmSize);
		
		memset(media[0]->szMedia,0x00,sizeof(char)*nmSize);
		strncpy(media[0]->szMedia, MsgData, sizeof(char)*nmSize);
		
		if(mapSend["ctn_name1"].length() > 0)
		{
			media[1] = (DELIVER_MEDIA*) malloc (sizeTrnsMInfo+sizeof(char)*nImgSize1);
			
			memset(media[1],0x00,sizeTrnsMInfo+sizeof(char)*nImgSize1);
			
			sprintf(media[1]->szMType,"21");
			sprintf(media[1]->szEncoding,"2");
		
			//snprintf(media[1]->szMFileName,sizeof(media[1]->szMFileName),"%s",mapSend["ctn_name1"].c_str());
			
			sprintf(media[1]->szMFileLen,"%d",sizeof(char)*nImgSize1);
		
			fclose(fp);
		}	
		
		if(mapSend["ctn_name2"].length() > 0)
		{
			media[2] = (DELIVER_MEDIA*) malloc (sizeTrnsMInfo+sizeTrnsMImg2);
			
			sprintf(media[2]->szMType,"21");
			sprintf(media[2]->szEncoding,"2");
		
			//snprintf(media[2]->szMFileName,sizeof(media[2]->szMFileName),"%s",mapSend["ctn_name2"].c_str());
			
			sprintf(media[2]->szMFileLen,"%d",sizeof(char)*nImgSize2);
			
			fclose(fp2);				
		}
		
		if(mapSend["ctn_name3"].length() > 0)
		{
			media[3] = (DELIVER_MEDIA*) malloc (sizeTrnsMInfo+sizeTrnsMImg3);
			
			sprintf(media[3]->szMType,"21");
			sprintf(media[3]->szEncoding,"2");
		
			//snprintf(media[3]->szMFileName,sizeof(media[3]->szMFileName),"%s",mapSend["ctn_name3"].c_str());
			
			sprintf(media[3]->szMFileLen,"%d",sizeof(char)*nImgSize3);
			
			fclose(fp3);			
		}
	}
	else
	{
		if(mapSend["ctn_name1"].length() > 0)
		{
			media[0] = (DELIVER_MEDIA*) malloc (sizeTrnsMInfo+sizeof(char)*nImgSize1);
			
			memset(media[0],0x00,sizeTrnsMInfo+sizeof(char)*nImgSize1);
			
			sprintf(media[0]->szMType,"21");
			sprintf(media[0]->szEncoding,"2");
		
			//snprintf(media[0]->szMFileName,sizeof(media[1]->szMFileName),"%s",mapSend["ctn_name1"].c_str());
			
			sprintf(media[0]->szMFileLen,"%d",sizeof(char)*nImgSize1);
			
			fclose(fp);
		}	
		
		if(mapSend["ctn_name2"].length() > 0)
		{
			media[1] = (DELIVER_MEDIA*) malloc (sizeTrnsMInfo+sizeTrnsMImg2);
			
			sprintf(media[1]->szMType,"21");
			sprintf(media[1]->szEncoding,"2");
		
			//snprintf(media[1]->szMFileName,sizeof(media[2]->szMFileName),"%s",mapSend["ctn_name2"].c_str());
			
			sprintf(media[1]->szMFileLen,"%d",sizeof(char)*nImgSize2);
			
			fclose(fp2);				
		}
		
		if(mapSend["ctn_name3"].length() > 0)
		{
			media[2] = (DELIVER_MEDIA*) malloc (sizeTrnsMInfo+sizeTrnsMImg3);
			
			sprintf(media[2]->szMType,"21");
			sprintf(media[2]->szEncoding,"2");
		
			//snprintf(media[2]->szMFileName,sizeof(media[3]->szMFileName),"%s",mapSend["ctn_name3"].c_str());
			
			sprintf(media[2]->szMFileLen,"%d",sizeof(char)*nImgSize3);
			
			fclose(fp3);			
		}	
	}
	
	if(nmSize > 0 && mapSend["ctn_name1"].length() == 0 && mapSend["ctn_name2"].length() == 0 && mapSend["ctn_name3"].length() == 0 )
	{
		sprintf(TrnsReq->header.msgLeng, "%d",sizeof(DELIVER_REQ)+sizeTrnsMInfo+sizeTrnsMText-sizeof(HEADER));
		
	}
	else if(nmSize > 0 && mapSend["ctn_name1"].length() > 0 && mapSend["ctn_name2"].length() == 0 && mapSend["ctn_name3"].length() == 0 )
	{
		sprintf(TrnsReq->header.msgLeng, "%d",sizeof(DELIVER_REQ)+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1-sizeof(HEADER));
		
	}
	else if(nmSize > 0 && mapSend["ctn_name1"].length() > 0 && mapSend["ctn_name2"].length() > 0 && mapSend["ctn_name3"].length() == 0 )
	{
		sprintf(TrnsReq->header.msgLeng, "%d",sizeof(DELIVER_REQ)+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2-sizeof(HEADER));
		
	}
	else if(nmSize > 0 && mapSend["ctn_name1"].length() > 0 && mapSend["ctn_name2"].length() > 0 && mapSend["ctn_name3"].length() > 0 )
	{
		sprintf(TrnsReq->header.msgLeng, "%d",sizeof(DELIVER_REQ)+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeTrnsMInfo+sizeTrnsMImg3-sizeof(HEADER));
	}
	else if(nmSize <= 0 && mapSend["ctn_name1"].length() > 0 && mapSend["ctn_name2"].length() == 0 && mapSend["ctn_name3"].length() == 0 )
	{
		sprintf(TrnsReq->header.msgLeng, "%d",sizeof(DELIVER_REQ)+sizeTrnsMInfo+sizeTrnsMImg1-sizeof(HEADER));
		
	}
	else if(nmSize <= 0 && mapSend["ctn_name1"].length() > 0 && mapSend["ctn_name2"].length() > 0 && mapSend["ctn_name3"].length() == 0 )
	{
		sprintf(TrnsReq->header.msgLeng, "%d",sizeof(DELIVER_REQ)+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2-sizeof(HEADER));
		
	}
	else if(nmSize <= 0 && mapSend["ctn_name1"].length() > 0 && mapSend["ctn_name2"].length() > 0 && mapSend["ctn_name3"].length() > 0 )
	{
		sprintf(TrnsReq->header.msgLeng, "%d",sizeof(DELIVER_REQ)+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeTrnsMInfo+sizeTrnsMImg3-sizeof(HEADER));
	}
	
	sizeTrnsReq = sizeof(DELIVER_REQ);	
	
	if(nmSize > 0 )
	{
		sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nmSize;	
		
		if(mapSend["ctn_name1"].length() > 0)	
		{
			sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nImgSize1;	
		}
		
		if(mapSend["ctn_name2"].length() > 0)	
		{
			sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nImgSize2;	
		}
		
		if(mapSend["ctn_name3"].length() > 0)	
		{
			sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nImgSize3;	
		}
	}
	else
	{
		sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nmSize;	
		
		if(mapSend["ctn_name1"].length() > 0)	
		{
			sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nImgSize1;	
		}
		
		if(mapSend["ctn_name2"].length() > 0)	
		{
			sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nImgSize2;	
		}
		
		if(mapSend["ctn_name3"].length() > 0)	
		{
			sizeTrnsMedia += sizeTrnsMInfo+sizeof(char)*nImgSize3;	
		}	
	}	

	memcpy(pBuff, TrnsReq, sizeTrnsReq);
	
	if(nmSize > 0 )
	{
		memcpy(pBuff+sizeTrnsReq,media[0]->szMType,sizeof(char)*3);
		//memcpy(pBuff+sizeTrnsReq+sizeof(char)*3,media[0]->szMFileName,sizeof(char)*50);
		memcpy(pBuff+sizeTrnsReq+sizeof(char)*3,media[0]->szEncoding, sizeof(char)*2);
		memcpy(pBuff+sizeTrnsReq+sizeof(char)*3+sizeof(char)*2,media[0]->szMFileLen,sizeof(char)*10);
		memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo,media[0]->szMedia,sizeTrnsMText);
		
		free(media[0]);
		
		if(mapSend["ctn_name1"].length() > 0)	
		{	
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText,media[1]->szMType,sizeof(char)*3);
			//memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeof(char)*3,media[1]->szMFileName,sizeof(char)*50);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeof(char)*3,media[1]->szEncoding,sizeof(char)*2);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeof(char)*3+sizeof(char)*2,media[1]->szMFileLen,sizeof(char)*10);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo,this->m_ptrBuff1,sizeTrnsMImg1);
	
			if(this->m_ptrBuff1 != NULL) 	free(this->m_ptrBuff1);	
			
			free(media[1]);
		}
		
		if(mapSend["ctn_name2"].length() > 0)
		{
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1,media[2]->szMType,sizeof(char)*3);
			//memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeof(char)*3,media[2]->szMFileName,sizeof(char)*50);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeof(char)*3,media[2]->szEncoding,sizeof(char)*2);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeof(char)*3+sizeof(char)*2,media[2]->szMFileLen,sizeof(char)*10);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo,this->m_ptrBuff2,sizeTrnsMImg2);
			
			if(this->m_ptrBuff2 != NULL) 	free(this->m_ptrBuff2);
			free(media[2]);
		}
		
		if(mapSend["ctn_name3"].length() > 0)
		{
			
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2,media[3]->szMType,sizeof(char)*3);
			//memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeof(char)*3,media[3]->szMFileName,sizeof(char)*50);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeof(char)*3,media[3]->szEncoding,sizeof(char)*2);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeof(char)*3+sizeof(char)*2,media[3]->szMFileLen,sizeof(char)*10);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeTrnsMInfo,this->m_ptrBuff3,sizeTrnsMImg3);
			
			if(this->m_ptrBuff3 != NULL) 	free(this->m_ptrBuff3);
			free(media[3]);
		}
	}
	else
	{
		if(mapSend["ctn_name1"].length() > 0)	
		{	
			memcpy(pBuff+sizeTrnsReq,media[0]->szMType,sizeof(char)*3);
			//memcpy(pBuff+sizeTrnsReq+sizeof(char)*3,media[0]->szMFileName,sizeof(char)*50);
			memcpy(pBuff+sizeTrnsReq+sizeof(char)*3,media[0]->szEncoding,sizeof(char)*2);
			memcpy(pBuff+sizeTrnsReq+sizeof(char)*3+sizeof(char)*2,media[0]->szMFileLen,sizeof(char)*10);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo,this->m_ptrBuff1,sizeTrnsMImg1);
			
			if(this->m_ptrBuff1 != NULL) 	free(this->m_ptrBuff1);
			free(media[0]);
		}
		
		if(mapSend["ctn_name2"].length() > 0)
		{
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1,media[1]->szMType,sizeof(char)*3);
			//memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeof(char)*3,media[1]->szMFileName,sizeof(char)*50);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeof(char)*3,media[1]->szEncoding,sizeof(char)*2);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeof(char)*3+sizeof(char)*2,media[1]->szMFileLen,sizeof(char)*10);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo,this->m_ptrBuff2,sizeTrnsMImg2);
			
			if(this->m_ptrBuff2 != NULL) 	free(this->m_ptrBuff2);
			free(media[1]);
		}
		
		if(mapSend["ctn_name3"].length() > 0)
		{
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2,media[2]->szMType,sizeof(char)*3);
			//memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeof(char)*3,media[2]->szMFileName,sizeof(char)*50);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeof(char)*3,media[2]->szEncoding,sizeof(char)*2);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeof(char)*3+sizeof(char)*2,media[2]->szMFileLen,sizeof(char)*10);
			memcpy(pBuff+sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1+sizeTrnsMInfo+sizeTrnsMImg2+sizeTrnsMInfo,this->m_ptrBuff3,sizeTrnsMImg3);
			
			if(this->m_ptrBuff3 != NULL) 	free(this->m_ptrBuff3);
			free(media[2]);
		}
	}		
	
	
	free(TrnsReq);
	
	if(nmSize > 0 )
	{
		returnLen += sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMText;
		if(mapSend["ctn_name1"].length() > 0)	
		{
			returnLen += sizeTrnsMInfo+sizeTrnsMImg1;
		}
		if(mapSend["ctn_name2"].length() > 0)	
		{
			returnLen += sizeTrnsMInfo+sizeTrnsMImg2;
		}
		if(mapSend["ctn_name3"].length() > 0)	
		{
			returnLen += sizeTrnsMInfo+sizeTrnsMImg3;
		}
	}
	else
	{
		if(mapSend["ctn_name1"].length() > 0)	
		{
			returnLen += sizeTrnsReq+sizeTrnsMInfo+sizeTrnsMImg1;
		}
		if(mapSend["ctn_name2"].length() > 0)	
		{
			returnLen += sizeTrnsMInfo+sizeTrnsMImg2;
		}
		if(mapSend["ctn_name3"].length() > 0)	
		{
			returnLen += sizeTrnsMInfo+sizeTrnsMImg3;
		}	
	}
	
	return returnLen;
}

int CPacketCtrlSKY::getMsg_PING_REQ(char* pBuff)
{
	
	LINK_CHK PingSnd;
	memset(&PingSnd, 0x00, sizeof(LINK_CHK));
	sprintf(PingSnd.header.headType, "%d",TYPE_PING);
	sprintf(PingSnd.header.msgLeng, "%d",0);
	
	memcpy(pBuff, (char*)&PingSnd, sizeof(LINK_CHK));
	
	return sizeof(LINK_CHK);
}

int CPacketCtrlSKY::getMsg_PING_RES(char* pBuff)
{
	LINK_CHK_ACK PingAck;
	memset(&PingAck, 0x00, sizeof(LINK_CHK_ACK));
	sprintf(PingAck.header.headType, "%d",TYPE_PONG);
	sprintf(PingAck.header.msgLeng, "%d",0);
	
	memcpy(pBuff, (char*)&PingAck, sizeof(LINK_CHK_ACK));
	return sizeof(LINK_CHK_ACK);
}

int CPacketCtrlSKY::getMsg_REPORT_ACK(char* pBuff, char* result, char* serial, char* gwSerial)
{
	REPORT_ACK TrnsRepAck;
	memset(&TrnsRepAck, 0x00, sizeof(REPORT_ACK));

  	sprintf(TrnsRepAck.header.headType, "%d",TYPE_REPORT_ACK);
	sprintf(TrnsRepAck.header.msgLeng, "%d",40);
	
	//TrnsRepAck.nResult = htonl(0);
	sprintf(TrnsRepAck.szResult,"%s",result);
	sprintf(TrnsRepAck.szSerial,"%s",serial);
	sprintf(TrnsRepAck.szGwSerial,"%s",gwSerial);
//	TrnsRepAck.nJobId = htonl(atoi(sJobId.c_str()));
//	TrnsRepAck.nSubJobId = htonl(0);
	
	memcpy(pBuff, (char*)&TrnsRepAck, sizeof(REPORT_ACK));

	return sizeof(REPORT_ACK);
}


int CPacketCtrlSKY::getData_BndAck(char* pBuff, vector<string>& vtBndAck)
{
	BIND_ACK *pBndAck = (BIND_ACK*)pBuff;
	char szTemp[89];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pBndAck->szResult);
		vtBndAck.push_back(szTemp);
		
		//memset(szTemp, 0x00, sizeof(szTemp));
		//sprintf(szTemp, "%s", pBndAck->szRersec);
		//vtBndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pBndAck->szSmsTps);
		vtBndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pBndAck->szMmsTps);
		vtBndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pBndAck->szKkoTps);
		vtBndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pBndAck->szMessage);
		vtBndAck.push_back(szTemp);	
	}
	
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKY::getData_CertAck(char* pBuff, vector<string>& vtCertAck)
{
	CERT_ACK *pCertAck = (CERT_ACK*)pBuff;

	char szTemp[80];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pCertAck->szResult);
		vtCertAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pCertAck->szCertKey);
		vtCertAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pCertAck->szServerIP);
		vtCertAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pCertAck->szDeliverPort);
		vtCertAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pCertAck->szReportPort);
		vtCertAck.push_back(szTemp);
	}
	
	catch (...) {
		return -1;
	}

	return 1;
}


int CPacketCtrlSKY::getData_SndAck(char* pBuff, vector<string>& vtSndAck)
{
	DELIVER_ACK *pSndAck = (DELIVER_ACK*)pBuff;
	char szTemp[20];
	try {
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pSndAck->szResult);
		vtSndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pSndAck->szDaAddr);
		vtSndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pSndAck->szSerial);
		vtSndAck.push_back(szTemp);
		
		
//		memset(szTemp, 0x00, sizeof(szTemp));
//		sprintf(szTemp, "%d", ntohl(pSndAck->nJobId));
//		vtSndAck.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	/* >>>> pSndAck Cout Start <<<<*/
//	cout << "============================="<<endl;
//	cout << ">>>> pSndAck.nSN     : " << ntohl(pSndAck->nSN) << " <<<<" << endl;
//	cout << ">>>> pSndAck.nResult : " << ntohl(pSndAck->nResult) << " <<<<" << endl;
//	cout << ">>>> pSndAck size    :" << sizeof(*pSndAck) << " <<<<" << endl;
//	cout << "============================="<<endl;
	/* >>>> pSndAck Cout End <<<<*/

	return 1;
}

int CPacketCtrlSKY::getData_SndAck_ENC(char* pBuff, char* encKey ,vector<string>& vtSndAck)
{
	cout<<"getData_SndAck_ENC START\n"<<endl;
	DELIVER_ACK_ENC *pSndAck = (DELIVER_ACK_ENC*)pBuff;
	
	int resSize = 0;
	char szTemp[20];
	char szTempData[48];
	char *unpad_SndAck;
	unsigned char dec_out[44];
	unsigned char iv_dec[AES_BLOCK_SIZE];
    //RAND_bytes(iv_enc, AES_BLOCK_SIZE);
    //memcpy(iv_dec, iv_enc, AES_BLOCK_SIZE);
    
	try {
		
		/*cout<<"encKey["<<encKey<<"]\n"<<endl;
		cout<<"pSndAck pBuff["<<pBuff<<"]\n"<<endl;
		cout<<"pSndAck pBuff["<<strlen(pBuff)<<"]\n"<<endl;
		cout<<"pSndAck pBuff["<<sizeof(pBuff)<<"]\n"<<endl;*/
		memset(&unpad_SndAck, 0x00, sizeof(&unpad_SndAck));
		memset(dec_out, 0x00, sizeof(dec_out));
		memset(iv_dec, 0x00, sizeof(iv_dec));
       
    	
    AES_KEY dec_key;
    	
    //printf("pSndAck1 [%s][%d]",pSndAck->szData,strlen(pSndAck->szData));
		
		memset(szTempData, 0x00, sizeof(szTempData));
		sprintf(szTempData, pSndAck->szData);
		
		cout<<"pSndAck1["<<pSndAck->header.headType<<"]\n"<<endl;
		cout<<"pSndAck1-1["<<pSndAck->header.msgLeng<<"]\n"<<endl;
		
		//inputslength = strlen(szTempData);
		
		//cout<<"pSndAck1["<<inputslength<<"]\n"<<endl;
		
		AES_set_decrypt_key((unsigned char*)encKey, 128, &dec_key);
  	//AES_cbc_encrypt(enc_out, dec_out, inputslength, &dec_key, iv_dec, AES_DECRYPT);
  
  	AES_cbc_encrypt((unsigned char*)pSndAck->szData, dec_out, 48, &dec_key, iv_dec, AES_DECRYPT);
		
		resSize = PKCS5_UnPadding((char*)dec_out, 48, 16, &unpad_SndAck);
     
   //cout<<"pSndAck1-2["<<*unpad_SndAck<<"]\n"<<endl;
		
		DELIVER_ACK_BODY *pSndAckbody = (DELIVER_ACK_BODY*)unpad_SndAck;
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pSndAckbody->szResult);
		vtSndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pSndAckbody->szDaAddr);
		if(szTemp != NULL)
		vtSndAck.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pSndAckbody->szSerial);
		if(szTemp != NULL)
		vtSndAck.push_back(szTemp);
		
		//free(unpad_SndAck);
//		memset(szTemp, 0x00, sizeof(szTemp));
//		sprintf(szTemp, "%d", ntohl(pSndAck->nJobId));
//		vtSndAck.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	/* >>>> pSndAck Cout Start <<<<*/
//	cout << "============================="<<endl;
//	cout << ">>>> pSndAck.nSN     : " << ntohl(pSndAck->nSN) << " <<<<" << endl;
//	cout << ">>>> pSndAck.nResult : " << ntohl(pSndAck->nResult) << " <<<<" << endl;
//	cout << ">>>> pSndAck size    :" << sizeof(*pSndAck) << " <<<<" << endl;
//	cout << "============================="<<endl;
	/* >>>> pSndAck Cout End <<<<*/

	return 1;
}

int CPacketCtrlSKY::getData_Report(char* pBuff, vector<string>& vtReport)
{
	REPORT *pReport = (REPORT*)pBuff;
	/* Get LocalTime Declare start */
	struct tm *t;
	time_t timer;
	timer = time(NULL);    // 현재 시각을 초 단위로 얻기
	t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기
	/* Get LocalTime Declare end */
	
	char szTemp[32];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pReport->szResult);
		vtReport.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReport->szDaAddr, sizeof(pReport->szDaAddr));
		vtReport.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReport->szSerial, sizeof(pReport->szSerial));
		vtReport.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReport->szGwSerial, sizeof(pReport->szGwSerial));
		vtReport.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReport->szSendTime, sizeof(pReport->szSendTime));
		vtReport.push_back(szTemp);
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, pReport->szTelcoInfo, sizeof(pReport->szTelcoInfo));
		vtReport.push_back(szTemp);
		
		// 결과 메시지 2308 add
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReport->szMessage, sizeof(pReport->szMessage));
		vtReport.push_back(szTemp);
		
		/*
		cout << " >>>> ************************** <<<< " << endl;
		cout << " >>>> pReport->nResult      : " << pReport->nResult      << " <<<<" << endl;
		cout << " >>>> pReport->szOrgAddr    : " << pReport->szOrgAddr    << " <<<<" << endl;
		cout << " >>>> pReport->szDstAddr    : " << pReport->szDstAddr    << " <<<<" << endl;
		cout << " >>>> pReport->nSN          : " << pReport->nSN          << " <<<<" << endl;
		cout << " >>>> pReport->nDeliverTime : " << pReport->nDeliverTime << " <<<<" << endl;
		cout << " >>>> pReport->szMoblieComp : " << pReport->szMobileComp << " <<<<" << endl;
		cout << " >>>> ************************** <<<< " << endl;
		*/
		
//		memset(szTemp, 0x00, sizeof(szTemp));
//		sprintf(szTemp, "%d", ntohl(pReport->nJobId));
//		vtReport.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKY::getData_Report_ENC(char* pBuff, char* encKey, vector<string>& vtReport)
{
	REPORT_ENC *pReport = (REPORT_ENC*)pBuff;
	/* Get LocalTime Declare start */
	struct tm *t;
	time_t timer;
	timer = time(NULL);    // 현재 시각을 초 단위로 얻기
	t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기
	/* Get LocalTime Declare end */
	
	int resSize = 0;
	char szTemp[32];
	char szTempData[80];
	char *unpad_report;
	unsigned char dec_out[79];
	unsigned char iv_dec[AES_BLOCK_SIZE];
	try {
		cout<<"encKey["<<encKey<<"]\n"<<endl;
		cout<<"pReport pBuff["<<pBuff<<"]\n"<<endl;
		cout<<"pReport pBuff["<<strlen(pBuff)<<"]\n"<<endl;
		cout<<"pReport pBuff["<<sizeof(pBuff)<<"]\n"<<endl;
		memset(&unpad_report, 0x00, sizeof(&unpad_report));
		memset(dec_out, 0x00, sizeof(dec_out));
		memset(iv_dec, 0x00, sizeof(iv_dec));
       
    	
    AES_KEY dec_key;
    	
  	
		memset(szTempData, 0x00, sizeof(szTempData));
		sprintf(szTempData, pReport->szData);
		
		cout<<"pReport["<<pReport->header.headType<<"]\n"<<endl;
		cout<<"pReport1-1["<<pReport->header.msgLeng<<"]\n"<<endl;
	
		//inputslength = strlen(szTempData);
		
		//cout<<"pSndAck1["<<inputslength<<"]\n"<<endl;
		
		AES_set_decrypt_key((unsigned char*)encKey, 128, &dec_key);
  	//AES_cbc_encrypt(enc_out, dec_out, inputslength, &dec_key, iv_dec, AES_DECRYPT);
  	
  	cout<<"pReport2\n"<<endl;
  
  	AES_cbc_encrypt((unsigned char*)pReport->szData, dec_out, 80, &dec_key, iv_dec, AES_DECRYPT);
  	
  	cout<<"pReport3\n"<<endl;
  	
  	resSize = PKCS5_UnPadding((char*)dec_out, 80, 16, &unpad_report);
  	
  	cout<<"pReport4 ["<<dec_out<<"\n"<<endl;
		 
    REPORT_BODY *pReportbody = (REPORT_BODY*)unpad_report;
		
			cout<<"pReport5\n"<<endl;
			
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, "%s", pReportbody->szResult);
		vtReport.push_back(szTemp);
		
		cout<<"pReport szResult["<<szTemp<<"]\n"<<endl;
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReportbody->szDaAddr, sizeof(pReportbody->szDaAddr));
		vtReport.push_back(szTemp);
		
		cout<<"pReport szDaAddr["<<szTemp<<"]\n"<<endl;
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReportbody->szSerial, sizeof(pReportbody->szSerial));
		vtReport.push_back(szTemp);
		
		cout<<"pReport szSerial["<<szTemp<<"]\n"<<endl;
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReportbody->szGwSerial, sizeof(pReportbody->szGwSerial));
		vtReport.push_back(szTemp);
		
		cout<<"pReport szGwSerial["<<szTemp<<"]\n"<<endl;
		
		memset(szTemp, 0x00, sizeof(szTemp));
		memcpy(szTemp, pReportbody->szSendTime, sizeof(pReportbody->szSendTime));
		vtReport.push_back(szTemp);
		
		cout<<"pReport szSendTime["<<szTemp<<"]\n"<<endl;
		
		memset(szTemp, 0x00, sizeof(szTemp));
		sprintf(szTemp, pReportbody->szTelcoInfo, sizeof(pReportbody->szTelcoInfo));
		vtReport.push_back(szTemp);
		
		cout<<"pReport szTelcoInfo["<<szTemp<<"]\n"<<endl;
		
		//free(unpad_report);
		/*
		cout << " >>>> ************************** <<<< " << endl;
		cout << " >>>> pReport->nResult      : " << pReport->nResult      << " <<<<" << endl;
		cout << " >>>> pReport->szOrgAddr    : " << pReport->szOrgAddr    << " <<<<" << endl;
		cout << " >>>> pReport->szDstAddr    : " << pReport->szDstAddr    << " <<<<" << endl;
		cout << " >>>> pReport->nSN          : " << pReport->nSN          << " <<<<" << endl;
		cout << " >>>> pReport->nDeliverTime : " << pReport->nDeliverTime << " <<<<" << endl;
		cout << " >>>> pReport->szMoblieComp : " << pReport->szMobileComp << " <<<<" << endl;
		cout << " >>>> ************************** <<<< " << endl;
		*/
		
//		memset(szTemp, 0x00, sizeof(szTemp));
//		sprintf(szTemp, "%d", ntohl(pReport->nJobId));
//		vtReport.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKY::getMsgCode(char* pBuff)
{
	HEADER *head = (HEADER*)pBuff;
	int nType = -1;
	try {
	//	nType = atoi(head->headType);
	//	nType = head->headType;
	    nType = atoi(head->headType);
	}
	catch (...) {
		nType = -1;
	}	
	return nType;
}

int CPacketCtrlSKY::getMsgEncKey(char* pBuff,char *id, char *pw, int nCntCode)
{
	char* encPw[5];
	char encKeyBuff1[40];
	char encKeyBuff2[40];
	char loginKey[strlen(id)+strlen(pw)]; //로그인 정보
	int nXor = 0; //xor용 for문용 변수
	
	char dataKey[40]; //로그인 정보
	int pwReultLen = 0;
	
	//char* encPw = (char*)malloc(sizeof(char));
	
	memset(encKeyBuff1, 0x73, sizeof(encKeyBuff1));
	memset(encKeyBuff2, 0x07, sizeof(encKeyBuff2));
	memset(loginKey, 0x07, sizeof(loginKey));
	
	memset(dataKey, 0x00, sizeof(dataKey));
	
	strcpy(loginKey,pw) ;
	strcat(loginKey,id) ; 
	
	strncpy(encKeyBuff2,loginKey,sizeof(loginKey));
	
	
	for(nXor = 0; nXor < sizeof(dataKey) ; nXor++ )
	{
		dataKey[nXor] = (char)(encKeyBuff1[nXor]^encKeyBuff2[nXor]);
	}
	
	pwReultLen = SHA1_Hash(dataKey,sizeof(dataKey),encPw);
	
	//cout << "getMsgEncKey encPw ["<<*encPw <<"]\n"<< endl;
	
	
	memcpy(pBuff, *encPw, sizeof(encPw));
	
	
	return pwReultLen;
}

int CPacketCtrlSKY::GetLMSText(const char *TxtPath, char* TxtData, bool *isColor, int nImgCnt)
{
	// @brief < LMS 내용 텍스트 구하기
	FILE *fp;

	size_t result;
	bool isLMS=false;

	long nSize=0;

	char *strContent = (char*)NULL;
	char strColorContent[MAX_BUFF];
	char DstData[MAX_BUFF];
	char strCtnPath[256];
	char strCtnPath_Loc[256];

	if (strcmp(TxtPath, "") == 0)
	{
		sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() TxtPath NOT FOUND ERROR Path : [%s], type : [TXT]", TxtPath);
		log2(tmpLog, 0, 0);
		nmSize = 0; //20210604추가
		return 0;
	}

	//컨텐츠 데이터 작업
	//텍스트 파일 컨텐츠

	char tmpPath[64];
	char tmpPath_Loc[64];
	memset(tmpPath    ,0x00,sizeof(tmpPath    ));
	memset(tmpPath_Loc,0x00,sizeof(tmpPath_Loc));
	strcpy(tmpPath    , TxtPath);
	strcpy(tmpPath_Loc, TxtPath);
	strncpy (tmpPath    ,"TXT_SSN"    ,7);
	sprintf (tmpPath_Loc,"TXT_SSN_LOC%s", &TxtPath[7]);
	sprintf(strCtnPath    , "/data/neomms/CNT/%s",tmpPath    );	// TXT_PATH
	sprintf(strCtnPath_Loc, "/data/neomms/CNT/%s",tmpPath_Loc);	// TXT_LOCPATH

	//파일 사이즈 구하기
	fp = fopen(strCtnPath, "rb");
	if (fp == NULL)
	{
		sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() FileOpen ERROR Path : [%s], type : [TXT]", strCtnPath);
		log2(tmpLog, 0, 0);
		
		fp = fopen(strCtnPath_Loc, "rb");
		if (fp == NULL)
		{
			sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() FileOpen ERROR Path : [%s], type : [TXT]", strCtnPath_Loc);
			log2(tmpLog, 0, 0);
			//return 0;
			// 변경: 두 경로 모두 실패 시 -1 반환
			nmSize = 0;
			return -1;	
		}
		sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() FileOpen SUCC Path : [%s], type : [TXT]", strCtnPath_Loc);
		log2(tmpLog, 0, 0);
	}
	fseek(fp, 0l, SEEK_END);
	nSize = ftell(fp);
	
	
	//데이터 구하기
	rewind(fp);
	strContent = (char*)malloc(sizeof(char)*nSize+1);
	memset(strContent, 0x00, sizeof(char)*(int)nSize+1);
	result = fread(strContent, sizeof(char), nSize, fp);
	strContent[nSize]='\0';
	fclose(fp);
	if (result != nSize)
	{
		if (strContent) free(strContent);
		sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() TXT ERROR[reading file: %s]", strCtnPath);
		log2(tmpLog, 0, 0);
		//return 0;
		// 변경: 읽기 실패 시 -1
		nmSize = 0;
		return -1;
	}

	memcpy(TxtData, strContent, strlen(strContent));
		
	nmSize = nSize;
		
	if (strContent) free(strContent);

	return 0;
}

char* CPacketCtrlSKY::trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

/*
 * 시간 문자열을 출력한다.
 */
void CPacketCtrlSKY::get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
}

int SHA1_Hash(char* data, int data_size, char** result)
{
	SHA_CTX sha;
	unsigned char shaResult[SHA_DIGEST_LENGTH];         // SHA_DIGEST_LENGTH = 20
	int nResLen = (SHA_DIGEST_LENGTH * 2) + 1;
	int loop = 0;
	
	*result = (char*)malloc(nResLen);
	
	memset(*result, 0x00, nResLen);
	
	SHA1_Init(&sha);
	SHA1_Update(&sha, data, data_size);
	SHA1_Final((unsigned char*)&shaResult ,&sha);
	
	//cout<<"shaResult["<<endl;
	for(loop = 0; loop < SHA_DIGEST_LENGTH; loop++)
	{
		sprintf(*result+(loop*2), "%02x", (unsigned char)shaResult[loop]);
		//cout<<(unsigned char)shaResult[loop]<<endl;
	}
	//cout<<"]\n"<<endl;
	
	
	return (SHA_DIGEST_LENGTH * 2);
	
	return 0;
}

//int SHA256_Hash(char* data, int data_size, char** result)
int SHA256_Hash(char* data, int data_size, char* result)
{
	//SHA_CTX sha;
	SHA256_CTX sha256;
	//unsigned char shaResult[SHA_DIGEST_LENGTH];         // SHA_DIGEST_LENGTH = 20
	unsigned char shaResult[SHA256_DIGEST_LENGTH];         // SHA256_DIGEST_LENGTH = 32
	int nResLen = (SHA256_DIGEST_LENGTH * 2) + 1;
	//int nResLen = (SHA_DIGEST_LENGTH * 2) + 1;
	int loop = 0;
	
	//*result = (char*)malloc(nResLen);
	
	memset(result, 0x00, nResLen);
	
	SHA256_Init(&sha256);
	SHA256_Update(&sha256, data, data_size);
	SHA256_Final((unsigned char*)&shaResult ,&sha256);
	
	cout<<"shaResult["<<endl;
	//cout << std::showbase << endl;
	cout << std::hex;
	for(loop = 0; loop < SHA256_DIGEST_LENGTH; loop++)
	{
		//sprintf(*result+(loop*2), "%02x", (unsigned char)shaResult[loop]);
		sprintf(result+(loop*2), "%02x", (unsigned char)shaResult[loop]);
		//cout<< std::hex << (unsigned int)shaResult[loop] ;
		cout<< (unsigned int)shaResult[loop] ;
	}
	cout<<"]\n"<<endl;
	cout << std::dec;
	//cout << std::noshowbase << endl;
	cout << "sha256 [" << result << "] [" << strlen(result) << "]" << endl;
	
	return (SHA256_DIGEST_LENGTH * 2);
}


int PKCS5_Padding(char *source, int size, int block_size, char **dest)
{
		
    int source_len = size;
    int dest_len = (source_len / block_size + 1) * block_size;
    int pad = dest_len - source_len;

		
    if(*dest != NULL)
    {
        free(*dest);
        *dest = NULL;
    }
    *dest = (char *)malloc(dest_len);
    memcpy(*dest, source, source_len);
    memset(*dest+source_len, pad, pad);
		
    return dest_len;
}

int PKCS5_UnPadding(char* src, int size, int block_size, char** result)
{
    int unpad = 0;

    if(size < block_size)
    {
    	*result = NULL;
    	return 0;
    }

    unpad = src[size-1];

    if((unpad < 1) || (unpad > block_size))
    {
    	*result = NULL;
    	return 0;
    }
    else
    {
      *result = (char*)malloc(size-unpad);
      memcpy(*result, src, size-unpad);
      return size-unpad;
    }
    return 0;
}

static void hex_print(const void* pv, size_t len)
{
    const unsigned char * p = (const unsigned char*)pv;
    if (NULL == pv)
        printf("NULL");
    else
    {
        size_t i = 0;
        for (; i<len;++i)
            printf("%02X ", *p++);
    }
    printf("\n");
}

void log2(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log2 ERROR. %d %s\n", "PacketCtrlKTC_MMS", 0, "");
	}
}
