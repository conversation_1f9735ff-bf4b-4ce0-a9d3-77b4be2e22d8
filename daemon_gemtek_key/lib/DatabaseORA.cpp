/*
 * DatabaseORA.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA.h"
#include <sqlca.h>
#include <iostream>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <time.h>
using namespace std;

char tmpLog3[1024];
void log3(char *buf, int st, int err);

namespace KSKYB
{
int CDatabaseORA::setEnableThreads()
{
	m_bThread = true;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ENABLE THREADS;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::setEnableThreads() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT ALLOCATE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::initThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	pCtx = ctx;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT FREE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::freeThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char szConnInf[20+1], szConnDsn[20+1];
	EXEC SQL END DECLARE SECTION;

	if ((szUID == NULL) || (szDSN == NULL)) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[szUID or szDSN is NULL]");
		log3(tmpLog3, 0, 0);
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::connectToOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::closeFromOracle() Success" << endl;
	return 1;
}

long long CDatabaseORA::getSendData(sql_context ctx, char* q_name, map<string,string>& mapSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char telco_name[24+1];
	long long mms_id;
	char cmms_id[30+1];
    char ctn_id[16+1];
    char callback[16+1];
    char dst_addr[12+1];
    char msgTitle[100+1];
    int cnt_type;
    char txt_path[256+1];
    int rgn_rate;
    int interval;
	int nCtnId;
	char ctn_name[50+1];
	char ctn_type[50+1];
	char id_code [12+1];	//식별코드
	
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */
    
    int ctn_cnt=0;
    char s_ctn_cnt[1+1];

	char s_mms_id[30+1];
	char s_cnt_type[2+1];
	char s_rgn_rate[2+1];
	char s_interval[2+1];

	memset(s_ctn_cnt, 0x00, sizeof s_ctn_cnt);
	
	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(telco_name, 0x00, sizeof telco_name);
	memset(ctn_id, 0x00, sizeof ctn_id);
	memset(callback, 0x00, sizeof callback);
	memset(dst_addr, 0x00, sizeof dst_addr);
	memset(msgTitle, 0x00, sizeof msgTitle);
	memset(txt_path, 0x00, sizeof txt_path);
	memset(cmms_id, 0x00, sizeof(cmms_id));
	memset(id_code, 0x00, sizeof(id_code));

	memset(s_mms_id, 0x00, sizeof s_mms_id);
	memset(s_cnt_type, 0x00, sizeof s_cnt_type);
	memset(s_rgn_rate, 0x00, sizeof s_rgn_rate);
	memset(s_interval, 0x00, sizeof s_interval);
	
	memset(ctn_name, 0x00, sizeof ctn_name);
	memset(ctn_type, 0x00, sizeof ctn_type);
	
	
	//mapSend.clear();
	
	strcpy(telco_name, q_name);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		proc_get_msg_key_skb(:telco_name, :cmms_id, :ctn_id, :callback, :dst_addr, :msgTitle, 
							    :cnt_type, :txt_path, :rgn_rate, :interval, :id_code, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	//sprintf(s_mms_id, "%d", mms_id);
	mms_id = atoll(cmms_id);
	sprintf(s_mms_id, "%s", cmms_id);
	sprintf(s_cnt_type, "%d", cnt_type);
	sprintf(s_rgn_rate, "%d", rgn_rate);
	sprintf(s_interval, "%d", interval);
	
	nCtnId = atoi(ctn_id);
	
	if(nCtnId > 0)
	{	
		EXEC SQL CONTEXT USE :ctx;
			EXEC SQL DECLARE C1 CURSOR FOR
				SELECT CTN_NAME, CTN_MIME FROM TBL_MMS_CTN WHERE CTN_ID = :nCtnId ORDER BY CTN_SEQ;
    		EXEC SQL OPEN C1;
		
		while(1)
		{
			EXEC SQL FETCH C1 INTO :ctn_name, :ctn_type;
    	
			if(sqlca.sqlcode == 1403)
			{
				//sprintf(tmpLog3, "CDatabaseORA::getSendData() CNT[%d] ctn_id : [%d] NOT FOUND", ctn_cnt, nCtnId);
				//log3(tmpLog3, 0, 0);
				break;
			}
			else if(sqlca.sqlcode != 0 )
			{
				sprintf(tmpLog3, "CDatabaseORA::getSendData() CNT[%d] ctn_id : [%d] SQL ERROR[%d][%s]", ctn_cnt, nCtnId, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
				log3(tmpLog3, 0, 0);
			}
			
			if(ctn_cnt == 0)
			{
				mapSend["ctn_name1"] = rtrim(ctn_name);
				mapSend["ctn_type1"] = rtrim(ctn_type);
			}
			
			if(ctn_cnt == 1)
			{
				mapSend["ctn_name2"] = rtrim(ctn_name);
				mapSend["ctn_type2"] = rtrim(ctn_type);
			}
			
			if(ctn_cnt == 2)
			{
				mapSend["ctn_name3"] = rtrim(ctn_name);
				mapSend["ctn_type3"] = rtrim(ctn_type);
			}
    	
			ctn_cnt++;
    	
		}
    EXEC SQL CLOSE C1;
	}
	
	switch(ot_sqlcode) {
		case 0:
			
			sprintf(s_ctn_cnt,"%d",ctn_cnt); //mapsend시 정상적인 값 입력을 위한 로직
			
			mapSend["s_mms_id"] = rtrim(s_mms_id);
			mapSend["ctn_id"] = rtrim(ctn_id);
			mapSend["callback"] = rtrim(callback);
			mapSend["dst_addr"] = rtrim(dst_addr);
			mapSend["msgTitle"] = rtrim(msgTitle);
			mapSend["s_cnt_type"] = rtrim(s_cnt_type);
			mapSend["txt_path"] = rtrim(txt_path);
			mapSend["s_rgn_rate"] = rtrim(s_rgn_rate);
			mapSend["s_interval"] = rtrim(s_interval);
			mapSend["ctn_cnt"] = rtrim(s_ctn_cnt); //contents count
			//mapSend["ctn_cnt"] = ctn_cnt; //contents count
			mapSend["id_code"] = rtrim(id_code);		//식별코드 추가 202309			
			
			return mms_id;
		case -5:
			usleep(80000);
			return 0;
		default:
			if (sqlca.sqlcode == -1405)
			{
				return 0;
			}
			sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);
			return -1;
	}

	return 0;
}

int CDatabaseORA::setSndAckData(int quid, char* resCode, sql_context ctx, vector<string>& vtSndAck)
{
EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	long long  mms_id;
	char msg_id[50+1];
	char ack_code[10+1];
	char ack_text[200+1];
	int telco_id;
	char ot_sqlmsg[256];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
	memset(msg_id, 0x00, sizeof(msg_id));
	memset(ack_code, 0x00, sizeof(ack_code));
	memset(ack_text, 0x00, sizeof(ack_text));

	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();

	mms_id = atoll(string(*(itrData + 2)).c_str());	//mms_id
	strcpy(msg_id, string(*(itrData + 2)).c_str());	//msg_id
	//strcpy(ack_code, string(*itrData).c_str());		// result code
	strcpy(ack_code, resCode);		// result code
	
	/*if (strcmp(ack_code,"0") == 0)
	{
		sprintf(ack_code,"1000");
	}*/
	strcpy(ack_text, "...");	// result_message
	telco_id = quid;	// QUEUE_GEMTEK 큐 넘버

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_ack2(:mms_id, :msg_id, :ack_code, :ack_text, :telco_id, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	if (ot_sqlcode!=0) {
		//sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		return 1;
}

int CDatabaseORA::setSndMsgReportData(int quid, char* resCode, sql_context ctx, map<string,string>& mapSend)
{
EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	long long  mms_id;
	char cmms_id[30+1];
	char msg_id[50+1];
	char msg_code[10+1];
	char msg_text[200+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	int telco_id;
	char ot_sqlmsg[256];
	int res_type;
	char end_telco[5+1];
	char dlv_date[14+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
	memset(msg_id, 0x00, sizeof(msg_id));
	memset(msg_code, 0x00, sizeof(msg_code));
	memset(msg_text, 0x00, sizeof(msg_text));
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id, 0x00, sizeof(cmms_id));
	memset(dlv_date, 0x00, sizeof(dlv_date));
	
	mms_id = atoll(mapSend["s_mms_id"].c_str());	//mms_id
	strcpy(msg_id, mapSend["s_mms_id"].c_str());	//msg_id
	strcpy(msg_code, resCode);		// result code
	sprintf(cmms_id, "%lld", mms_id);
	
	if (strcmp(msg_code,"1000") == 0)
	{
		//sprintf(ack_code,"1000");
		strcpy(msg_text, "success");
	}else{
		strcpy(msg_text, "fail");	
	}
	
	telco_id = quid;	// QUEUE_GEMTEK 큐 넘버
	
	res_type = 0;
	
	strcpy(end_telco, "ETC");
	
	//통신사로 발송된 시간이 없으면 현재 수신 시간 입력
	struct tm *t;
	time_t timer;
	timer = time(NULL);    // 현재 시각을 초 단위로 얻기
	t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기
	sprintf(dlv_date, "%04d%02d%02d%02d%02d%02d", t->tm_year + 1900, t->tm_mon + 1, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);
	
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :msg_code, :msg_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	if (ot_sqlcode!=0) {
		sprintf(tmpLog3, "CDatabaseORA::setSndMsgReportData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		return 1;
}

int CDatabaseORA::setSndAckReportData(int quid, char* resCode, sql_context ctx, vector<string>& vtSndAck)
{
EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	long long  mms_id;
	char cmms_id[30+1];
	char msg_id[50+1];
	char ack_code[10+1];
	char ack_text[200+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	int telco_id;
	char ot_sqlmsg[256];
	int res_type;
	char end_telco[5+1];
	char dlv_date[14+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
	memset(msg_id, 0x00, sizeof(msg_id));
	memset(ack_code, 0x00, sizeof(ack_code));
	memset(ack_text, 0x00, sizeof(ack_text));
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id, 0x00, sizeof(cmms_id));
	memset(dlv_date, 0x00, sizeof(dlv_date));

	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();

	mms_id = atoll(string(*(itrData + 2)).c_str());	//mms_id
	strcpy(msg_id, string(*(itrData + 2)).c_str());	//msg_id
	strcpy(ack_code, resCode);		// result code
	sprintf(cmms_id, "%lld", mms_id);
	
	if (strcmp(ack_code,"1000") == 0)
	{
		//sprintf(ack_code,"1000");
		strcpy(ack_text, "success");
	}else{
		strcpy(ack_text, "fail");	
	}
	
	telco_id = quid;	// QUEUE_GEMTEK 큐 넘버
	
	res_type = 0;
	
	strcpy(end_telco, "ETC");
	
	//통신사로 발송된 시간이 없으면 현재 수신 시간 입력
	struct tm *t;
	time_t timer;
	timer = time(NULL);    // 현재 시각을 초 단위로 얻기
	t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기
	sprintf(dlv_date, "%04d%02d%02d%02d%02d%02d", t->tm_year + 1900, t->tm_mon + 1, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);
	
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :ack_code, :ack_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	if (ot_sqlcode!=0) {
		//sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		return 1;
}

//int setReportData(sql_context ctx, vector<string>& vtReport);
int CDatabaseORA::setReportData(int quid, char* resCode, sql_context ctx, vector<string>& vtReport, char *Type)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[30+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(msg_id, 0x00, sizeof msg_id);
	memset(dlv_date, 0x00, sizeof dlv_date);
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(res_code, 0x00, sizeof res_code);
	memset(res_text, 0x00, sizeof res_text);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id, 0x00, sizeof(cmms_id));

	vector<string>::iterator itrData;
	itrData = vtReport.begin();

	mms_id=atoll((char*)string(*(itrData + 2)).c_str());						//CustomMessageID
	sprintf(cmms_id, "%lld", mms_id);
	//sprintf(msg_id, "%s", (char*)string(*(itrData + 3)).c_str());		//JobID
	sprintf(msg_id, "%lld", mms_id);
	//sprintf(snd_numb, "%s", (char*)string(*(itrData + 11)).c_str());			//SendNumber
	//sprintf(rcv_numb, "%s", (char*)string(*(itrData + 10)).c_str());			//ReceiveNumber
	sprintf(dlv_date, "%s", (char*)string(*(itrData + 4)).c_str());		//Time
	if (strcmp(dlv_date, "-") == 0 || strcmp(dlv_date, "              ") == 0)
	{//통신사로 발송된 시간이 없으면 현재 수신 시간 입력
		struct tm *t;
		time_t timer;
		timer = time(NULL);    // 현재 시각을 초 단위로 얻기
		t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기
		sprintf(dlv_date, "%04d%02d%02d%02d%02d%02d", t->tm_year + 1900, t->tm_mon + 1, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);
	}
	sprintf(res_code, "%s", resCode);		//Result

	if (atoi(res_code) == 1000)
	{
		//strcpy(res_code, "1000");
		sprintf(res_text, "success");
	}
	else
	{
		sprintf(res_text, "fail");
	}
	
	strcpy(end_telco, (char*)string(*(itrData + 5)).c_str());			//TelcoInfo

	/*if (strcmp(end_telco, "1") == 0)
		strcpy(end_telco, "SKT");
	else if (strcmp(end_telco, "2") == 0)
		strcpy(end_telco, "KTF");
	else if (strcmp(end_telco, "3") == 0)
		strcpy(end_telco, "LGT");
	else
		strcpy(end_telco, "ETC");*/
		
	if (strcmp(end_telco, "SK") == 0 || strcmp(end_telco, "SK") == 0)
		strcpy(end_telco, "SKT");
	else if (strcmp(end_telco, "KT") == 0||strcmp(end_telco, "KT") == 0)
		strcpy(end_telco, "KTF");
	else if (strcmp(end_telco, "LG") == 0||strcmp(end_telco, "LG") == 0)
		strcpy(end_telco, "LGT");
	else
		strcpy(end_telco, "ETC");

	telco_id=quid;
	res_type=0;

	int retry_cnt=0;

retry:
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :res_code, :res_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;
	
	if( ot_sqlcode != 0 )
	{
		if (strstr(ot_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			retry_cnt++;
			sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_skb retry [%s][%s][%d][%d][%s]", cmms_id, msg_id, ot_sqlcode, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);
			goto retry;
		}
		
		sprintf(tmpLog3, "CDatabaseORA::setReportData() ERROR[%s][%s][%d][%d][%.200s]", cmms_id, msg_id, ot_sqlcode, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc  );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	if (retry_cnt > 0)
	{
		sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_skb retry success");
		log3(tmpLog3, 0, 0);
	}

	return 0;
} 

int CDatabaseORA::_putMsgRetryDB(long long mmsid, char *q_name, sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
		int ot_sqlcode = -1;
		char ot_sqlmsg[1024];
		struct sqlca sqlca;
		long long mms_id;
		char cmms_id[30+1];
		char txt_path[256+1];
		int nTelcoID;
		char telco_name[24+1];
	
	EXEC SQL END DECLARE SECTION;
	
	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
  memset(telco_name, 0x00, sizeof telco_name);
  memset(txt_path, 0x00, sizeof txt_path);
	memset(cmms_id, 0x00, sizeof(cmms_id));
	
	strcpy(telco_name, q_name);
	
	char szYYYYMM[32];
	string strYYYYMM;
	char ymd[16] = {0};
	
	memset(szYYYYMM, 0x00, 32);

	//get_timestring("%04d%02d",time(NULL),szYYYYMM);
	get_timestring("%04d%02d%02d",time(NULL), ymd);
	snprintf(szYYYYMM, sizeof(szYYYYMM), "%.6s/%.4s", ymd, ymd+4);	

	mms_id = mmsid;
	sprintf(cmms_id,"%lld",mms_id);

	sprintf(txt_path, "TXT_SSN/%s/%lld.txt", szYYYYMM, mms_id);	// TXT_PATH
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
		EXEC SQL EXECUTE
	      BEGIN
	      	//proc_set_msgretry(:nMsgID, :nTelcoID, :nPriority, :ot_sqlcode, :ot_sqlmsg);
	      	//proc_set_msg_retry_skb(:telco_name, :cmms_id, :txt_path, :ot_sqlcode, :ot_sqlmsg);
			proc_set_msg_retry_key_skb(:telco_name, :cmms_id, :txt_path, :ot_sqlcode, :ot_sqlmsg);
	  		END;
	  END-EXEC;
	  
	  sprintf(tmpLog3, "CDatabaseORA::_putMsgRetryDB() [%lld][%s]", mms_id, telco_name);
		log3(tmpLog3, 0, 0);
	
	  if( ot_sqlcode != 0 )
	  {
			sprintf(tmpLog3, "CDatabaseORA::_putMsgRetryDB() ERROR[%lld][%d][%s]", mms_id, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);
	
	      return -1;
	  }
	  return 0;
}

int CDatabaseORA::getCheckReport(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int report_cnt = 0;
	
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		SELECT count(*) into :report_cnt from dual;
	END;
	END-EXEC;
    
    sprintf(tmpLog3, "CDatabaseORA::getCheckReport() report_cnt[%d]", report_cnt);
		log3(tmpLog3, 0, 0);
		
    if(report_cnt != 1)
    {
    	sprintf(tmpLog3, "CDatabaseORA::getCheckReport() report_cnt ERROR[%d]", report_cnt);
		log3(tmpLog3, 0, 0);
    }
   
    switch(sqlca.sqlcode) {
		case 0:
		case -25228:
		case -1405:
			return 0;
		default:
			//sprintf(tmpLog3, "CDatabaseORA::getSendData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			sprintf(tmpLog3, "CDatabaseORA::getCheckReport() ERROR[%d][%.80s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);

			return -1;
	}
    
	return 0;
}

/*
 * 시간 문자열을 출력한다.
 */
void CDatabaseORA::get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
}


}

void log3(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "DatabaseORA", 0, "");
	}
}


