/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <vector>
#include <ml_ctrlsub.h>
#include <map>
#include <ctype.h> // isspace()
#include <unistd.h>

#define MAX_STR_LEN 4000

using namespace std;

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int setEnableThreads();
	int initThread(sql_context& ctx);
	int freeThread(sql_context& ctx);
	int connectToOracle(sql_context ctx, char*, char*);
	int closeFromOracle(sql_context ctx);
	int setSndAckData(int quid, char* resCode, sql_context ctx, vector<string>& vtSndAck);
	int setSndMsgReportData(int quid, char* resCode, sql_context ctx, map<string,string>& mapSend);
	int setSndAckReportData(int quid, char* resCode, sql_context ctx, vector<string>& vtSndAck);
	//int setReportData(sql_context ctx, vector<string>& vtReport);
	int setReportData(int quid, char* resCode, sql_context ctx, vector<string>& vtReport, char *Type);
	long long getSendData(sql_context ctx, char* q_name, map<string,string>& mapSend);
	int getCheckReport(sql_context ctx);
	int _putMsgRetryDB(long long mmsid, char *q_name, sql_context ctx);
	
	void get_timestring(char *fmt, long n, char *s);

private:
	bool m_bThread;
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
	
	char* rtrim(char* s) {
  	char t[MAX_STR_LEN];
  	char *end;
  	
  	// Visual C 2003 이하에서는
  	// strcpy(t, s);
  	// 이렇게 해야 함
  	strcpy(t, s); // 이것은 Visual C 2005용
  	end = t + strlen(t) - 1;
  	while (end != t && isspace(*end))
  	  end--;
  	*(end + 1) = '\0';
  	s = t;
  	
  	return s;
	}
};

}

#endif /* DATABASEORA_H_ */
