/*
 * PacketCtrlSKY.h
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#ifndef PACKETCTRLSKY_H_
#define PACKETCTRLSKY_H_
#define SHA_DIGEST_LENGTH = 20
#define MAX_BUFF			4096
#define PROTOCOL_VER	"TV_2.0.1"

#include <string>
#include <vector>
#include <arpa/inet.h>
#include <iostream>
#include <ml_ctrlsub.h>
#include <map>
#include <sys/time.h>
//#include "myException.h"
//#include "ksbase64.h"

using namespace std;

typedef struct _HEADER {				/* wisecan 전문 Header */
	char headType[4];
	char msg<PERSON>eng[10];
} HEADER;

typedef struct MSG_CERT_SND	{		/* 인증 전문 [Client -> gemtek] */
	HEADER header;
	char szCID[20];
	char szPWD[110];
	char szVersion[15];
	char szKey<PERSON>s[ 4];
} CERT_REQ;

typedef struct MSG_CERT_ACK	{		/* 인증 전문 [Client -> gemtek] */
	HEADER header;
	char szResult[4];
	char sz<PERSON><PERSON><PERSON><PERSON>[65];
	char szServerIP[40];
	char szDeliverPort[8];
	char szReportPort[8];
} CERT_ACK;

typedef struct MSG_BIND_SND	{		/* 접속요청 전문 [Client -> gemtek] */
	HEADER header;
	char szCID[20];
	char szPWD[110];
	char szCertKey[65];
	char szType[2];
	char szKind[4];					// DLV, REP
	char szVersion[15];				// TV_2.0.1
	char szKeypos[4];
} BIND_REQ;

typedef struct MSG_BIND_ACK {		/* 접속요청에 대한 응답 [gemtek -> Client] */
	HEADER header;
	char szResult[4];
	//char szRersec[8];
	char szSmsTps[5];
	char szMmsTps[5];
	char szKkoTps[5];
	char szMessage[40];
} BIND_ACK;

typedef struct MSG_DATA_SND	{		/* 메세지 전송 전문 [Client -> gemtek] */
	HEADER header;
  char szMsgType[2];
  char szDaAddr[20];
  char szCallBack[20];
  char szEncoding	[ 2];			// 0:EUC-KR , 1: UTF-8 (202308 add)
  //char szSubject[90];
  char szSubject[141];
  char szSerial[20];
  char szSenderCode	[10];			// 식별코드 (202308 add)
  //char szExpiredTime[7];
  //char szReport[2];
  //char szDaCnt[4];
  //char szDaAddr[20];   				
  char szMediaCnt[2];
  char szExtSize	[ 4];			// 확장필드 크기 ((202308 add)
} DELIVER_REQ;	/* header 14 byte, body 221 = 235 byte */

typedef struct MSG_DATA_MEDIA	{		/* 메세지 전송 전문 [Client -> gemtek] */
  	char szMType[3];				// 11 - Text (LMS/MMS 본문)
  									// 21 - JPG 이미지 (MMS)
    char szEncoding	[ 2];			// 0:EUC-KR , 1: UTF-8 , 2: Binary (202308 add)
  	//char szMFileName[50];
  	char szMFileLen[10];
  	char szMedia[];
}DELIVER_MEDIA;	

typedef struct MSG_DATA_SND_ENC	{		/* 메세지 전송 전문 [Client -> gemtek] */
  HEADER header;   				
  char szData[224];
} DELIVER_REQ_ENC;	

typedef struct MSG_DATA_ACK {		/* 응답전문 [gemtek -> Client] */
	HEADER header;
	char szResult[4];
	char szDaAddr[20];
	char szSerial[20];
} DELIVER_ACK;

typedef struct MSG_DATA_ACK_ENC	{		/* 메세지 전송 전문 [Client -> gemtek] */
  HEADER header;   				
  char szData[48];
} DELIVER_ACK_ENC;	

typedef struct MSG_DATA_ACK_BODY {		/* 응답전문 [gemtek -> Client] */
	char szResult[4];
	char szDaAddr[20];
	char szSerial[20];
} DELIVER_ACK_BODY;


typedef struct MSG_TRNS_REP	{		/* 메세지 단말기 수신결과 전문 (레포트 전문 ) [KT -> Client] */
	HEADER header;
	char szResult[4];
	char szMessage	[15];			// 결과메시지 202308 추가
	char szDaAddr[20];
	char szSerial[20];
	char szGwSerial[16];
	char szSendTime[15];
	char szTelcoInfo[4];
} REPORT;

typedef struct MSG_TRNS_REP_ENC	{		/* 메세지 전송 전문 [Client -> gemtek] */
  HEADER header;   				
  //char szData[80];
  char szData[96];
} REPORT_ENC;	

typedef struct MSG_TRNS_REP_BODY	{		/* 메세지 단말기 수신결과 전문 (레포트 전문 ) [KT -> Client] */
	char szResult[4];
	char szMessage	[15];			// 결과메시지 202308 추가
	char szDaAddr[20];
	char szSerial[20];
	char szGwSerial[16];
	char szSendTime[15];
	char szTelcoInfo[4];
} REPORT_BODY;

typedef struct MSG_REPORT_ACK {	/* 응답전문 [Client -> gemtek] */
	HEADER header;
	char szResult[4];
	char szSerial[20];
	char szGwSerial[16];
} REPORT_ACK;

typedef struct MSG_REPORT_ACK_ENC {	/* 응답전문 [Client -> gemtek] */
	HEADER header;
	 char szData[48];
} REPORT_ACK_ENC;

typedef struct MSG_LINK_CHK	{
	HEADER header;
} LINK_CHK;

typedef struct MSG_LINK_CHK_ACK	{
	HEADER header;
} LINK_CHK_ACK;

typedef struct tag_resultTable {/* CodeTable struct */
    char tCode[8];
    char kCode[8];
    char desc[512];
} CODETABLE;

class CPacketCtrlSKY
{
public:
	enum logLevels {
		TYPE_CERT_REQ = 0,					/* Cert 요청		*/
		TYPE_CERT_ACK = 1,					/* Cert 응답 		*/
		TYPE_BIND_REQ = 11,					/* Bind 요구		*/
		TYPE_BIND_ACK = 12,						/* Bind 응답		*/
		TYPE_DELIVER_REQ = 21,				/* 전송요청		*/
		TYPE_DELIVER_ACK = 22,					/* SubmitAck	*/
		TYPE_REPORT = 31,						/* 전송결과		*/
		TYPE_REPORT_ACK = 32,					/* 전송결과Ack	*/
		TYPE_PING = 41,						/* 상태확인		*/
		TYPE_PONG	= 42,					/* 상태확인Ack	*/
		TYPE_EVENT = 51
											/* (LINK_CHECK && LINK_ACK) == 100 */
	};

   CPacketCtrlSKY() {};
	virtual ~CPacketCtrlSKY() {};
	
	char*		m_ptrBuff1;	
	char*		m_ptrBuff2;	
	char*		m_ptrBuff3;
	char*       m_ptrData1;	
	char*       m_ptrData2;	
	char*       m_ptrData3;	
	
	//int getMsg_BIND_REQ(char* pBuff, char* id, char* pw, char* szTp, char* szKnd, char* offset, char* encPw2);		
	int getMsg_BIND_REQ(char* pBuff, char* id, char* pw, char* szTp, char* szKnd, char* offset, char* certKey);
	int getMsg_CERT_REQ(char* pBuff, char* id, char* pw, char* offset, char* encPw2, char * encDataKey);
	int getMsg_PING_REQ(char* pBuff);
	int getMsg_PING_RES(char* pBuff);
	int getMsg_DELIVER_REQ(char* pBuff, long long nMsgSeq, char* szExpiredTime, char* szSubject, map<string,string> &mapSend);
	int getMsg_REPORT_ACK(char* pBuff, char* result, char* serial, char* gwSerial);
	int getData_BndAck(char* pBuff, vector<string>& vtBndAck);
	int getData_CertAck(char* pBuff, vector<string>& vtCertAck);
	int getData_SndAck(char* pBuff, vector<string>& vtSndAck);
	int getData_SndAck_ENC(char* pBuff, char* encKey ,vector<string>& vtSndAck);
	int getData_Report(char* pBuff, vector<string>& vtReport);
	int getData_Report_ENC(char* pBuff, char* encKey, vector<string>& vtReport);
	int getMsgEncKey(char* pBuff,char *id, char *pw, int nCntCode);
	int getMsgCode(char* pBuff);
	//int GetLMSText(const char *TxtPath, char* TxtData, bool *isColor, int nImgCnt);
	int GetLMSText(const char *TxtPath, char* TxtData, bool *isColor, int nImgCnt);
	char* trim(char* szOrg, int leng);
	void get_timestring(char *fmt, long n, char *s);
};

#endif /* PACKETCTRLSKY_H_ */