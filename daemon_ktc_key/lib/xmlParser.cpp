#include "xmlParser.h"

void processNode(xmlTextReaderPtr reader, char *Title, char *Value)
{
    const xmlChar   *name, *value, *node;
	bool find = false;
	int ret = 0;

	while(1)
	{
		name = xmlTextReaderConstName(reader);
		if (name == NULL)
		{
			printf("name null\n");
			break;
		}

		if((!xmlStrcmp(name, (const xmlChar *) Title)))
		{
			find = true;
			break;
		}
		if (xmlTextReaderRead(reader) == 0)
		{
			printf("processNode break\n");
			break;
		}
	}

	if (find)
	{
		if (xmlTextReaderRead(reader) == NULL)
		{
			return;
		}
		name = xmlTextReaderConstName(reader);
		value = xmlTextReaderConstValue(reader);

		if(value == NULL)
		{
			//printf("Title : [%s],", Title);
			//printf("value : [-]\n");
			sprintf(Value,"-");
		}
		else
		{
			xmlStrlen(value);
			//printf("Title : [%.25s], ", Title);
			//printf("value : [%s]\n", value);
			sprintf(Value,"%s", value);
		}
	}
}

void streamXmlTitle(char *xmlResult, char *Title)
{
    xmlTextReaderPtr reader;
    int ret;
    const xmlChar *name, *value;
	
    reader = xmlReaderForMemory(xmlResult,strlen(xmlResult), NULL, NULL, 0);

    if(reader != NULL)
    {
        ret = xmlTextReaderRead(reader);
        if (ret == 1)
        {
			name = xmlTextReaderConstName(reader);
			if (name == NULL)
			{
				printf("name null\n");
				name = BAD_CAST "--";
			}
			else
			{
				//printf("name : %s\n", name);
				if((value = xmlTextReaderGetAttributeNo(reader, 0)) == NULL)
				{
					printf("value null\n");
				}
				else
				{
					sprintf(Title, "%s", value);
					//printf("value : %s\n", value);
				}
			}
        }
        xmlFreeTextReader(reader);
    }
    else
    {
        printf("Unable to open\n");
    }
	xmlCleanupParser();
	xmlMemoryDump();
}
