/*
 * DatabaseORA.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA_MMS.h"
#include <sqlca.h>
#include <string.h>
#include <stdlib.h>
#include <iostream>

using namespace std;

namespace KSKYB
{
int CDatabaseORA::setEnableThreads()
{
	m_bThread = true;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ENABLE THREADS;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::setEnableThreads() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT ALLOCATE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::initThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	pCtx = ctx;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT FREE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::freeThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char szConnInf[20+1], szConnDsn[20+1];
	EXEC SQL END DECLARE SECTION;

	if ((szUID == NULL) || (szDSN == NULL)) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[szUID or szDSN is NULL]");
		log3(tmpLog3, 0, 0);
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::connectToOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::closeFromOracle() Success" << endl;
	return 1;
}

long long CDatabaseORA::getMsgData(sql_context ctx, char *q_name, vector<string>& vtSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char telco_name[24+1];
	//long long mms_id;
	long long ll_mms_id;
	//char cmms_id[30+1];
	char cmms_id[16+1];
	long long ll_ctn_id;
    char ctn_id[16+1];
    char callback[16+1];
    char dst_addr[12+1];
    char msgTitle[100+1];
    int cnt_type;
    char txt_path[256+1];
    int rgn_rate;
    int interval;
	char id_code [12+1];	//식별코드

	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	char s_mms_id[16+1];
	char s_ctn_id[16+1];
	char s_cnt_type[2+1];
	char s_rgn_rate[2+1];
	char s_interval[2+1];

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(telco_name, 0x00, sizeof telco_name);
	memset(ctn_id, 0x00, sizeof ctn_id);
	memset(callback, 0x00, sizeof callback);
	memset(dst_addr, 0x00, sizeof dst_addr);
	memset(msgTitle, 0x00, sizeof msgTitle);
	memset(txt_path, 0x00, sizeof txt_path);
	ll_mms_id = 0;
	ll_ctn_id = 0;
	memset(cmms_id, 0x00, sizeof(cmms_id));
	memset(id_code, 0x00, sizeof(id_code));

	memset(s_mms_id, 0x00, sizeof s_mms_id);
	memset(s_ctn_id, 0x00, sizeof s_ctn_id);
	memset(s_cnt_type, 0x00, sizeof s_cnt_type);
	memset(s_rgn_rate, 0x00, sizeof s_rgn_rate);
	memset(s_interval, 0x00, sizeof s_interval);

	strcpy(telco_name, q_name);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		//proc_get_msg_skb(:telco_name, :cmms_id, :ctn_id, :callback, :dst_addr, :msgTitle, :cnt_type, :txt_path, :rgn_rate, :interval, :ot_sqlcode, :ot_sqlmsg);
		//proc_get_msg_key_skb(:telco_name, :cmms_id, :ctn_id, :callback, :dst_addr, :msgTitle, :cnt_type, :txt_path, :rgn_rate, :interval, :id_code, :ot_sqlcode, :ot_sqlmsg);
		proc_get_msg_key_skb(:telco_name, :ll_mms_id, :ctn_id, :callback, :dst_addr, :msgTitle, :cnt_type, :txt_path, :rgn_rate, :interval, :id_code, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	//sprintf(s_mms_id, "%d", mms_id);
	//mms_id = atoll(cmms_id);
	//sprintf(s_mms_id, "%s", cmms_id);	
	//sprintf(s_cnt_type, "%d", cnt_type);
	//sprintf(s_rgn_rate, "%d", rgn_rate);
	//sprintf(s_interval, "%d", interval);

	//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ll_mms_id[%lld] s_mms_id[%s]", ll_mms_id, s_mms_id );
	//log3(tmpLog3, 0, 0);	
	
	//sprintf(tmpLog3, "CDatabaseORA::getMsgData() proc_get_msg_key_skb ot_sqlcode [%d]", ot_sqlcode );
	//log3(tmpLog3, 0, 0);


	switch(ot_sqlcode) {
		case 0:
			////sprintf(s_mms_id, "%d", mms_id);
			//ll_mms_id = atoll(trimR(cmms_id).c_str());
			// 사이즈 초과로 메모리 침범 
			// s_mms_id는 16바이트인데 cmms_id 를 30바이트로 선언
			////sprintf(s_mms_id, "%s", cmms_id);
			sprintf(s_mms_id, "%-16lld", ll_mms_id);

			ll_ctn_id = atoll(trimR(ctn_id).c_str());
			sprintf(s_ctn_id, "%-16lld", ll_ctn_id);
			
			sprintf(s_cnt_type, "%d", cnt_type);
			sprintf(s_rgn_rate, "%d", rgn_rate);
			sprintf(s_interval, "%d", interval);

			sprintf(tmpLog3, "CDatabaseORA::getMsgData() cmms_id [%s] ll_mms_id [%lld]", cmms_id, ll_mms_id );
			log3(tmpLog3, 0, 0);
			sprintf(tmpLog3, "CDatabaseORA::getMsgData() s_mms_id [%s]", s_mms_id );
			log3(tmpLog3, 0, 0);
			sprintf(tmpLog3, "CDatabaseORA::getMsgData() ctn_id [%s] ll_ctn_id [%lld]", ctn_id, ll_ctn_id );
			log3(tmpLog3, 0, 0);
			sprintf(tmpLog3, "CDatabaseORA::getMsgData() s_ctn_id [%s]", s_ctn_id );
			log3(tmpLog3, 0, 0);

			sprintf(tmpLog3, "CDatabaseORA::getMsgData() id_code [%s]", trimR(id_code).c_str() );
			log3(tmpLog3, 0, 0);
			vtSend.push_back(trimR(s_mms_id).c_str());
			//vtSend.push_back(trimR(ctn_id).c_str());
			vtSend.push_back(trimR(s_ctn_id).c_str());
			vtSend.push_back(trimR(callback).c_str());
			vtSend.push_back(trimR(dst_addr).c_str());
			vtSend.push_back(trimR(msgTitle).c_str());
			vtSend.push_back(trimR(s_cnt_type).c_str());
			vtSend.push_back(trimR(txt_path).c_str());
			vtSend.push_back(trimR(s_rgn_rate).c_str());
			vtSend.push_back(trimR(s_interval).c_str());
			vtSend.push_back(trimR(id_code).c_str());		//식별코드 추가 202309	

			return ll_mms_id;
		case -5:
			return 0;
		default:
			if (sqlca.sqlcode == -1405)
			{
				return 0;
			}
			sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);
			return -1;
	}

	return 0;
}

int CDatabaseORA::getCtnData(sql_context ctx, int cid, vector<string>& vtCtnData)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ctn_id;
	char ctn_name[50+1];
	char ctn_type[50+1];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND DO BREAK;
    EXEC SQL WHENEVER SQLERROR goto sql_error;   /* don't trap errors */

	int ctn_cnt=0;

	memset(ctn_name, 0x00, sizeof ctn_name);
	memset(ctn_type, 0x00, sizeof ctn_type);

	ctn_id = cid;

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL DECLARE C1 CURSOR FOR
		SELECT CTN_NAME, CTN_MIME FROM TBL_MMS_CTN WHERE CTN_ID = :ctn_id ORDER BY CTN_SEQ;
    EXEC SQL OPEN C1;

	while(1)
	{
		EXEC SQL FETCH C1 INTO :ctn_name, :ctn_type;

		if(sqlca.sqlcode == 1403)
		{
			sprintf(tmpLog3, "CDatabaseORA::getCtnData() CNT[%d] ctn_id : [%d] NOT FOUND", ctn_cnt, ctn_id, ctn_name, ctn_type);
			log3(tmpLog3, 0, 0);
			break;
		}
		else if(sqlca.sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::getCtnData() CNT[%d] ctn_id : [%d] SQL ERROR", ctn_cnt, ctn_id, ctn_name, ctn_type);
			log3(tmpLog3, 0, 0);
		}

		vtCtnData.push_back(trimR(ctn_name).c_str());
		vtCtnData.push_back(trimR(ctn_type).c_str());
		ctn_cnt++;

		//sprintf(tmpLog3, "ctn_id[%d] : [%d] ctn_name : [%s] ctn_type : [%s]", ctn_cnt, ctn_id, ctn_name, ctn_type);
		//log3(tmpLog3, 0, 0);
	}
    EXEC SQL CLOSE C1;

	return ctn_cnt;

sql_error:
	sprintf(tmpLog3, "\n CDatabaseORA::getCtnData() ERROR[%d][%70s] \n", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
	log3(tmpLog3, 0, 0);
	return -1;
}

int CDatabaseORA::setSndAckData(int telcoid, sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	long long  mms_id;
	char msg_id[50+1];
	char ack_code[10+1];
	char ack_text[200+1];
	int telco_id;
	char ot_sqlmsg[256];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
	memset(msg_id, 0x00, sizeof(msg_id));
	memset(ack_code, 0x00, sizeof(ack_code));
	memset(ack_text, 0x00, sizeof(ack_text));

	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();

	mms_id = atoll(string(*(itrData + 2)).c_str());	//mms_id
	strcpy(msg_id, string(*(itrData + 2)).c_str());	//msg_id
	strcpy(ack_code, string(*itrData).c_str());		// result code
	if (strcmp(ack_code,"0") == 0)
	{
		sprintf(ack_code,"1000");
	}
	strcpy(ack_text, "...");	// result_message
	telco_id = telcoid;	// QUEUE_KTC_MMS 큐 넘버

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_ack2(:mms_id, :msg_id, :ack_code, :ack_text, :telco_id, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	if (ot_sqlcode!=0) {
		//sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		return 1;
}

int CDatabaseORA::setReportData(int telcoid, sql_context ctx, vector<string>& vtReport)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[30+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(msg_id, 0x00, sizeof msg_id);
	memset(dlv_date, 0x00, sizeof dlv_date);
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(res_code, 0x00, sizeof res_code);
	memset(res_text, 0x00, sizeof res_text);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id, 0x00, sizeof(cmms_id));

	vector<string>::iterator itrData;
	itrData = vtReport.begin();

	mms_id=atoll((char*)string(*(itrData + 4)).c_str());						//CustomMessageID
	sprintf(cmms_id, "%lld", mms_id);
	sprintf(msg_id, "%s", (char*)string(*(itrData + 5)).c_str());		//JobID
	sprintf(snd_numb, "%s", (char*)string(*(itrData + 11)).c_str());			//SendNumber
	sprintf(rcv_numb, "%s", (char*)string(*(itrData + 10)).c_str());			//ReceiveNumber
	sprintf(dlv_date, "%s", (char*)string(*(itrData + 3)).c_str());		//Time
	if (strcmp(dlv_date, "-") == 0 || strcmp(dlv_date, "              ") == 0)
	{//통신사로 발송된 시간이 없으면 현재 수신 시간 입력
		struct tm *t;
		time_t timer;
		timer = time(NULL);    // 현재 시각을 초 단위로 얻기
		t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기
		sprintf(dlv_date, "%04d%02d%02d%02d%02d%02d", t->tm_year + 1900, t->tm_mon + 1, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);
	}
	sprintf(res_code, "%s", (char*)string(*(itrData + 2)).c_str());		//Result

	if (atoi(res_code) == 0)
	{
		strcpy(res_code, "1000");
		sprintf(res_text, "success");
	}
	else
	{
		sprintf(res_text, "fail");
	}

	strcpy(end_telco, (char*)string(*(itrData + 12)).c_str());			//TelcoInfo

	if (strcmp(end_telco, "1") == 0)
		strcpy(end_telco, "SKT");
	else if (strcmp(end_telco, "2") == 0)
		strcpy(end_telco, "KTF");
	else if (strcmp(end_telco, "3") == 0)
		strcpy(end_telco, "LGT");
	else
		strcpy(end_telco, "ETC");

	telco_id=telcoid;
	res_type=0;

	int retry_cnt=0;

retry:
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :res_code, :res_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;
	if( ot_sqlcode != 0 )
	{
		if (strstr(ot_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			retry_cnt++;
			sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_ktc retry [%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);
			goto retry;
		}

		sprintf(tmpLog3, "CDatabaseORA::setReportData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc  );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	if (retry_cnt > 0)
	{
		sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_ktc retry success");
		log3(tmpLog3, 0, 0);
	}

	return 0;
}

int CDatabaseORA::setReportDataUsingSndAck(int telcoid, sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[30+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(msg_id, 0x00, sizeof msg_id);
	memset(dlv_date, 0x00, sizeof dlv_date);
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(res_code, 0x00, sizeof res_code);
	memset(res_text, 0x00, sizeof res_text);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id, 0x00, sizeof(cmms_id));

	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();

	mms_id = atoll(string(*(itrData+2)).c_str());						//CustomMessageID
	sprintf(cmms_id, "%lld", mms_id);
	sprintf(msg_id, "%s", (char*)string(*(itrData+4)).c_str());		//JobID
	//sprintf(snd_numb, "%s", (char*)string(*(itrData + 11)).c_str());			//SendNumber
	//sprintf(rcv_numb, "%s", (char*)string(*(itrData + 10)).c_str());			//ReceiveNumber
	sprintf(dlv_date, "%s", (char*)string(*(itrData+1)).c_str());		//Time
	if (strcmp(dlv_date, "-") == 0 || strcmp(dlv_date, "              ") == 0)
	{//통신사로 발송된 시간이 없으면 현재 수신 시간 입력
		struct tm *t;
		time_t timer;
		timer = time(NULL);    // 현재 시각을 초 단위로 얻기
		t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기
		sprintf(dlv_date, "%04d%02d%02d%02d%02d%02d", t->tm_year + 1900, t->tm_mon + 1, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);
	}
	sprintf(res_code, "%s", (char*)string(*(itrData)).c_str());		//Result

	if (atoi(res_code) == 0)
	{
		strcpy(res_code, "1000");
		sprintf(res_text, "success");
	}
	else
	{
		sprintf(res_text, "fail");
	}

	/*strcpy(end_telco, (char*)string(*(itrData + 12)).c_str());			//TelcoInfo


	if (strcmp(end_telco, "1") == 0)
		strcpy(end_telco, "SKT");
	else if (strcmp(end_telco, "2") == 0)
		strcpy(end_telco, "KTF");
	else if (strcmp(end_telco, "3") == 0)
		strcpy(end_telco, "LGT");
	else
		strcpy(end_telco, "ETC");
	*/
	strcpy(end_telco, "ETC");

	telco_id = telcoid;
	res_type = 0;

	int retry_cnt=0;

retry:
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :res_code, :res_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;
	if( ot_sqlcode != 0 )
	{
		if (strstr(ot_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			retry_cnt++;
			sprintf(tmpLog3, "CDatabaseORA::setReportDataUsingSndAck() proc_set_rpt_ktc retry [%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);
			goto retry;
		}

		sprintf(tmpLog3, "CDatabaseORA::setReportDataUsingSndAck() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc  );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	if (retry_cnt > 0)
	{
		sprintf(tmpLog3, "CDatabaseORA::setReportDataUsingSndAck() proc_set_rpt_ktc retry success");
		log3(tmpLog3, 0, 0);
	}

	return 0;
}

int CDatabaseORA::_putMsgRetryDB(long long mmsid, char *q_name, int telcoid, sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
	char telco_name[24+1];
	long long mms_id;
	char cmms_id[30+1];
	char txt_path[256+1];
    struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

    memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
    memset(telco_name, 0x00, sizeof telco_name);
    memset(txt_path, 0x00, sizeof txt_path);
	memset(cmms_id, 0x00, sizeof(cmms_id));
	vector<string>::iterator itrData;
	//데이터 입력
	strcpy(telco_name, q_name);

	char szYYYYMM[32];
	char ymd[16] = {0};

	memset(szYYYYMM, 0x00, 32);

	//get_timestring("%04d%02d",time(NULL),szYYYYMM);
	get_timestring((char *)"%04d%02d%02d",time(NULL), ymd);
	snprintf(szYYYYMM, sizeof(szYYYYMM), "%.6s/%.4s", ymd, ymd+4);
	trim(szYYYYMM,strlen(szYYYYMM));

	mms_id = mmsid;
	sprintf(cmms_id,"%lld",mms_id);

	sprintf(txt_path, "TXT_SSN/%s/%lld.txt", szYYYYMM, mms_id);	// TXT_PATH
	char * pch;
	// if (telcoid == 1)
	// {
	// 	pch = strstr (txt_path,"KTF");
	// 	if (pch) strncpy (pch,"SKT",3);
	// 	pch = strstr (txt_path,"LGT");
	// 	if (pch) strncpy (pch,"SKT",3);
	// 	pch = strstr (txt_path,"SSN");
	// 	if (pch) strncpy (pch,"SKT",3);
	// }

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		//proc_set_msg_retry_skb(:telco_name, :cmms_id, :txt_path, :ot_sqlcode, :ot_sqlmsg);
		proc_set_msg_retry_key_skb(:telco_name, :cmms_id, :txt_path, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	switch (ot_sqlcode)
	{
		case 0 ://success
			return 0;
		break;
		case 100 ://no data found
			sprintf(tmpLog3, "CDatabaseORA::_putMsgRetryDB() NO DATA FOUND ERROR ot_sqlcode[%d], [%lld]", ot_sqlcode, mmsid);
			log3(tmpLog3, 0, 0);
			return 0;
		break;
		default :
			sprintf(tmpLog3, "CDatabaseORA::_putMsgRetryDB() ERROR ot_sqlcode[%d], [%d][%s]", ot_sqlcode, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);
			return -1;
		break;
	}
    return 0;
}


/*
 * 시간 문자열을 출력한다.
 */
void CDatabaseORA::get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
}

char* CDatabaseORA::trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

void CDatabaseORA::log3(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "DatabaseORA", 0, "");
	}
}

}
