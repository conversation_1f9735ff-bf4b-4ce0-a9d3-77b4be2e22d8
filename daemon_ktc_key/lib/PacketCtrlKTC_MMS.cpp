/*
 * PacketCtrlKTC_MMS.cpp
 *
 *  Created on: 2011. 07. 28.
 *      Author: Administrator
 */

#include "PacketCtrlKTC_MMS.h"
#include "xml.h"
#include "cryptopp.h"

#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <time.h>
#include <iostream>
#include <iconv.h>
#include <errno.h>

char tmpLog2[MAX_BUFF];
char tmpLog[MAX_BUFF*MAX_BUFF+1];
char szXmlData[MAX_BUFF];

void log2(char *buf, int st, int err);

void CPacketCtrlKTC::SetReplaceTel(char* pSource, int nSourceSize)
{
	char pDest[nSourceSize];
	int i, j;
	memset(pDest, 0x00, nSourceSize);
	
	if (strlen(pSource) == 0) return;

	for(i=0,j=0;i<nSourceSize;i++)
	{
		//HYPHEN-MINUS, SPACE 제거
		if (!(pSource[i] == (char)0x2d || pSource[i] == (char)0x20))
		{
			pDest[j++] = pSource[i];
		}
	}

	memset(pSource, 0x00, nSourceSize);
	strcpy(pSource, pDest);
}

void CPacketCtrlKTC::SetReplaceCRCF(char* pSource, int nSourceSize)
{
	char pDest[nSourceSize];
	int i, j;
	memset(pDest, 0x00, nSourceSize);

	if (strlen(pSource) == 0) return;

	for(i=0,j=0;i<nSourceSize;i++)
	{
		//'\r' '\n' 제거
		if (!(pSource[i] == (char)0x0d || pSource[i] == (char)0x0a))
		{
			pDest[j++] = pSource[i];
		}
	}

	memset(pSource, 0x00, nSourceSize);
	strcpy(pSource, pDest);
}

//XML 특수문자 처리
void CPacketCtrlKTC::SetReplaceEscapeChar(char* pSource, int nSourceSize)
{
	char pDest[MAX_BUFF];
	int i, j;
	memset(pDest, 0x00, MAX_BUFF);

	if (strlen(pSource) == 0) return;

	//escape문자 처리
	for(i=0,j=0;j<nSourceSize;j++)
	{
		if (pSource[j] == '&')	
		{//& : &amp;
			pDest[i++]='&';
			pDest[i++]='a';
			pDest[i++]='m';
			pDest[i++]='p';
			pDest[i++]=';';
			continue;
		}
		else if (pSource[j] == '<')
		{//< : &lt;
			pDest[i++]='&';
			pDest[i++]='l';
			pDest[i++]='t';
			pDest[i++]=';';
			continue;
		}
		else if (pSource[j] == '>')
		{//> : &gt;
			pDest[i++]='&';
			pDest[i++]='g';
			pDest[i++]='t';
			pDest[i++]=';';
			continue;
		}
		else if (pSource[j] == '\'')
		{//' : &apos;
			pDest[i++]='&';
			pDest[i++]='a';
			pDest[i++]='p';
			pDest[i++]='o';
			pDest[i++]='s';
			pDest[i++]=';';
			continue;
		}
		else if (pSource[j] == '"')
		{//" : &quot;
			pDest[i++]='&';
			pDest[i++]='q';
			pDest[i++]='u';
			pDest[i++]='o';
			pDest[i++]='t';
			pDest[i++]=';';
			continue;
		}
		pDest[i++]=pSource[j];
	}

	memset(pSource, 0x00, nSourceSize);
	strcpy(pSource, pDest);
}

//////////////////////////////////////////////////////////////////////////////////////////////
// 보낼 데이터 구성
//////////////////////////////////////////////////////////////////////////////////////////////
int CPacketCtrlKTC::getMsg_MAS_SERVER_REQ(char* pBuff, char* ip)
{
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szXmlData,"GET /catalogs/MAS/recommended/0\r\nHost: %s\r\n\r\n", ip);
	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_FUS_TCSMSG_SERVER_REQ(char* pBuff, char* ip)
{
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szXmlData,"GET /catalogs/FUS-TCSMSG/recommended/0\r\nHost: %s\r\n\r\n", ip);
	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_FILE_SAVE_REQ(char* pBuff, long long msgid, int ctn_id, int idx)
{
	char szTemp[MAX_BUFF], szTemp2[MAX_BUFF];
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	strcpy(szXmlData, XML_HEADER);
	sprintf(szTemp, XML_MAS_S, "req_storage");
	strcat(szXmlData, szTemp);

	memset(szTemp2, 0, MAX_BUFF);
	memset(szTemp, 0, MAX_BUFF);
	sprintf(szTemp2, "%d_%d.jpg", ctn_id, idx+1);
	sprintf(szTemp, XML_FILENAME, szTemp2);
	strcat(szXmlData, szTemp);

	memset(szTemp2, 0, MAX_BUFF);
	memset(szTemp, 0, MAX_BUFF);
	sprintf(szTemp2, "%lld", msgid);
	sprintf(szTemp, XML_CUSTOMMSGID, szTemp2);
	strcat(szXmlData, szTemp);

	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_FILE_UPLOAD_FINISH_REQ(char* pBuff, char* FilePath, char* FileSize, char* CustomMessageID)
{
	char szTemp[MAX_BUFF];
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	strcpy(szXmlData, XML_HEADER);
	sprintf(szTemp, XML_MAS_S, "req_finish_upload");
	strcat(szXmlData, szTemp);
	sprintf(szTemp, XML_PATH, FilePath);
	strcat(szXmlData, szTemp);
	sprintf(szTemp, XML_FILESIZE, FileSize);
	strcat(szXmlData, szTemp);
	sprintf(szTemp, XML_CUSTOMMSGID, CustomMessageID);
	strcat(szXmlData, szTemp);

	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_SERVER_TIME_REQ(char* pBuff, char* id)
{
	char szTemp[MAX_BUFF];
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	strcpy(szXmlData, XML_HEADER);
	sprintf(szTemp, XML_MAS_S, "req_auth");
	strcat(szXmlData, szTemp);
	sprintf(szTemp, XML_SPID, id);
	strcat(szXmlData, szTemp);
	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_BIND_REQ(char* pBuff, char* id, char* AuthTicket, int AuthKeyIndex)
{
	char szTemp[MAX_BUFF];
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	strcpy(szXmlData, XML_HEADER);

	sprintf(szTemp, XML_MAS_S, "req_regist");
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_SPID, id);
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_ENDUSERID, id);
	strcat(szXmlData, szTemp);

	SetReplaceCRCF(AuthTicket, strlen(AuthTicket));
	sprintf(szTemp, XML_AUTHTICKET, AuthTicket);
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_AUTHKEY, AuthKeyIndex);
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_VERSION, "1.0.15");
	strcat(szXmlData, szTemp);

	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_LOGOUT_REQ(char* pBuff)
{
	char szTemp[MAX_BUFF];
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	strcpy(szXmlData, XML_HEADER);

	sprintf(szTemp, XML_MAS_S, "req_unregist");
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_REASON, 0);
	strcat(szXmlData, szTemp);

	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_PING_REQ(char* pBuff)
{
	char szTemp[MAX_BUFF];
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	strcpy(szXmlData, XML_HEADER);

	sprintf(szTemp, XML_MAS_S, "req_ping");
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_RESULT, 0);
	strcat(szXmlData, szTemp);

	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_REPORT_ACK(char* pBuff, char* jobid)
{
	char szTemp[MAX_BUFF];
	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	strcpy(szXmlData, XML_HEADER);

	sprintf(szTemp, XML_MAS_S, "res_report");
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_RESULT, 0);
	strcat(szXmlData, szTemp);

	sprintf(szTemp, XML_JOBID, jobid);
	strcat(szXmlData, szTemp);

	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_DELIVER_REQ(char* pBuff, char* KeyData, char* SessionID, vector<string>& vtSend, char* filePath1, char* fileSize1, char* filePath2, char* fileSize2,  char* filePath3, char* fileSize3, int ctn_cnt)
{
	bool isColor=false;
	int nImgCnt = 0;
	char szTemp[MAX_BUFF];
	char SrcData[MAX_BUFF];
	char DstData[MAX_BUFF];
	char MsgData[MAX_BUFF];
	char* utf8;

	memset(szXmlData, 0x00, sizeof(char)*MAX_BUFF);

	vector<string>::iterator itrData;
	itrData = vtSend.begin();

	strcpy(szXmlData, XML_HEADER);

	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, XML_MAS_S, "req_send_message_2");
	strcat(szXmlData, szTemp);

	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, XML_MSGTYPE, 4);
	strcat(szXmlData, szTemp);

	memset(MsgData, 0x00, sizeof(char)*MAX_BUFF);
	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, "%s", (char*)string(*(itrData+6)).c_str());
	//전문내용(일반 LMS, 컬러LMS, 컬러LMS 일 경우 이미지 태그추가
	if (strcmp(filePath1, "") != 0) nImgCnt++;
	if (strcmp(filePath2, "") != 0) nImgCnt++;
	if (strcmp(filePath3, "") != 0) nImgCnt++;

	if (GetLMSText(szTemp, MsgData, &isColor, nImgCnt) < 0)
	{
		sprintf(tmpLog2, "CPacketCtrlKTC::getMsg_DELIVER_REQ() GetLMSText() ERROR MMS_ID : [%s]", (char*)string(*itrData).c_str());
		log2(tmpLog2, 0, 0);
		return -1;
	}

	//LMS : 1, MMS : 3, COLOR : 6
	if (isColor)
	{
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
		sprintf(szTemp, XML_MSGSUBTYPE, 6);
		sprintf(tmpLog2, "isColor : TRUE, MMS_ID : [%s]", (char*)string(*itrData).c_str());
		log2(tmpLog2, 0, 0);
	}
	else
	{
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
		sprintf(szTemp, XML_MSGSUBTYPE, (ctn_cnt > 0) ? 3 : 1);
	}
	strcat(szXmlData, szTemp);
	
	memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);

	sprintf(SrcData, "%s", (char*)string(*(itrData + 2)).c_str());
	//전화번호 하이픈 "-", 공백 " " 제거
	SetReplaceTel(SrcData, 24);
	//
	//SrcData 암호화 필요
	memset(DstData, 0x00, sizeof(char)*MAX_BUFF);
	if (strlen(SrcData) <= 0 || AESandBASE64_Encode(KeyData, SrcData, DstData) < 0)
	{
		sprintf(tmpLog2, "CPacketCtrlKTC::getMsg_DELIVER_REQ() ERROR CALLBACKNUM : [%s], MMS_ID : [%s]", SrcData, (char*)string(*itrData).c_str());
		log2(tmpLog2, 0, 0);
		return -1;
	}
	SetReplaceCRCF(DstData, strlen(DstData));
	//
	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, XML_CALLBACKNUM, DstData);	//회신번호
	strcat(szXmlData, szTemp);

	// 디코드 확인
	//memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);
	//AESandBASE64_Decode(KeyData, DstData, SrcData);
	// sprintf(tmpLog2, "DECODE : [%s]", SrcData);
	// log2(tmpLog2, 0, 0);

	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, XML_CUSTOMMSGID,  (char*)string(*itrData).c_str());
	strcat(szXmlData, szTemp);

	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	//식별코드 202309 추가
	sprintf(szTemp, XML_KISAORIGCODE, (char*)string(*(itrData + 9)).c_str());
	strcat(szXmlData, szTemp);

#if (DEBUG >= 5)	
	sprintf(tmpLog2, "KisaOrigCode : %s", (char*)string(*(itrData + 9)).c_str());
	log2(tmpLog2, 0, 0);
	sprintf(tmpLog2, "%.200s", szTemp);
	log2(tmpLog2, 0, 0);
#endif

	sprintf(szTemp, XML_MESSAGE_S);
	strcat(szXmlData, szTemp);

	sprintf(SrcData, "%s", (char*)string(*(itrData + 3)).c_str());
	//전화번호 하이픈 "-", 공백 " " 제거
	SetReplaceTel(SrcData, 24);

	//
	//SrcData 암호화 필요
	memset(DstData, 0x00, sizeof(char)*MAX_BUFF);
	if (strlen(SrcData) <= 0 || AESandBASE64_Encode(KeyData, SrcData, DstData) < 0)
	{
		sprintf(tmpLog2, "CPacketCtrlKTC::getMsg_DELIVER_REQ() ERROR RECVNUM : [%s], MMS_ID : [%s]", SrcData, (char*)string(*itrData).c_str());
		log2(tmpLog2, 0, 0);
		return -1;
	}
	SetReplaceCRCF(DstData, strlen(DstData));
	//
	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, XML_RECVNUM_SEQ, 1, DstData);		//수신번호
	strcat(szXmlData, szTemp);

	// 디코드 확인
	//memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);
	//AESandBASE64_Decode(KeyData, DstData, SrcData);
	//sprintf(tmpLog2, "DECODE : [%s]", SrcData);
	//log2(tmpLog2, 0, 0);

	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	strcpy(szTemp, (char*)string(*(itrData + 4)).c_str());
	//특수문자 처리
	if(strlen(szTemp)>0)
	{
		SetReplaceEscapeChar(szTemp, strlen(szTemp));

		//szTemp[64] = 0x00;
		utf8 = euckr_to_utf8((const char*)szTemp);
		sprintf(szTemp, XML_SUBJECT, utf8);
		strcat(szXmlData, szTemp);
		free(utf8);
	}
	
	utf8 = (char*)NULL;

	//
	//MsgData 암호화
	memset(DstData, 0x00, sizeof(char)*MAX_BUFF);
	if(strlen(MsgData)>0)
	{
		if (strlen(MsgData) <= 0 || AESandBASE64_Encode(KeyData, MsgData, DstData) < 0)
		{
			sprintf(tmpLog2, "CPacketCtrlKTC::getMsg_DELIVER_REQ() ERROR MSGDATA : [%s], MMS_ID : [%s]", MsgData, (char*)string(*itrData).c_str());
			log2(tmpLog2, 0, 0);	
			return -1;
		}
		SetReplaceCRCF(DstData, strlen(DstData));
	}	
	//
	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, XML_CONTENT, DstData);
	strcat(szXmlData, szTemp);

	// 디코드 확인
	//memset(MsgData, 0x00, sizeof(char)*MAX_BUFF);
	//AESandBASE64_Decode(KeyData, DstData, MsgData);
	//sprintf(tmpLog2, "DECODE : [%s]", MsgData);
	//log2(tmpLog2, 0, 0);

	if (ctn_cnt > 0)
	{
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
		sprintf(szTemp, XML_ATTACHMENT, 1, filePath1);
		strcat(szXmlData, szTemp);
		
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
		sprintf(szTemp, XML_FILESIZE, fileSize1);
		strcat(szXmlData, szTemp);

		if (ctn_cnt > 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			sprintf(szTemp, XML_ATTACHMENT, 2, filePath2);
			strcat(szXmlData, szTemp);
			
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			sprintf(szTemp, XML_FILESIZE, fileSize2);
			strcat(szXmlData, szTemp);
		}
		if (ctn_cnt > 2)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			sprintf(szTemp, XML_ATTACHMENT, 3, filePath3);
			strcat(szXmlData, szTemp);
			
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			sprintf(szTemp, XML_FILESIZE, fileSize3);
			strcat(szXmlData, szTemp);
		}
	}

	memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
	sprintf(szTemp, XML_MESSAGE_E);
	strcat(szXmlData, szTemp);

	strcat(szXmlData, XML_MAS_E);

	memcpy(pBuff, szXmlData, sizeof(char)*MAX_BUFF);

	return strlen(pBuff);
}

int CPacketCtrlKTC::getMsg_FILEUPLOAD_REQ(char* pBuff, char* KeyData, char* SessionID, char* fFilePath, char* fAuthTicket, char* fFileSize, char* FileServerIP, vector<string>& vtSend, vector<string>& vtCtnSend, int idx)
{
	FILE *fp;
	size_t result;
	long nSize=0;
	char szTemp[MAX_CONTENT_SIZE+MAX_BUFF];
	char szTemp2[MAX_CONTENT_SIZE+MAX_BUFF];
	char DstData[MAX_BUFF];
	char *strContent = (char*)NULL;
	char strCtnPath[256];
	char strCtnType[16];

	vector<string>::iterator itrData;
	itrData = vtSend.begin();

	vector<string>::iterator itrCtnData;
	itrCtnData = vtCtnSend.begin();

	//이미지 파일 컨텐츠
	char logMsg[MAX_BUFF];

	sprintf(strCtnPath, "/data/neomms/CNT/%s", (char*)string(*(itrCtnData+(idx*2))).c_str());	// 컨텐츠 경로
	sprintf(strCtnType, "%s", (char*)string(*(itrCtnData+(idx*2+1))).c_str());	//컨텐츠 타입

	//파일 사이즈 구하기
	fp = fopen(strCtnPath, "rb");
	if (fp == NULL)
	{
		sprintf(tmpLog, "CPacketCtrlKTC::getMsg_FILEUPLOAD_REQ() FileOpen ERROR Path : [%s], MMS_ID : [%s]", strCtnPath, (char*)string(*itrData).c_str());
		log2(tmpLog, 0, 0);
		return -1;
	}
	fseek(fp, 0l, SEEK_END);
	nSize = 0;
	nSize = ftell(fp);
	
	//sprintf(logMsg, "mms_id : [%s], ctn_id : [%s], imgPath : [%s] imgSize : [%ld]", (char*)string(*itrData).c_str(), string(*(itrData + 1)).c_str(), strCtnPath, nSize);
	//log2(logMsg, 0, 0);

	//데이터 구하기
	rewind(fp);
	strContent = (char*)malloc(sizeof(char)*nSize+1);
	memset(strContent, 0x00, sizeof(char)*nSize+1);
	result = fread(strContent, sizeof(char), nSize, fp);
	strContent[nSize]='\0';
	fclose(fp);

	if (result != nSize)
	{
		if (strContent) free(strContent);
		sprintf(tmpLog, "CPacketCtrlKTC::getMsg_FILEUPLOAD_REQ() IMG ERROR reading file: [%s], type : [IMG], MMS_ID : [%s]", strCtnPath, (char*)string(*itrData).c_str());
		log2(tmpLog, 0, 0);
		return -1;
	}
	//

	//파일 PATH 디코드
	memset(DstData, 0x00, sizeof(char)*MAX_BUFF);
	if (strlen(fFilePath) <= 0 || AESandBASE64_Decode(KeyData, fFilePath, DstData) < 0)
	{
		sprintf(tmpLog, "CPacketCtrlKTC::getMsg_FILEUPLOAD_REQ() FILEPATH TEXT ERROR : [%s], MMS_ID : [%s]", fFilePath, (char*)string(*itrData).c_str());
		log2(tmpLog, 0, 0);	
		return -1;
	}
	sprintf(tmpLog2, "MMS_ID[%s] CTN_ID[%s] DECODE : [%s]",(char*)string(*itrData).c_str(), string(*(itrData + 1)).c_str(), DstData);
	log2(tmpLog2, 0, 0);

	//파일 저장 위치
	memset(szTemp, 0x00, MAX_CONTENT_SIZE+MAX_BUFF);
	sprintf(szTemp, "POST %s HTTP/1.1\r\n", DstData);
	strcat(szTemp, "Host: ");
	strcat(szTemp, FileServerIP);
	strcat(szTemp, "\r\n");
	strcat(szTemp, "Accept: text/html, *; q=.2, */*; q=.2\r\n");
	strcat(szTemp, "Connection: keep-alive\r\n");
	strcat(szTemp, "Content-type: application/x-www-form-urlencoded\r\n");

	//이미지 사이즈
	sprintf(szTemp2, "Content-Length: %d\r\n", nSize);
	strcat(szTemp, szTemp2);
	
	//파일 AuthTicket
	sprintf(szTemp2, "X-FUS-Authentication: %s\r\n\r\n", fAuthTicket);
	strcat(szTemp, szTemp2);

	//파일 binary
	int header_len = strlen(szTemp);

	memcpy(szTemp+header_len, strContent, nSize);

	memcpy(pBuff, szTemp, header_len+nSize);

	sprintf(fFileSize, "%d", nSize);

	if (strContent) free(strContent);
	strContent = (char*)NULL;

	return header_len+nSize;
}

int CPacketCtrlKTC::GetLMSText(const char *TxtPath, char* TxtData, bool *isColor, int nImgCnt)
{
	// @brief < LMS 내용 텍스트 구하기
	FILE *fp;

	size_t result;
	bool isLMS=false;

	long nSize=0;

	char *strContent = (char*)NULL;
	char strColorContent[MAX_BUFF];
	char DstData[MAX_BUFF];
	char strCtnPath[256];

	if (strcmp(TxtPath, "") == 0)
	{
		sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() TxtPath NOT FOUND ERROR Path : [%s], type : [TXT]", TxtPath);
		log2(tmpLog, 0, 0);
		return 0;
	}

	//컨텐츠 데이터 작업
	//텍스트 파일 컨텐츠

	char tmpPath[50];
	memset(tmpPath,0x00,sizeof(char)*50);
	strcpy(tmpPath, TxtPath);
	strncpy (tmpPath,"TXT_SSN",7);
	sprintf(strCtnPath, "/data/neomms/CNT/%s",tmpPath);	// TXT_PATH

	//파일 사이즈 구하기
	fp = fopen(strCtnPath, "rb");
	if (fp == NULL)
	{
		sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() FileOpen ERROR Path : [%s], type : [TXT]", strCtnPath);
		log2(tmpLog, 0, 0);
		return 0;
	}
	fseek(fp, 0l, SEEK_END);
	nSize = ftell(fp);
	//

	//데이터 구하기
	rewind(fp);
	strContent = (char*)malloc(sizeof(char)*nSize+1);
	memset(strContent, 0x00, sizeof(char)*(int)nSize+1);
	result = fread(strContent, sizeof(char), nSize, fp);
	strContent[nSize]='\0';
	fclose(fp);
	if (result != nSize)
	{
		if (strContent) free(strContent);
		sprintf(tmpLog, "CPacketCtrlKTC::GetLMSText() TXT ERROR[reading file: %s]", strCtnPath);
		log2(tmpLog, 0, 0);
		return 0;
	}
	//

	//컬러문자인지 확인 후 컬러문자 이면 KTC 용 컬러문자 템플릿으로 변경.2012.10.22 by han
	if ((strstr(strContent, "<#Y>") && strstr(strContent, "</#Y>")) || 
		(strstr(strContent, "<#R>") && strstr(strContent, "</#R>")) || 
		(strstr(strContent, "<#B>") && strstr(strContent, "</#B>")) || 
		(strstr(strContent, "<#G>") && strstr(strContent, "</#G>")))
	{
		memset(strColorContent, 0x00, MAX_BUFF);
		strcat(strColorContent, HTML_S);
		strcat(strColorContent, HTML_HEAD_S);
		strcat(strColorContent, HTML_DEFINE_S);
		strcat(strColorContent, HTML_COLOR_RED);
		strcat(strColorContent, HTML_COLOR_GREEN);
		strcat(strColorContent, HTML_COLOR_BLUE);
		strcat(strColorContent, HTML_COLOR_YELLOW);
		for (int i=1; i<=nImgCnt; i++)
		{//이미지 태그 삽입
			char tmp[256];
			memset(tmp, 0x00, 256);
			sprintf(tmp, HTML_IMAGE, i, i);
			strcat(strColorContent, tmp);
		}
		strcat(strColorContent, HTML_DEFINE_E);
		strcat(strColorContent, HTML_HEAD_E);
		strcat(strColorContent, HTML_BODY_S);

		memset(DstData, 0x00, MAX_BUFF);
		for (int i=1; i<=nImgCnt; i++)
		{//이미지 태그 삽입
			char tmp[256];
			memset(tmp, 0x00, 256);
			sprintf(tmp, HTML_IMAGE_NO, i, i);
			strcat(strColorContent, tmp);
		}
		//컬러문자 태그 변환
		GetColorLMS(strContent, DstData);
		//
		strcat(strColorContent, DstData);

		strcat(strColorContent, HTML_BODY_E);
		strcat(strColorContent, HTML_E);

		memcpy(TxtData, strColorContent, strlen(strColorContent));
		FILE *fp;
		
		fp = fopen("/data/log/20130516.txt", "a+");
		
		fprintf(fp, "%s", TxtData);
		
		fclose(fp);
		*isColor = true;
	}
	else
	{
		memcpy(TxtData, strContent, strlen(strContent));
	}
	//
	if (strContent) free(strContent);

	//
	return 0;
}

void CPacketCtrlKTC::GetColorLMS(char *SrcData, char *DstData)
{
	int nLen = 0;
	int i = 0, j = 0;
	char str[MAX_BUFF];
	memset(str, 0x00, MAX_BUFF);
	strcpy(str, SrcData);
	nLen = strlen(str);

	//KTC 형식으로 C1,C2,C3,C4 설정
	//RED -> C1, GREEN -> C2, BLUE -> C3, YELLOW -> C4
	for (i=0; i<nLen;)
	{
		if ((str[i-1]=='<' && str[i]=='#' && str[i+1]=='R' && str[i+2]=='>') ||					//<#R>
			(str[i-2]=='<' && str[i-1]=='/' && str[i]=='#' && str[i+1]=='R' && str[i+2]=='>'))	//</#R>
		{
			str[i++]='C';
			str[i++]='1';
			continue;
		}
		if (str[i-1]=='<' && str[i]=='#' && str[i+1]=='G' && str[i+2]=='>' ||					//<#G>
			(str[i-2]=='<' && str[i-1]=='/' && str[i]=='#' && str[i+1]=='G' && str[i+2]=='>'))	//</#G>
		{
			str[i++]='C';
			str[i++]='2';
			continue;
		}
		if (str[i-1]=='<' && str[i]=='#' && str[i+1]=='B' && str[i+2]=='>' ||					//<#B>
			(str[i-2]=='<' && str[i-1]=='/' && str[i]=='#' && str[i+1]=='B' && str[i+2]=='>'))	//</#B>
		{
			str[i++]='C';
			str[i++]='3';
			continue;
		}
		if (str[i-1]=='<' && str[i]=='#' && str[i+1]=='Y' && str[i+2]=='>' ||					//<#Y>
			(str[i-2]=='<' && str[i-1]=='/' && str[i]=='#' && str[i+1]=='Y' && str[i+2]=='>'))	//</#Y>
		{
			str[i++]='C';
			str[i++]='4';
			continue;
		}
		i++;
	}

	//\n -> <br/> 변환
	for (i=0, j=0; i<nLen;)
	{
		//\n\n이 두개이면 <br><br>로 개행이 두줄 처리가 되지않아
		//<C1></C1><br>로 입력하여 개행 처리가 되도록 함.2012.10.26.by han
		
		if (str[i] == '\r' && str[i+1] == '\n')
		{
			DstData[j++] = '<';
			DstData[j++] = 'b';
			DstData[j++] = 'r';
			DstData[j++] = '>';
			DstData[j++] = '\r';
			DstData[j++] = '\n';
			i++;
			i++;
			continue;
		}
		else if (str[i] == '\n' && str[i+1] == '\n')
		{
			DstData[j++] = '<';
			DstData[j++] = 'b';
			DstData[j++] = 'r';
			DstData[j++] = '>';
			DstData[j++] = '\r';
			DstData[j++] = '\n';
			DstData[j++] = '<';
			DstData[j++] = 'C';
			DstData[j++] = '1';
			DstData[j++] = '>';
			DstData[j++] = '<';
			DstData[j++] = '/';
			DstData[j++] = 'C';
			DstData[j++] = '1';
			DstData[j++] = '>';
			i++;
			continue;
		}
		else if(str[i] == '\n')
		{
			DstData[j++] = '<';
			DstData[j++] = 'b';
			DstData[j++] = 'r';
			DstData[j++] = '>';
			DstData[j++] = '\r';
			DstData[j++] = '\n';
			i++;
			continue;
		}
		DstData[j++]=str[i++];
	}
}

int CPacketCtrlKTC::getData_ServerReqAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Category", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "ResourceID", szTemp);
			vtData.push_back(szTemp);
		}

		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Address", szTemp);
			vtData.push_back(szTemp);
		}

		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Port", szTemp);
			vtData.push_back(szTemp);
		}

		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_ServerTimeAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Time", szTemp);
			vtData.push_back(szTemp);
		}

		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_BindAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;
		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);
		if(reader != NULL)
		{
               	ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SessionID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerSecond", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerSecond", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerSecond", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerSecond", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "ProductStatus", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "ProductStatus", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "ProductStatus", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "ProductStatus", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerMonth", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerMonth", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerMonth", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendLimitPerMonth", szTemp);
			vtData.push_back(szTemp);
		}
		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_FileSaveAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "AuthTicket", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Path", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "CustomMessageID", szTemp);
			vtData.push_back(szTemp);
		}
		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_FileUploadAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		char * pch;
		char szXML[MAX_BUFF];
		pch = strchr (pBuff,'<');

		memset(szXML, 0x00, MAX_BUFF);
		sprintf(szXML, "%s", pch);

		reader = xmlReaderForMemory(szXML, strlen(szXML),NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Path", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Message", szTemp);
			vtData.push_back(szTemp);
		}
		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_FinishUploadAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "CustomMessageID", szTemp);
			vtData.push_back(szTemp);
		}
		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_LogoutAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}

		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_SndAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Time", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "CustomMessageID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SequenceNumber", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "JobID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "GroupID", szTemp);
			vtData.push_back(szTemp);
		}

		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_SndAllAck(char* pBuff, vector<string>& vtData)
{
	char szTemp[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Time", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "CustomMessageID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Count", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "GroupID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1 && xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "JobID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SubmitTime", szTemp);
			vtData.push_back(szTemp);
		}

		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getData_Report(char* pBuff, vector<string>& vtData, char* KeyData)
{
	char szTemp[MAX_BUFF], SrcData[MAX_BUFF];
	try {
		memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);

		xmlTextReaderPtr    reader;
		int                 ret;

		reader = xmlReaderForMemory(pBuff, strlen(pBuff), NULL, NULL, 0);

		if(reader != NULL)
		{
			ret = xmlTextReaderRead(reader);
		}
		if (ret == 1)
		{
			processNode(reader, "ServiceProviderID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "EndUserID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Result", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Time", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "CustomMessageID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "JobID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "GroupID", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SequenceNumber", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "MessageType", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SendNumber", szTemp);			//발신번호
			//
			//SrcData 암호화 필요
			if (strcmp(szTemp, "-") != 0)
			{
				memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);
				sprintf(SrcData, "%s", szTemp);
				memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
				if (strlen(SrcData) <= 0 || AESandBASE64_Decode(KeyData, SrcData, szTemp) < 0)
				{
					sprintf(tmpLog2, "CPacketCtrlKTC::getData_Report() ERROR SendNumber : [%s]", SrcData);
					log2(tmpLog2, 0, 0);				
					return -1;
				}
			}
			//
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "ReceiveNumber", szTemp);		//수신번호
			//
			//SrcData 암호화 필요
			if (strcmp(szTemp, "-") != 0)
			{
				memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);
				sprintf(SrcData, "%s", szTemp);
				memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
				if (strlen(SrcData) <= 0 || AESandBASE64_Decode(KeyData, SrcData, szTemp) < 0)
				{
					sprintf(tmpLog2, "CPacketCtrlKTC::getData_Report() ERROR ReceiveNumber : [%s]", SrcData);
					log2(tmpLog2, 0, 0);				
					return -1;
				}
			}
			//
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "CallbackNumber", szTemp);		//콜백번호
			//
			//SrcData 암호화 필요
			if (strcmp(szTemp, "-") != 0)
			{
				memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);
				sprintf(SrcData, "%s", szTemp);
				memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
				if (strlen(SrcData) <= 0 || AESandBASE64_Decode(KeyData, SrcData, szTemp) < 0)
				{
					sprintf(tmpLog2, "CPacketCtrlKTC::getData_Report() ERROR CallbackNumber : [%s]", SrcData);
					log2(tmpLog2, 0, 0);					
					return -1;
				}
			}
			//
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "TelcoInfo", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "Fee", szTemp);
			vtData.push_back(szTemp);
		}
		if (xmlTextReaderRead(reader) == 1)
		{
			memset(szTemp, 0x00, sizeof(char)*MAX_BUFF);
			processNode(reader, "SubmitTime", szTemp);
			vtData.push_back(szTemp);
		}
		xmlFreeTextReader(reader);
		xmlCleanupParser();
		xmlMemoryDump();
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlKTC::getMsgCode(char* pBuff)
{
	int nType = -1;
	char strMethod[24];

	memset(strMethod, 0x00, 24);

	char * pch;
	char szXML[MAX_BUFF];
	pch = strchr (pBuff,'<');

	memset(szXML, 0x00, MAX_BUFF);
	sprintf(szXML, "%s", pch);

	streamXmlTitle(szXML, strMethod);
	//printf("\nstrMethod : %s\n", strMethod);

	try {
		if (strcmp(strMethod, "ressvr_res") == 0)	//서버 요청 RESPONSE
			nType = TYPE_SERVER_REQ_ACK;
		if (strcmp(strMethod,"res_auth") == 0)	//서버 시간 요청 RESPONSE
			nType = TYPE_SERVER_TIME_ACK;
		if (strcmp(strMethod,"res_regist") == 0)	//서버 로그인 요청 RESPONSE
			nType = TYPE_BIND_ACK;
		if (strcmp(strMethod,"res_unregist") == 0)	//서버 로그아웃 요청 RESPONSE
			nType = TYPE_LOGOUT_ACK;
		if (strcmp(strMethod,"res_ping") == 0)	//PING 응답 RESPONSE
			nType = TYPE_PONG;
		if (strcmp(strMethod,"req_report") == 0)	//레포트 응답 RESPONSE
			nType = TYPE_REPORT;
		if (strcmp(strMethod,"res_send_message") == 0)	//전문 응답 RESPONSE
			nType = TYPE_DELIVER_ACK;
		if (strcmp(strMethod,"res_send_message_all") == 0)	//전문 응답 RESPONSE
			nType = TYPE_DELIVER_ALL_ACK;
		if (strcmp(strMethod,"res_storage") == 0)	//파일 저장 위치 응답 RESPONSE
			nType = TYPE_FILE_SAVE_ACK;
		if (strcmp(strMethod,"POST") == 0)	//파일 업로드 응답 RESPONSE
			nType = TYPE_FILE_UPLOAD_ACK;
		if (strcmp(strMethod,"res_finish_upload") == 0)	//파일 업로드 응답 RESPONSE
			nType = TYPE_FINISH_UPLOAD_ACK;

	}
	catch (...) {
		nType = -1;
	}
	return nType;
}

void log2(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log2 ERROR. %d %s\n", "PacketCtrlKTC_MMS", 0, "");
	}
}

char* CPacketCtrlKTC::euckr_to_utf8( const char* str)
{

	iconv_t cd;
	char* outbuf,* out;

	size_t ileft, oleft;

	int err;
	int len;

	len = strlen( str );

	ileft = len;
//	oleft = 64;
//
//	out = outbuf = (char*) malloc( 64 + 1 );
//	memset(out, 0x00, 64+1);
//	memset(outbuf, 0x00, 64+1);
	
	/*
	 * 오류현상 : 69byte 문자열이 변환하면서 60byte로 축소되는 현상발생
	 * 수정사항
	 * KTC 전문상 64byte 이나 
	 * KSKYB SUBJECT항목이 20byte제한이므로 128byte로 설정해도 문제 없음
	 */
	oleft = 128;

	out = outbuf = (char*) malloc( 128 + 1 );
	memset(out, 0x00, 128+1);
	memset(outbuf, 0x00, 128+1);

	cd = iconv_open( "UTF8", "EUC-KR" );

	err = iconv( cd, (char**)&str, &ileft, &outbuf, &oleft );
	iconv_close( cd );

	return out;

}
