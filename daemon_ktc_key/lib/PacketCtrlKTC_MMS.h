/*
 * PacketCtrlKTC.h
 *
 *  Created on: 2011. 07. 27.
 *      Author: Administrator
 */

#ifndef PacketCtrlKTC_H_
#define PacketCtrlKTC_H_

#include <string>
#include <vector>
#include <arpa/inet.h>
#include <iostream>
using namespace std;
#include <ml_ctrlsub.h>
#include <stdio.h>
#include "xmlParser.h"

#define	MAX_CONTENT_SIZE	1024*640*2
#define MAX_TCP_BUF 		((MAX_CONTENT_SIZE)+1024)
#define MAX_BUFF			4096
#define TMP_BUFF			1024

typedef struct tag_resultTable {
    char tCode[8];
    char kCode[8];
    char desc[512];
} CODETABLE;

class CPacketCtrlKTC
{
public:
	enum logLevels {
		TYPE_SERVER_REQ_ACK = 2,	/* 실, 파일 서버 요청 RESPONSE */
		TYPE_SERVER_TIME_ACK,		/* 서버 시간 RESPONSE */
		TYPE_BIND_ACK,				/* Bind 응답 */
		TYPE_LOGOUT_ACK,			/* LOGOUT */
		TYPE_PONG,					/* PONG */
		TYPE_REPORT,				/* REPORT 수신 */
		TYPE_DELIVER_ACK,			/* 전문 응답 수신 */
		TYPE_DELIVER_ALL_ACK,		/* 전문 응답 수신 */
		TYPE_FILE_FINISH_ACK,		/* 파일전송 완료 수신 */
		TYPE_FILE_SAVE_ACK,			/* 파일저장 요청 수신 */
		TYPE_FILE_UPLOAD_ACK,		/* 파일업로드 요청 수신 */
		TYPE_FINISH_UPLOAD_ACK		/* 파일업로드 완료 수신 */
	};

   CPacketCtrlKTC() {};
	virtual ~CPacketCtrlKTC() {};

	void SetReplaceTel(char* pSource, int nSourceSize);
	void SetReplaceCRCF(char* pSource, int nSourceSize);
	void SetReplaceEscapeChar(char* pSource, int nLen);
	char* euckr_to_utf8( const char* str);

	//Packet Msg 생성
	int getMsg_MAS_SERVER_REQ(char* pBuff, char* ip);
	int getMsg_FUS_TCSMSG_SERVER_REQ(char* pBuff, char* ip);
	//int getMsg_FILE_SAVE_REQ(char* pBuff, int msgid, int ctn_id, int idx);
	int getMsg_FILE_SAVE_REQ(char* pBuff, long long msgid, int ctn_id, int idx);

	int getMsg_FILE_UPLOAD_FINISH_REQ(char* pBuff, char* FilePath, char* FileSize, char* CustomMessageID);

	int getMsg_SERVER_TIME_REQ(char* pBuff, char* id);
	int getMsg_BIND_REQ(char* pBuff, char* id, char* AuthTicket, int AuthKeyIndex);
	int getMsg_LOGOUT_REQ(char* pBuff);
	int getMsg_PING_REQ(char* pBuff);
	int getMsg_PONG_RES(char* pBuff);
	int getMsg_DELIVER_REQ(char* pBuff, char* KeyData, char* SessionID, vector<string>& vtSend, char* filePath1, char* fileSize1, char* filePath2, char* fileSize2, char* filePath3, char* fileSize3, int ctn_cnt);
	int getMsg_FILEUPLOAD_REQ(char* pBuff, char* KeyData, char* SessionID, char* fFilePath, char* fAuthTicket, char* fFileSize, char* FileServerIP,
			vector<string>& vtSend, vector<string>& vtCtnSend, int ctn_cnt);

	int getMsg_REPORT_ACK(char* pBuff, char* msgid);
	//
	void GetColorLMS(char *SrcData, char* DstData);
	int GetLMSText(const char *TxtPath, char* TxtData, bool *isColor, int nImgCnt);

	//Ack 받은 데이터 입력
	int getData_ServerReqAck(char* pBuff, vector<string>& vtDataAck);
	int getData_ServerTimeAck(char* pBuff, vector<string>& vtDataAck);
	int getData_BindAck(char* pBuff, vector<string>& vtDataAck);
	int getData_LogoutAck(char* pBuff, vector<string>& vtDataAck);
	int getData_SndAck(char* pBuff, vector<string>& vtDataAck);
	int getData_SndAllAck(char* pBuff, vector<string>& vtDataAck);
	int getData_FileSaveAck(char* pBuff, vector<string>& vtDataAck);
	int getData_FileUploadAck(char* pBuff, vector<string>& vtData);
	int getData_FinishUploadAck(char* pBuff, vector<string>& vtDataAck);
	//

	//Server(KTC) -> Client(KSKYB) 데이터 입력
	int getData_Report(char* pBuff, vector<string>& vtReport, char* KeyData);
	int getData_Control(char* pBuff, vector<string>& vtControl);
	//

	int getMsgCode(char* pBuff);
};

#endif /* PacketCtrlKTC_H_ */
