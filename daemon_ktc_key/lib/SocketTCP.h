/*
 * SocketTCP.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef SOCKETTCP_H_
#define SOCKETTCP_H_

#include <sys/socket.h>
#include <sys/types.h>
#include <fcntl.h>
#include <arpa/inet.h>
#include <unistd.h>

namespace KSKYB
{

class CSocketTCP
{
protected:
	void setSocketId(int socketFd)
	{
		m_nSocketId = socketFd;
	}

public:
	CSocketTCP() {};
	CSocketTCP(int);
	virtual ~CSocketTCP();

	void setDebug(int);
	void setReuseAddr(int);
	void setKeepAlive(int);
	void setLingerOnOff(bool);
	void setLingerSeconds(int);
	void setLingerSeconds(int,int);
	void setSocketBlocking(int);
	void setSendBufSize(int);
	void setReceiveBufSize(int);
	int getSocketId()
	{
		return m_nSocketId;
	}
	int getPortNumber()
	{
		return m_nPortNumb;
	}
	int sendMessage(char*,int);
	int recieveMessage(char*);
	int checkSelect(int);
	void bindSocket();
	void listenToClient(int);
	CSocketTCP* acceptClient();
	void connectToServer(char*);
	void Wait_A_Moment(int nSec, int nUsc);
	void SocketClose();
	int EndSeparator(char *sBuff);

private:
	int m_nPortNumb; // Socket port number
	int m_nSocketId; // Socket file descriptor
	int m_nBlocking; // Blocking flag
	int m_nBindFlag; // Binding flag
	struct sockaddr_in m_ClientAddr; // Address of the client that sent data
};

}

#endif /* SOCKETTCP_H_ */
