/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <vector>
using namespace std;
#include <ml_ctrlsub.h>

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int setEnableThreads();
	int initThread(sql_context& ctx);
	int freeThread(sql_context& ctx);
	int connectToOracle(sql_context ctx, char*, char*);
	int closeFromOracle(sql_context ctx);
	int setSndAckData(int telcoid, sql_context ctx, vector<string>& vtSndAck);
	//int setReportData(sql_context ctx, vector<string>& vtReport);
	int setReportData(int telcoid, sql_context ctx, vector<string>& vtReport);
	int setReportDataUsingSndAck(int telcoid, sql_context ctx, vector<string>& vtSndAck);
	//int getMsgData(sql_context ctx, char *q_name, vector<string>& vtSend);
	long long getMsgData(sql_context ctx, char *q_name, vector<string>& vtSend);
	int getCtnData(sql_context ctx, int cid, vector<string>& vtCtnData);
	//int _putMsgRetryDB(int mmsid, char *q_name, int telcoid, sql_context ctx);
	int _putMsgRetryDB(long long mmsid, char *q_name, int telcoid, sql_context ctx);

	void log3(char *buf, int st, int err);
	void get_timestring(char *fmt, long n, char *s);
	char* trim(char* szOrg, int leng);
	

private:
	bool m_bThread;
	char tmpLog3[1024];
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
};

}

#endif /* DATABASEORA_H_ */
