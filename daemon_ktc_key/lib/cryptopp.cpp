#include "cryptopp.h"
using namespace std;
using namespace CryptoPP;

void hex2byte(const char *in, uint len, byte *out)
{
     for (uint i = 0; i < len; i+=2) {
           char c0 = in[i+0];
           char c1 = in[i+1];
           byte c = (
              ((c0 & 0x40 ? (c0 & 0x20 ? c0-0x57 : c0-0x37) : c0-0x30)<<4) |
              ((c1 & 0x40 ? (c1 & 0x20 ? c1-0x57 : c1-0x37) : c1-0x30))
            );
           out[i/2] = c;
     }
}

int AESandBASE64_Encode(char *rawKey, char *SrcData, char *DstData)
{

	if (*rawKey == NULL || rawKey == NULL) return -1;
	if (*SrcData == NULL || SrcData == NULL) return -1;
    //
    // 키 할당
    //
    byte key[AES::DEFAULT_KEYLENGTH];
    memset(key, 0x00, AES::DEFAULT_KEYLENGTH );
    //char* rawKey="60EE738174BDD375D6C2D0F8E2105362CBD989FF";
    hex2byte(rawKey, strlen(rawKey), key);

    // IV 할당
    byte iv[AES::BLOCKSIZE];
    memset(iv, 0x00, AES::BLOCKSIZE );
    char *rawIv = "0000000000000000";

    hex2byte(rawIv, strlen(rawIv), iv);
    //
    // 평문 할당
    //
    string plaintext;
    string ciphertext;
    string base64encodedciphertext;
    string decryptedtext;
    string base64decryptedciphertext;

	plaintext = SrcData;

    //
    // 평문 출력
    //
    // cout << "Plain Text (" << plaintext.size() << " bytes)" << endl;
    // cout << plaintext;
    // cout << endl << endl;

    unsigned int plainTextLength = plaintext.length();

    //
    // AES 암호화 수행
    //
    AES::Encryption
       aesEncryption(key, AES::DEFAULT_KEYLENGTH);
    CBC_Mode_ExternalCipher::Encryption
       cbcEncryption(aesEncryption, iv);

    StreamTransformationFilter
       stfEncryptor(cbcEncryption, new StringSink( ciphertext));
    stfEncryptor.Put(reinterpret_cast<const unsigned char*>
       (plaintext.c_str()), plainTextLength + 1);
    stfEncryptor.MessageEnd();

    //
    // Base64 인코딩
    //
    StringSource(ciphertext, true,
              new Base64Encoder(
                 new StringSink(base64encodedciphertext)
               ) // Base64Encoder
    ); // StringSource
    //
    // Base64 인코딩 문자열 출력
    //
    // cout << "Cipher Text (" << base64encodedciphertext.size() << " bytes)" << endl;
    // cout << "cipher : " << base64encodedciphertext << endl;
    // cout << endl << endl;

	//
	memcpy(DstData, base64encodedciphertext.c_str(), base64encodedciphertext.length());

    return 0;
}

int AESandBASE64_Decode(char *rawKey, char *SrcData, char *DstData)
{
	if (*rawKey == NULL || rawKey == NULL) return -1;
	if (*SrcData == NULL || SrcData == NULL) return -1;
    //
    // 키 할당
    //
    byte key[AES::DEFAULT_KEYLENGTH];
    memset(key, 0x00, AES::DEFAULT_KEYLENGTH );
    //char* rawKey="60EE738174BDD375D6C2D0F8E2105362CBD989FF";
    hex2byte(rawKey, strlen(rawKey), key);
    // IV 할당
    byte iv[AES::BLOCKSIZE];
    memset(iv, 0x00, AES::BLOCKSIZE );
    char* rawIv="0000000000000000";
    hex2byte(rawIv, strlen(rawIv), iv);
    //
    // 평문 할당
    //
    string plaintext;
    string ciphertext;
    string base64encodedciphertext;
    string decryptedtext;
    string base64decryptedciphertext;

	base64encodedciphertext = SrcData;
    //
    // Base64 디코딩
    //
    StringSource(base64encodedciphertext, true,
         new Base64Decoder(
             new StringSink( base64decryptedciphertext)
         ) // Base64Encoder
    ); // StringSource

    //
    // AES 복호화
    //
    AES::Decryption aesDecryption(key,
       AES::DEFAULT_KEYLENGTH);
    CBC_Mode_ExternalCipher::Decryption
       cbcDecryption(aesDecryption, iv );

    StreamTransformationFilter
       stfDecryptor(cbcDecryption, new StringSink(decryptedtext));
    stfDecryptor.Put( reinterpret_cast<const unsigned char*>
       (base64decryptedciphertext.c_str()), base64decryptedciphertext.size());
    stfDecryptor.MessageEnd();

    //
    // 복호화 문자열 출력
    //
    // cout << "Decrypted Text: " << endl;
    // cout << decryptedtext;
    // cout << endl << endl;

	memcpy(DstData, decryptedtext.c_str(), decryptedtext.length());

	return 0;
}
