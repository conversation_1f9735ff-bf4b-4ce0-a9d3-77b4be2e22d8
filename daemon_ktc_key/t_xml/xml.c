#include <stdio.h>
#include <libxml2/libxml/xmlreader.h>

#ifdef  LIBXML_READER_ENABLED
static void
processNode(xmlTextReaderPtr    reader)
{
    const xmlChar   *name, *value;

    name    =   xmlTextReaderConstName(reader);

    if(name == NULL)
    {
        printf("TEST TEST TES\n");
        name = BAD_CAST "--";
    }

    value = xmlTextReaderConstValue(reader);

    printf("%d %d %s %d %d",
            xmlTextReaderDepth(reader),
            xmlTextReaderNodeType(reader),
            name,
            xmlTextReaderIsEmptyElement(reader),
            xmlTextReaderHasValue(reader));


    if(value == NULL)
        printf("\n");

    else {
        if(xmlStrlen(value) > 40)
            printf(" %.40s...\n", value);
        else
            printf(" %s\n", value);
    }

    if(xmlTextReaderHasAttributes(reader))
    {
        int     nLoop = 0;
        do
        {
            if((value = xmlTextReaderGetAttributeNo(reader, nLoop)) == NULL)
                break;
            printf("Attribute Value %d : %s\n", nLoop, value);
        } while(++nLoop);
    }
}

static void
streamFile(const char *filename)
{
    xmlTextReaderPtr    reader;
    int                 ret;
	char xml_sample[1000];
	
	sprintf(xml_sample,"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\
<MAS method=\"res_regist\">\
  <Result>5</Result>\
  <SessionID>0</SessionID>\
  <SendLimitPerSecond msgType=\"1\">0</SendLimitPerSecond>\
  <SendLimitPerSecond msgType=\"2\">0</SendLimitPerSecond>\
  <SendLimitPerSecond msgType=\"3\">0</SendLimitPerSecond>\
  <SendLimitPerSecond msgType=\"4\">0</SendLimitPerSecond>\
  <ProductStatus msgType=\"1\">Y</ProductStatus>\
  <ProductStatus msgType=\"2\">Y</ProductStatus>\
  <ProductStatus msgType=\"3\">Y</ProductStatus>\
  <ProductStatus msgType=\"4\">Y</ProductStatus>\
  <SendLimitPerMonth msgType=\"1\">0</SendLimitPerMonth>\
  <SendLimitPerMonth msgType=\"2\">0</SendLimitPerMonth>\
  <SendLimitPerMonth msgType=\"3\">0</SendLimitPerMonth>\
  <SendLimitPerMonth msgType=\"4\">0</SendLimitPerMonth>\
</MAS>\
");
    reader = xmlReaderForMemory(xml_sample,strlen(xml_sample), NULL,NULL, 0);

    printf("%s %d\n", __func__, __LINE__);

    if(reader != NULL)
    {

    printf("%s %d\n", __func__, __LINE__);

        ret = xmlTextReaderRead(reader);
        while(ret == 1)
        {
            processNode(reader);

            ret = xmlTextReaderRead(reader);
        }

        xmlFreeTextReader(reader);
    printf("%s %d\n", __func__, __LINE__);

        if(ret != 0)
        {
            printf("%s : failed to parse\n", filename);
        }
    }
    else
    {
        printf("Unable to open %s\n", filename);
    }
}

int main(int argc, char **argv)
{
    streamFile(argv[1]);
    xmlCleanupParser();
    xmlMemoryDump();

    return 0;
}
#else
int main(void)
{
    printf("XInclude support not compiled in \n");
    exit(1);
}

#endif
