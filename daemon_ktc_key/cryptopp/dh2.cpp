// dh2.cpp - written and placed in the public domain by <PERSON>

#include "pch.h"
#include "dh2.h"

NAMESPACE_BEGIN(CryptoPP)

void DH2_TestInstantiations()
{
	DH2 dh(*(SimpleKeyAgreementDomain*)NULL);
}

bool DH2::Agree(byte *agreedValue,
		const byte *staticSecretKey, const byte *ephemeralSecret<PERSON>ey, 
		const byte *staticOtherPublicKey, const byte *ephemeralOtherPublic<PERSON>ey,
		bool validateStaticOtherPublicKey) const
{
	return d1.Agree(agreedValue, staticSecretKey, staticOtherPublic<PERSON>ey, validateStaticOtherPublicKey)
		&& d2.Agree(agreedValue+d1.AgreedValueLength(), ephemeralSecretKey, ephemeralOtherPublicKey, true);
}

NAMESPACE_END
