/*
 * telco_ktc_new_mms.h
 *
 *  Created on: 2011. 07. 27.
 *      Author: Administrator
 */

#include <iostream>
using namespace std;
#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include "Properties.h"
#include "SocketTCP.h"
#include "myException.h"

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

// > brief base64 include
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

// > brief sha1
#include "sha1.h"
#include "cryptopp.h"

#include "PacketCtrlKTC_MMS.h"
#include "DatabaseORA_MMS.h"

#define	MAX_CONTENT_SIZE	1024*640*2
#define MAX_TCP_BUF 		((MAX_CONTENT_SIZE)+1024)
#define MAX_BUFF			4096
#define TMP_BUFF			1024

#define SKT_TELCO_ID		1
#define SKB_TELCO_ID		21
#define TELCO_ID			25

typedef struct _THREAD_PARAM
{
	int sockfd;
	pthread_t tid;
	sql_context ctx;
	time_t sThisT, sLastT;
	time_t rThisT, rLastT;
	char buff[MAX_TCP_BUF];
	char logMsg[TMP_BUFF];

	char mas_ServerTime[14+1];
	char mas_AuthTicket[TMP_BUFF];
	char mas_AuthKey[128];
	int  mas_AuthKeyIndex;
	char mas_SessionID[12];
	char mas_OneTimeKey[40+1];

	char jobid[24];

	char fus_AuthTicket[TMP_BUFF];
	char fus_CustomMessageID[TMP_BUFF];
	char fus_FilePath[TMP_BUFF];
	char fus_FilePath1[TMP_BUFF];
	char fus_FileSize1[12];
	char fus_FilePath2[TMP_BUFF];
	char fus_FileSize2[12];
	char fus_FilePath3[TMP_BUFF];
	char fus_FileSize3[12];

} ThreadParam;

CODETABLE* pstCodeTable = NULL; /**< 각 해당 이통사 에러 코드 설명 테이블 */
int nCntCode = 0; /**< 에러 코드 테이블의 총 Row */
char mas_ServerIP[16+1];		//실 서버 아이피
char mas_ServerPORT[5+1];		//실 서버 포트
char fus_ServerIP[16+1];		//파일업로드 서버 아이피
char fus_ServerPORT[5+1];		//파일업로드 서버 포트
char ServerCategory[12];		//요청 시 실서버,파일업로드 구분
char fus_OneTimeKey[40+1];
int nTelcoNo = 0;

vector<string> vtBuff;
vector<string> vtFileBuff;
vector<string> vtCtnBuff;
vector<string> vtReportBuff;
vector<string> vtDeliverBuff;

KSKYB::CProperties g_prop;
KSKYB::CDatabaseORA g_oracle;
int activeProcess = true;
char PROCESS_NO[ 7], PROCESS_NAME[36];
struct _message_info message_info;
struct _shm_info *shm_info;

//MAS 서버 정보 요청
int MAS_SERVER_REQ(void* param);
//FUS-TCSMSG(MMS) 서버 정보 요청
int FUS_TCSMSG_SERVER_REQ(void* param);
void* procSendRept(void* param);
int CheckThreadStatus(ThreadParam** param, int nCnt);
int BindGateway(ThreadParam* tp, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst);
int ClassifyResponse(ThreadParam* tp, char *buff, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst, int &ResType);
int findCodeTab(CODETABLE* pstCodeTable, char* tCode,int cnt);
void Init_Server();
void CloseProcess(int sig);
int getAllocResultTable(char* filename, CODETABLE** ppstCodeTable);

void getCertFileData(char* filename, char* strAuthKey, int index);
void HexToChar(char *Data, char *Result, int length);
int socketSendRecv(char *buff, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst, int nLength);
int LogOut(ThreadParam* tp, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst);
int encrypto2(ThreadParam* tp, char *szSID, char *szPWD, char *mas_AuthKey, char *szFinalAuthData);
void retry(long long msgid, char *q_name, int telcoid, sql_context ctx);

void mnt(char *buf, int st, int err);
void log(char *buf, int st, int err);
