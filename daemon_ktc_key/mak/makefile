PROC=proc
CC=g++
COPY=cp
RM=rm
LINT=lint
#CFLAGS = -g -Wall -DDEBUG
CFLAGS = -g -Wall -DDEBUG=5
LIBS = -lcryptopp

MMS_DBSTRING=NEO228
MMS_DBID=neomms2
MMS_DBPASS=neomms2

ORG_D=${HOME}/daemon_ktc_key
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
CRYPT_LIB_D=${ORG_D}/cryptopp
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

XML_INC_D=/usr/include/libxml2
XML_LIB_D=/usr/lib64

EXT_LIB=${HOME}/command_2telco_key/obj/sms_ctrlsub++.o
EXT_INC=${HOME}/command_2telco_key/inc

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
ORA_INC = ${ORACLE_HOME}/precomp/public

INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(LIB_D) -I$(CRYPT_LIB_D) -I$(ORA_INC) -I$(XML_INC_D)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC)
ORALIB = -lclntsh
XMLLIB = -L$(XML_LIB_D) -lxml2

all: telco_ktc_mms

telco_ktc_mms : $(OBJ_D)/telco_ktc_new_mms.o $(OBJ_D)/sha1.o  $(OBJ_D)/cryptopp.o $(OBJ_D)/Properties.o $(OBJ_D)/SocketTCP.o $(OBJ_D)/xmlParser.o $(OBJ_D)/PacketCtrlKTC_MMS.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) $(XMLLIB) $(LIBD) ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} ${LIBS} -lpthread $(ORALIB) -o $(BIN_D)/telco_ktc_mms_tmp

$(OBJ_D)/telco_ktc_new_mms.o: $(SRC_D)/telco_ktc_new_mms.cpp
	$(RM) -rf $(OBJ_D)/telco_ktc_new_mms.*
	$(COPY) $(SRC_D)/telco_ktc_new_mms.cpp $(OBJ_D)/telco_ktc_new_mms.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/telco_ktc_new_mms.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/telco_ktc_new_mms.o $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/telco_ktc_new_mms.cpp

$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/SocketTCP.o: $(LIB_D)/SocketTCP.cpp
	$(RM) -rf $(OBJ_D)/SocketTCP.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/xmlParser.o: $(LIB_D)/xmlParser.cpp
	$(RM) -rf $(OBJ_D)/xmlParser.*
	$(CC) -o $@ $(CFLAGS) $(LIBS) $(XMLLIB) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/PacketCtrlKTC_MMS.o: $(LIB_D)/PacketCtrlKTC_MMS.cpp
	$(RM) -rf $(OBJ_D)/PacketCtrlKTC_MMS.*
	$(CC) -o $@ $(CFLAGS) $(LIBS) $(XMLLIB) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/sha1.o: $(LIB_D)/sha1.c
	$(RM) -rf $(OBJ_D)/sha1.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/cryptopp.o: $(LIB_D)/cryptopp.cpp
	$(RM) -rf $(OBJ_D)/cryptopp.*
	$(CC) -o $@ $(CFLAGS) $(LIBS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/telco_ktc_mms_tmp $(BIN_D)/telco_ktc_mms

telco_ktc_mms_install:
	mv $(BIN_D)/telco_ktc_mms_tmp $(BIN_D)/telco_ktc_mms
