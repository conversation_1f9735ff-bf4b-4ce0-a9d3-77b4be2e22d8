//============================================================================
//============================================================================
// Name        : telco_ktc_new_mms.cpp
// Author      : KskyB Inc.
// Version     :
// Copyright   : Copyright@KSKYB
// Description : Hello World in C++, Ansi-style
//============================================================================
#include "../inc/telco_ktc_new_mms.h"

#include <iostream>
using namespace std;
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <sys/time.h>

#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ether.h>
#include <net/ethernet.h>
#include <sys/types.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <ifaddrs.h>
#include <vector>


char s_args[64];
const char ConvertToHex( const char cSource ) 
{    
	return "0123456789abcdef"[ 0x0f & cSource ]; 
} 


const std::string URLEncoding( const std::string& kInput ) 
{ 
	std::string kOutput; 

	std::string::const_iterator string_iter = kInput.begin(); 
	while( string_iter != kInput.end() ) 
	{ 
		const std::string::value_type element = (*string_iter); 
		if(*string_iter == '/' || *string_iter == '.')
		{
			kOutput += element;
		}else if( isascii( element ) // ASCII 문자 이고 
			&& isalnum( element ) ) // 대소문자, 그리고 숫자 
		{ 
			kOutput += element;
		} 
		else // 그 외 문자는 모두 %16진수 형태로 변환 
		{ 
			kOutput += "%"; 
			kOutput += ConvertToHex( element >> 4 ); 
			kOutput += ConvertToHex( element ); 
		} 

		++string_iter; 
	} 
	return kOutput; 
}

std::string _get_ip()
{
	// 	std::vector<std::string> ip4_list;
	// 	std::vector<std::string> ip6_list;
	std::string ip="";
	struct ifaddrs* ifaddrStruct=NULL;
	struct ifaddrs* ifa=NULL;

	void* tmpAddrPtr=NULL;

	getifaddrs(&ifaddrStruct);

	for(ifa = ifaddrStruct; ifa != NULL ; ifa = ifa->ifa_next)
	{
		if(ifa->ifa_addr->sa_family==AF_INET)
		{
			tmpAddrPtr=&((struct sockaddr_in*)ifa->ifa_addr)->sin_addr;
			char addressBuffer[INET_ADDRSTRLEN]={0,};
			inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);

			if(strncmp(ifa->ifa_name, "eth0", 4) == 0)
				ip=addressBuffer;

		}else if(ifa->ifa_addr->sa_family==AF_INET6)
		{
			tmpAddrPtr=&((struct sockaddr_in6*)ifa->ifa_addr)->sin6_addr;
			char addressBuffer[INET6_ADDRSTRLEN]={0,};
			inet_ntop(AF_INET6, tmpAddrPtr, addressBuffer, INET6_ADDRSTRLEN);
			// 			ip = ifa->ifa_name;
			// 			ip6_list.push_back(ip);
		}
	}

	return ip;
}

int _Alert(char* pForm,...)
{
	int sockfd, len;
	struct sockaddr_in serv_addr;
	struct timeval tv;

	char strParam[256]={0,};
	char transBuf[350]={0,};

	va_list pArg;
	va_start(pArg, pForm);
	vsprintf(strParam, pForm, pArg);
	va_end(pArg);

	std::string msg;
	sprintf(transBuf,"[%s]\n[%s]\n[%s]", _get_ip().c_str(), PROCESS_NAME, strParam);

	msg = URLEncoding(transBuf);
	memset(transBuf,0x00,sizeof(transBuf));
	sprintf(transBuf,"get %s?type=1&callType=%s&msgbody=%s\n",WEB_PAGE, "410", msg.c_str());	
//	sprintf(transBuf,"get %s?type=1&callType=%s&msgbody=%s\n","/alertcall/alertcall.php", "410", msg.c_str());		
	memset((char*)&serv_addr,0x00,sizeof(serv_addr));
	serv_addr.sin_family = AF_INET;
	serv_addr.sin_addr.s_addr = inet_addr(CALL_WEB);
	serv_addr.sin_port = htons(PORT_WEB);

	if ( (sockfd=socket(AF_INET,SOCK_STREAM,0))<0 ) {
		return -1;
	}
	tv.tv_sec = 5;
	tv.tv_usec = 0;

	if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv)) < 0 ) {
		return -1;
	}

	if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv)) < 0 ) {
		return -1;
	}

	if ( connect(sockfd,(struct sockaddr*)&serv_addr, sizeof(struct sockaddr))<0 ) {
		return -1;
	}
	
	len = write(sockfd,transBuf,strlen(transBuf));
	if( len < 0 )
		return -1;

	memset(transBuf,0x080,sizeof(transBuf));
	len = read(sockfd,transBuf,sizeof(transBuf));
	close(sockfd);
	if (strncmp(transBuf,"OK",2) != 0 )
		return -1;

	return 0;
}

static int limit_error_count=0;
int main(int argc, char* argv[])
{
	int idx;
	char logMsg[512];
	
	strcpy(s_args, argv[1]);
	g_prop.load(argv[1]);

	int nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	Init_Server();
	printf("KTC TREAD CNT:[%d]\n", nThreadCnt);

	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0)<0) {
		printf("ml_sub_init ERROR.\n");
		return 0;
	}

	sprintf(logMsg, "[%s():%d][main Process Start.]", __func__, __LINE__);
	mnt(logMsg, 0, 0);

	if (g_oracle.setEnableThreads()<0)
	{
		sprintf(logMsg, "[%s():%d][setEnableThreads ERROR. process return;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return 0;
	}

	sprintf(logMsg, "[%s():%d][g_oracle.setEnableThreads() Success.]", __func__, __LINE__);
	mnt(logMsg, 0, 0);

	ThreadParam pParam[nThreadCnt];
	for (idx = 0; idx < nThreadCnt; idx++)
	{
		memset(&pParam[idx], 0x00, sizeof(ThreadParam));
		vtBuff.reserve(100);
		pParam[idx].sockfd = idx;
		if (g_oracle.initThread(pParam[idx].ctx)<0 || pParam[idx].ctx==NULL)
		{
			sprintf(logMsg, "[%s():%d][g_oracle.initThread Fail.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			continue;
		}
		nCntCode = getAllocResultTable(g_prop.getProperty("code.reportCodeFile"), &pstCodeTable);
		if (nCntCode <= 0)
		{
			sprintf(logMsg, "[%s():%d][getAllocResultTable Fail.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			continue;
		}

		// 서버 요청 아이피, 포트 정보
		if (MAS_SERVER_REQ(&pParam) == -1)
		{
			sprintf(logMsg, "[%s():%d][MAS_SERVER_REQ Fail.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return 0;
		}

		sprintf(logMsg, "[%s():%d][MAS_SERVER_REQ Success.]", __func__, __LINE__);
		mnt(logMsg, 0, 0);

		if (FUS_TCSMSG_SERVER_REQ(&pParam) == -1)
		{
			sprintf(logMsg, "[%s():%d][FUS_TCSMSG_SERVER_REQ Fail.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return 0;
		}

		sprintf(logMsg, "[%s():%d][FUS_TCSMSG_SERVER_REQ Success.]", __func__, __LINE__);
		mnt(logMsg, 0, 0);

		// 암호화 과정 진행 필요
		sprintf(fus_OneTimeKey, "182AD883DA1C744ED2864A12A68B9D7A3DF02433");

		pthread_create(&pParam[idx].tid, NULL, procSendRept, &pParam[idx]);
	}

	CheckThreadStatus((ThreadParam**)pParam, nThreadCnt);

	sprintf(logMsg, "[%s():%d][main Process End.]", __func__, __LINE__);
	mnt(logMsg, 0, 0);

	ml_sub_end();

	printf("ktc ml_sub_end\n");

	return 0;
}

int CheckThreadStatus(ThreadParam** param, int nThreadCnt)
{
	char logMsg[256];
	int idx, status;
	KSKYB::CSocketTCP sockInst;
	ThreadParam tpSub[nThreadCnt];
	memcpy(tpSub, param, sizeof(ThreadParam) * nThreadCnt);

	sprintf(logMsg, "[%s():%d][CheckThreadStatus Start]", __func__, __LINE__);
	mnt(logMsg, 0, 0);

	while (true) {
		sockInst.Wait_A_Moment(0, 10);
		for (idx = 0; idx < nThreadCnt; idx++) {
			if (pthread_kill(tpSub[idx].tid, 0) != 0) {
				pthread_join(tpSub[idx].tid, (void**)&status);
				activeProcess = false;
				sprintf(logMsg, "[%s():%d][CheckThreadStatus activeProcess = false;]", __func__, __LINE__);
				mnt(logMsg, 0, 0);
				break;
			}
		}
		if (activeProcess == false) break;
	}

	sleep(3);

	for (idx = 0; idx < nThreadCnt; idx++) {
#ifdef DEBUG
	sprintf(logMsg, "[%s():%d][CheckThreadStatus Sleep(3)]", __func__, __LINE__);
	mnt(logMsg, 0, 0);
#endif
		if (pthread_kill(tpSub[idx].tid, 0) != 0) {
			pthread_join(tpSub[idx].tid, (void**)&status);
			g_oracle.freeThread(tpSub[idx].ctx);

			if( pstCodeTable != NULL)
			{
				free(pstCodeTable);
				pstCodeTable = NULL;
			}
#ifdef DEBUG
			sprintf(logMsg, "[%s():%d][g_oracle.freeThread Success.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			sprintf(logMsg, "[%s():%d][pWorkThread::[%d] Closed..]", __func__, __LINE__, idx);
			mnt(logMsg, 0, 0);
#endif
		}
	}
#ifdef DEBUG
	sprintf(logMsg, "[%s():%d][CheckThreadStatus End]", __func__, __LINE__);
	mnt(logMsg, 0, 0);
#endif
	return 0;
}

int MAS_SERVER_REQ(void* param)
{
	ThreadParam* tp = (ThreadParam*)param;
	CPacketCtrlKTC packetKTC;

	KSKYB::CSocketTCP sockInst(g_prop.getPropertyInt("gw.port")); //socket()
	sockInst.connectToServer(g_prop.getProperty("gw.addr1")); //connect()
	sockInst.setLingerSeconds(1, 0); //setsockopt()
	int ResType;

	int msgSize = 0;
	int nRet = 0;

	try {
		memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
		sprintf(ServerCategory, "MAS");
		msgSize = packetKTC.getMsg_MAS_SERVER_REQ(tp->buff,g_prop.getProperty("gw.addr1"));
		tp->buff[msgSize] = '\0';

		if (nRet = socketSendRecv(tp->buff, packetKTC, sockInst, msgSize) < 0)
		{
			if (nRet == -1)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][MAS_SERVER_REQ sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "MAS_SERVER_REQ sendMessage ERROR...");
			}
			if (nRet == -2)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][MAS_SERVER_REQ recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "MAS_SERVER_REQ recieveMessage ERROR...");
			}
		}

		if (ClassifyResponse(tp, tp->buff, packetKTC, sockInst, ResType) < 0)
		{
			sprintf(tp->logMsg, "[%d][%s():%d][MAS_SERVER_REQ ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
			mnt(tp->logMsg, 0, 0);
			throw new myException(0, "MAS_SERVER_REQ ClassifyResponse ERROR...");
		}
	}
	catch (myException* excp) {
		sprintf(tp->logMsg, "[%d][%s():%d][%s]", tp->sockfd, __func__, __LINE__, (char*)(excp->getErrMsg().c_str()));
		mnt(tp->logMsg, 0, 0);
		sprintf(tp->logMsg, "[%d][%s():%d][catch (myException* excp) return -1;]", tp->sockfd, __func__, __LINE__);
		mnt(tp->logMsg, 0, 0);
		delete excp;
		return -1;
	}

	sockInst.SocketClose();
	sprintf(tp->logMsg, "[%d][%s():%d][MAS_SERVER_REQ Success. return true;]", tp->sockfd, __func__, __LINE__);
	mnt(tp->logMsg, 0, 0);
	return 1;
}

int FUS_TCSMSG_SERVER_REQ(void* param)
{
	ThreadParam* tp = (ThreadParam*)param;
	CPacketCtrlKTC packetKTC;

	KSKYB::CSocketTCP sockInst(g_prop.getPropertyInt("gw.port")); //socket()
	sockInst.connectToServer(g_prop.getProperty("gw.addr1")); //connect()
	sockInst.setLingerSeconds(1, 0); //setsockopt()
	int ResType;

	int msgSize = 0;
	int nRet = 0;

	try {
		memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
		sprintf(ServerCategory, "FUS-TCSMSG");
		msgSize = packetKTC.getMsg_FUS_TCSMSG_SERVER_REQ(tp->buff, g_prop.getProperty("gw.addr1"));
		tp->buff[msgSize] = '\0';

		if (nRet = socketSendRecv(tp->buff, packetKTC, sockInst, msgSize) < 0)
		{
			if (nRet == -1)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][FUS_TCSMSG_SERVER_REQ sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "FUS_TCSMSG_SERVER_REQ sendMessage ERROR...");
			}
			if (nRet == -2)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][FUS_TCSMSG_SERVER_REQ recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "FUS_TCSMSG_SERVER_REQ recieveMessage ERROR...");
			}
		}
		if (ClassifyResponse(tp, tp->buff, packetKTC, sockInst, ResType) < 0)
		{
			sprintf(tp->logMsg, "[%d][%s():%d][FUS_TCSMSG_SERVER_REQ ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
			mnt(tp->logMsg, 0, 0);
			throw new myException(0, "FUS_TCSMSG_SERVER_REQ ClassifyResponse ERROR...");
		}
	}
	catch (myException* excp) {
		sprintf(tp->logMsg, "[%d][%s():%d][%s]", tp->sockfd, __func__, __LINE__, (char*)(excp->getErrMsg().c_str()));
		mnt(tp->logMsg, 0, 0);
		sprintf(tp->logMsg, "[%d][%s():%d][catch (myException* excp) return -1;]", tp->sockfd, __func__, __LINE__);
		mnt(tp->logMsg, 0, 0);
		delete excp;
		return -1;
	}

	sockInst.SocketClose();
	sprintf(tp->logMsg, "[%d][%s():%d][FUS_TCSMSG_SERVER_REQ Success. return true;]", tp->sockfd, __func__, __LINE__);
	mnt(tp->logMsg, 0, 0);

	return 1;
}

int BindGateway(ThreadParam* tp, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst)
{
	int msgSize = 0;
	int nRet = 0;
	char szSID[16], szPWD[16], szFinalAuthData[MAX_BUFF];
	int ResType;
	try {
		sprintf(szSID, "gw.sid%d", tp->sockfd + 1);
		sprintf(szPWD, "gw.pwd%d", tp->sockfd + 1);

//////////////////////////////////////////////////////////////////////////////////////////////////////
//		서버 시간 요청
//////////////////////////////////////////////////////////////////////////////////////////////////////
		memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
		msgSize = packetKTC.getMsg_SERVER_TIME_REQ(tp->buff, g_prop.getProperty(szSID));
		tp->buff[msgSize] = '\0';

		if (nRet = socketSendRecv(tp->buff, packetKTC, sockInst, msgSize+1) < 0)
		{
			if (nRet == -1)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][BindGateway(SERVER_TIME) sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "BindGateway(SERVER_TIME) sendMessage ERROR...");
			}
			if (nRet == -2)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][BindGateway(SERVER_TIME) recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "BindGateway(SERVER_TIME) recieveMessage ERROR...");
			}
		}
		if (ClassifyResponse(tp, tp->buff, packetKTC, sockInst, ResType) < 0)
		{
			sprintf(tp->logMsg, "[%d][%s():%d][BindGateway(SERVER_TIME) ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
			mnt(tp->logMsg, 0, 0);
			throw new myException(0, "BindGateway(SERVER_TIME) ClassifyResponse ERROR...");
		}
//////////////////////////////////////////////////////////////////////////////////////////////////////
//		MAS 서버 로그인 요청
//////////////////////////////////////////////////////////////////////////////////////////////////////

		// cert File Data
		tp->mas_AuthKeyIndex = 100;
		getCertFileData(g_prop.getProperty("code.certFile"), tp->mas_AuthKey, tp->mas_AuthKeyIndex);
		//sprintf(tp->logMsg, "#0(cert 128 byte) : [%s], Length : [%d]", tp->mas_AuthKey, strlen(tp->mas_AuthKey));
		//log(tp->logMsg, 0, 0);
		//인증키 암호화
		memset(szFinalAuthData, 0x00, sizeof(char)*MAX_BUFF);
		if (encrypto2(tp, szSID, szPWD, tp->mas_AuthKey, szFinalAuthData) < 0)
		{
			sprintf(tp->logMsg, "[%d][%s():%d][encrypto2 ERROR...]", tp->sockfd, __func__, __LINE__);
			mnt(tp->logMsg, 0, 0);
			throw new myException(0, "encrypto2 ERROR...");
		}

		memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
		msgSize = packetKTC.getMsg_BIND_REQ(tp->buff, g_prop.getProperty(szSID), szFinalAuthData, tp->mas_AuthKeyIndex);
		tp->buff[msgSize] = '\0';
#if (DEBUG >= 5)
		sprintf(tp->logMsg, "[%d][%s():%d] BindGateway BIND_REQ [%s]", tp->sockfd, __func__, __LINE__, tp->buff);
		log(tp->logMsg, 0, 0);
#endif

		if (nRet = socketSendRecv(tp->buff, packetKTC, sockInst, msgSize+1) < 0)
		{
			if (nRet == -1)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][BindGateway(BIND) sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "BindGateway(BIND) sendMessage ERROR...");
			}
			if (nRet == -2)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][BindGateway(BIND) recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "BindGateway(BIND) recieveMessage ERROR...");
			}
		}
		if (ClassifyResponse(tp, tp->buff, packetKTC, sockInst, ResType) < 0)
		{
			sprintf(tp->logMsg, "[%d][%s():%d][BindGateway(BIND) ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
			mnt(tp->logMsg, 0, 0);
			throw new myException(0, "BindGateway(BIND) ClassifyResponse ERROR...");
		}
//////////////////////////////////////////////////////////////////////////////////////////////////////
	}
	catch (myException* excp) {
		sprintf(tp->logMsg, "[%d][%s():%d][%s]", tp->sockfd, __func__, __LINE__, (char*)(excp->getErrMsg().c_str()));
		mnt(tp->logMsg, 0, 0);
		sprintf(tp->logMsg, "[%d][%s():%d][catch (myException* excp) return -1;]", tp->sockfd, __func__, __LINE__);
		mnt(tp->logMsg, 0, 0);
		delete excp;
		return -1;
	}
	sprintf(tp->logMsg, "[%d][%s():%d][BindGateway Success. return true;]", tp->sockfd, __func__, __LINE__);
	mnt(tp->logMsg, 0, 0);
	return 1;
}

int LogOut(ThreadParam* tp, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst)
{
	int msgSize = 0;
	int nRet = 0;
	int ResType;
	try {
		memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
		msgSize = packetKTC.getMsg_LOGOUT_REQ(tp->buff);
		tp->buff[msgSize] = '\0';

		if (nRet = socketSendRecv(tp->buff, packetKTC, sockInst, msgSize+1) < 0)
		{
			if (nRet == -1)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][LogOut sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "LogOut sendMessage ERROR...");
			}
			if (nRet == -2)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][LogOut recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "LogOut recieveMessage ERROR...");
			}
		}
		if (ClassifyResponse(tp, tp->buff, packetKTC, sockInst, ResType) < 0)
		{
			sprintf(tp->logMsg, "[%d][%s():%d][LogOut ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
			mnt(tp->logMsg, 0, 0);
			throw new myException(0, "LogOut ClassifyResponse ERROR...");
		}
	}
	catch (myException* excp) {
		sprintf(tp->logMsg, "[%d][%s():%d][%s]", tp->sockfd, __func__, __LINE__, (char*)(excp->getErrMsg().c_str()));
		mnt(tp->logMsg, 0, 0);
		sprintf(tp->logMsg, "[%d][%s():%d][catch (myException* excp) return -1;]", tp->sockfd, __func__, __LINE__);
		mnt(tp->logMsg, 0, 0);
		delete excp;
		return -1;
	}
	sprintf(tp->logMsg, "[%d][%s():%d][LogOut Success. return true;]", tp->sockfd, __func__, __LINE__);
	mnt(tp->logMsg, 0, 0);
	return 1;
}

void* procSendRept(void* param)
{
	ThreadParam* tp = (ThreadParam*)param;

	int msgSize = 0, nRet = 0; 
	long long msgid = 0;
	int ctn_cnt = 0;
	int ctn_id = 0;
	bool bFileError = false;
	char quid[256];
	char retry_q[256];
	sprintf(quid,"%s",g_prop.getProperty("gw.quid"));
	nTelcoNo = g_prop.getPropertyInt("gw.quno");
	sprintf(tp->logMsg, "[%s:%d]  gw.quno : [%d]", PROCESS_NAME, tp->sockfd, nTelcoNo);
	log(tp->logMsg, 0, 0);

	CPacketCtrlKTC packetKTC;
	int ResType = 0;
	KSKYB::CSocketTCP sockInst(atoi(mas_ServerPORT));	//실서버 접속
	sockInst.connectToServer(mas_ServerIP);
	sockInst.setLingerSeconds(1, 0);
	sprintf(tp->logMsg, "[%s:%d]mas_ServerIP : [%.16s], mas_ServerPORT : [%.5s]", PROCESS_NAME, tp->sockfd, mas_ServerIP, mas_ServerPORT);
	log(tp->logMsg, 0, 0);

	if (g_oracle.connectToOracle(tp->ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0)
	{
		activeProcess = false;
	}

	try {
		if (BindGateway(tp, packetKTC, sockInst) < 0)
		{
			sprintf(tp->logMsg, "[%d][%s():%d][BindGateway ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
			throw new myException(0, "BindGateway ERROR...");
		}

		time(&tp->sLastT);

		while (activeProcess) {
			bFileError = false;
			sockInst.Wait_A_Moment(0, 1000);
			//nRet = sockInst.checkSelect(100);
			nRet = sockInst.checkSelect(2000);
			if (nRet > 0) {
				memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
				if (sockInst.recieveMessage(tp->buff) < 0)
				{
					sprintf(tp->logMsg, "[%d][%s():%d][recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
					throw new myException(0, "recieveMessage ERROR...");
				}
				msgSize = strlen(tp->buff);
				tp->buff[msgSize] = '\0';
				if (ClassifyResponse(tp, tp->buff, packetKTC, sockInst, ResType) < 0)
				{
					sprintf(tp->logMsg, "[%d][%s():%d][ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
					throw new myException(0, "ClassifyResponse ERROR...");
				}
				time(&tp->sThisT);
			}
			else if (nRet < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][ERROR Occurred(sockInst.checkSelect)....]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "ERROR Occurred(sockInst.checkSelect)....");
			}

			// MMS DB QUEUE에서 SEND MSG 정보 가져오기
			msgid = 0;
			if (vtBuff.size() > 0) vtBuff.clear();
			msgid = g_oracle.getMsgData(tp->ctx, quid, vtBuff);
			if (msgid < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getMsgData msgid ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				activeProcess = false;
				goto endProcess;
			}
			else if (msgid > 0)
			{
				vector<string>::iterator itrData;
				itrData = vtBuff.begin();
				//CONTENT가 있으면 (이미지 파일 가져오기)
				ctn_cnt = 0;
				ctn_id = 0;
				
				int ctn_type = 0;
				ctn_id = atoi((char*)string(*(itrData + 1)).c_str());
				ctn_type = atoi((char*)string(*(itrData + 5)).c_str());
				
				if (ctn_id > 0 && ctn_type > 0)
				{
					if (vtCtnBuff.size() > 0) vtCtnBuff.clear();
					ctn_cnt = g_oracle.getCtnData(tp->ctx, ctn_id, vtCtnBuff);
				}
				// MMS 이면 파일 전송 진행
				if (ctn_cnt > 0)
				{
					char buff[MAX_TCP_BUF];
					for (int idx = 0; idx<ctn_cnt; idx++)
					{						
						char filePath[TMP_BUFF];
						char fileSize[12];
						
						memset(filePath, 0x00, sizeof(filePath));
						memset(fileSize, 0x00, sizeof(fileSize));
						
						//파일 저장 위치 요청
						memset(buff, 0x00, sizeof(char)*MAX_TCP_BUF);
						msgSize = packetKTC.getMsg_FILE_SAVE_REQ(buff, msgid, ctn_id, idx);
						buff[msgSize] = '\0';
						//
						sprintf(tp->logMsg, "[%s:%d]::FILE_SAVE_REQ::MMS_ID[%lld] CTN_ID[%d] idx[%d]", PROCESS_NAME, tp->sockfd, msgid, ctn_id, idx);
						log(tp->logMsg, 0, 0);
						if (sockInst.sendMessage(buff, msgSize+1) > 0)
						{
							sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILE_SAVE_REQ sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
							mnt(tp->logMsg, 0, 0);
							throw new myException(0, "getMsg_FILE_SAVE_REQ sendMessage ERROR...");
						}
						ResType = 0;

						double pst = clock();
						int timeout = 10;
						while(ResType != CPacketCtrlKTC::TYPE_FILE_SAVE_ACK)
						{
							if(((double)(clock()-pst)/CLOCKS_PER_SEC) > timeout)
							{
								g_prop.load(s_args);
								sprintf(retry_q,"%s",g_prop.getProperty("gw.retry_q"));
								retry(msgid, retry_q, 26, tp->ctx);
								sprintf(tp->logMsg, "[%s:%d]::FILE_SAVE_ACK Timeout...::MMS_ID[%lld] CTN_ID[%d]", PROCESS_NAME, tp->sockfd, msgid, ctn_id);
								log(tp->logMsg, 0, 0);
								sprintf(tp->logMsg, "[%d][%s():%d][FILE_SAVE_ACK Timeout ERROR...]", tp->sockfd, __func__, __LINE__);
										mnt(tp->logMsg, 0, 0);
								throw new myException(0, "FILE_SAVE_ACK Timeout...");
							}

							int msgSize = 0;
							int nRet = 0;
							memset(buff, 0x00, sizeof(char)*MAX_TCP_BUF);
							if (sockInst.recieveMessage(buff) > 0)
							{
								msgSize = strlen(buff);
								buff[msgSize] = '\0';

								if (ClassifyResponse(tp, buff, packetKTC, sockInst, ResType) < 0)
								{
									sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILE_SAVE_REQ ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
										mnt(tp->logMsg, 0, 0);
									throw new myException(0, "getMsg_FILE_SAVE_REQ ClassifyResponse ERROR...");
								}
							}							
						}
						

						KSKYB::CSocketTCP sockInst2(atoi(fus_ServerPORT));	//실서버 접속
						sockInst2.connectToServer(fus_ServerIP);
						sockInst2.setLingerSeconds(1, 0);

						sprintf(filePath, "%s", tp->fus_FilePath);
						//파일 업로드						
						memset(buff, 0x00, sizeof(char)*MAX_TCP_BUF);
						sprintf(tp->logMsg, "[%s:%d]::FILEUPLOAD_REQ::MMS_ID[%lld] CTN_ID[%d] fus_FilePath[%s]", PROCESS_NAME, tp->sockfd, msgid, ctn_id, tp->fus_FilePath);
						log(tp->logMsg, 0, 0);	

						msgSize = packetKTC.getMsg_FILEUPLOAD_REQ(buff, fus_OneTimeKey, tp->mas_SessionID, filePath, tp->fus_AuthTicket, fileSize, fus_ServerIP, vtBuff, vtCtnBuff, idx);

						if (msgSize == -1)
						{
							if (vtBuff.size() > 0) vtBuff.clear();
							if (vtCtnBuff.size() > 0) vtCtnBuff.clear();
							bFileError = true;
							break;
							//throw new myException(0, "getMsg_FILEUPLOAD_REQ(AESandBASE64) sendMessage ERROR...");
						}
						buff[msgSize] = '\0';

						if (nRet = socketSendRecv(buff, packetKTC, sockInst2, msgSize) < 0)
						{
							if (nRet == -1)
							{
								sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILEUPLOAD_REQ sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
									mnt(tp->logMsg, 0, 0);
								throw new myException(0, "getMsg_FILEUPLOAD_REQ sendMessage ERROR...");
							}
							if (nRet == -2)
							{
								sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILEUPLOAD_REQ recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
									mnt(tp->logMsg, 0, 0);
								throw new myException(0, "getMsg_FILEUPLOAD_REQ recieveMessage ERROR...");
							}
						}
						
						if (ClassifyResponse(tp, buff, packetKTC, sockInst, ResType) < 0)
						{
							sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILEUPLOAD_REQ ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
								mnt(tp->logMsg, 0, 0);
							throw new myException(0, "getMsg_FILEUPLOAD_REQ ClassifyResponse ERROR...");
						}
						sockInst2.SocketClose();

						//업로드 완료 전달
						memset(buff, 0x00, sizeof(char)*MAX_TCP_BUF);
						msgSize = packetKTC.getMsg_FILE_UPLOAD_FINISH_REQ(buff, filePath, fileSize, tp->fus_CustomMessageID);
						buff[msgSize] = '\0';
						//
						switch(idx)
						{
						case	0:
							sprintf(tp->fus_FilePath1, "%s", filePath);
							sprintf(tp->fus_FileSize1, "%s", fileSize);
							break;
						case	1:
							sprintf(tp->fus_FilePath2, "%s", filePath);
							sprintf(tp->fus_FileSize2, "%s", fileSize);
							break;
						case	2:
							sprintf(tp->fus_FilePath3, "%s", filePath);
							sprintf(tp->fus_FileSize3, "%s", fileSize);
							break;
						}

						sprintf(tp->logMsg, "[%s:%d]::UPLOAD_FINISH_REQ::MMS_ID[%lld] CTN_ID[%d] fus_CustomMessageID[%s] filePath[%s] fileSize[%s]", PROCESS_NAME, tp->sockfd, msgid, ctn_id, tp->fus_CustomMessageID, filePath, fileSize);
						log(tp->logMsg, 0, 0);	

						if (nRet = socketSendRecv(buff, packetKTC, sockInst, msgSize+1) < 0)
						{
							if (nRet == -1)
							{
								sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILE_UPLOAD_FINISH_REQ sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
									mnt(tp->logMsg, 0, 0);
								throw new myException(0, "getMsg_FILE_UPLOAD_FINISH_REQ sendMessage ERROR...");
							}
							if (nRet == -2)
							{
								sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILE_UPLOAD_FINISH_REQ recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
									mnt(tp->logMsg, 0, 0);
								throw new myException(0, "getMsg_FILE_UPLOAD_FINISH_REQ recieveMessage ERROR...");
							}
						}
						if (ClassifyResponse(tp, buff, packetKTC, sockInst, ResType) < 0)
						{
							sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILE_UPLOAD_FINISH_REQ ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
								mnt(tp->logMsg, 0, 0);
							throw new myException(0, "getMsg_FILE_UPLOAD_FINISH_REQ ClassifyResponse ERROR...");
						}
					}
				}

				if (bFileError)
				{//파일 업로드 과정에서 에러가 발생할 경우 리턴.
					if (vtBuff.size() > 0) vtBuff.clear();
					if (vtCtnBuff.size() > 0) vtCtnBuff.clear();
					sprintf(tp->logMsg, "[%d][%s():%d][getMsg_FILE_UPLOAD_FINISH_REQ bFileError ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
							
					continue;
				}

				//메세지 전송
				memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
				msgSize = packetKTC.getMsg_DELIVER_REQ(tp->buff, fus_OneTimeKey, tp->mas_SessionID, vtBuff, tp->fus_FilePath1, tp->fus_FileSize1, tp->fus_FilePath2, tp->fus_FileSize2, tp->fus_FilePath3, tp->fus_FileSize3, ctn_cnt);
				if (msgSize == -1)
				{
					sprintf(tp->logMsg, "[%d][%s():%d][getMsg_DELIVER_REQ msgSize ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
					if (vtBuff.size() > 0) vtBuff.clear();
					if (vtCtnBuff.size() > 0) vtCtnBuff.clear();
					continue;
				}
				tp->buff[msgSize] = '\0';
				//

				sprintf(tp->logMsg, "[%s:%d]::DELIVER_REQ::[%s][%s][%s][][][%s][%s][%s]", PROCESS_NAME, tp->sockfd,(char*)string(*itrData).c_str(),(char*)string(*(itrData+1)).c_str(),
						(char*)string(*(itrData+2)).c_str(),/*(char*)string(*(itrData+3)).c_str(),(char*)string(*(itrData+4)).c_str(),*/
						(char*)string(*(itrData+5)).c_str(),(char*)string(*(itrData+6)).c_str(),
						(char*)string(*(itrData+9)).c_str());
				log(tp->logMsg, 0, 0);

				memset(tp->fus_FilePath1, 0x00, TMP_BUFF);
				memset(tp->fus_FilePath2, 0x00, TMP_BUFF);
				memset(tp->fus_FilePath3, 0x00, TMP_BUFF);
				memset(tp->fus_FileSize1, 0x00, 12);
				memset(tp->fus_FileSize2, 0x00, 12);
				memset(tp->fus_FileSize3, 0x00, 12);

				if (vtBuff.size() > 0) vtBuff.clear();
				if (vtCtnBuff.size() > 0) vtCtnBuff.clear();

				if (nRet = socketSendRecv(tp->buff, packetKTC, sockInst, msgSize+1) < 0)
				{
					if (nRet == -1)
					{
						sprintf(tp->logMsg, "[%d][%s():%d][getMsg_DELIVER_REQ sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
							mnt(tp->logMsg, 0, 0);
						throw new myException(0, "getMsg_DELIVER_REQ sendMessage ERROR...");
					}
					if (nRet == -2)
					{
						sprintf(tp->logMsg, "[%d][%s():%d][getMsg_DELIVER_REQ recieveMessage ERROR...]", tp->sockfd, __func__, __LINE__);
							mnt(tp->logMsg, 0, 0);
						throw new myException(0, "getMsg_DELIVER_REQ recieveMessage ERROR...");
					}
				}
				if (ClassifyResponse(tp, tp->buff, packetKTC, sockInst, ResType) < 0)
				{
					sprintf(tp->logMsg, "[%d][%s():%d][getMsg_DELIVER_REQ ClassifyResponse ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
					throw new myException(0, "getMsg_DELIVER_REQ ClassifyResponse ERROR...");
				}
			}

			//30sec ping check
			time(&tp->sThisT);
			if (difftime(tp->sThisT,tp->sLastT) >= 30) {
				memset(tp->buff, 0x00, sizeof(char)*MAX_TCP_BUF);
				msgSize = packetKTC.getMsg_PING_REQ(tp->buff);
				tp->buff[msgSize] = '\0';
				if (sockInst.sendMessage(tp->buff, strlen(tp->buff)+1) < 0)
				{
					sprintf(tp->logMsg, "[%d][%s():%d][PING sendMessage ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
					throw new myException(0, "PING sendMessage ERROR...");
				}
				time(&tp->sLastT);
			}
		}
	}
	catch (myException* excp) {
		delete excp;
		goto endProcess;
	}

endProcess:
	if( pstCodeTable != NULL)
	{
		free(pstCodeTable);
		pstCodeTable = NULL;
	}

	LogOut(tp, packetKTC, sockInst);
	tp->buff[msgSize] = '\0';

	g_oracle.closeFromOracle(tp->ctx);
	sockInst.SocketClose();
	activeProcess = false;
	return NULL;
}
int ClassifyResponse(ThreadParam* tp, char* buff, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst, int &ResType)
{
	int msgSize = 0;
	char quid[256];
	char retry_q[256];
	
	sprintf(quid,"%s",g_prop.getProperty("gw.quid"));
	//log(buff, 0, 0);
	ResType = packetKTC.getMsgCode(buff);
	try {
		switch (ResType) {
		/* 서버 요청 응답 */
		case CPacketCtrlKTC::TYPE_SERVER_REQ_ACK: {
			if (vtBuff.size() > 0) vtBuff.clear();
			if (packetKTC.getData_ServerReqAck(buff, vtBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_ServerReqAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_ServerReqAck ERROR...");
			}
			vector<string>::iterator itrData;
			itrData = vtBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::SERVER_REQ_ACK::[%s][%s][%s][%s][%s]", PROCESS_NAME, tp->sockfd,
				string(*itrData).c_str(),string(*(itrData + 1)).c_str(),
				string(*(itrData + 2)).c_str(), string(*(itrData + 3)).c_str(),
				string(*(itrData + 4)).c_str());
			log(tp->logMsg, 0, 0);
			//

			if (strcmp(ServerCategory, "MAS") == 0)
			{
				memset(mas_ServerIP, 0x00, 16);
				memset(mas_ServerPORT, 0x00, 5);
				//실 서버 아이피
				sprintf(mas_ServerIP, "%s", string(*(itrData + 3)).c_str());
				//실 서버 포트
				sprintf(mas_ServerPORT, "%s", string(*(itrData + 4)).c_str());
			}
			else if (strcmp(ServerCategory, "FUS-TCSMSG") == 0)
			{
				memset(fus_ServerIP, 0x00, 16);
				memset(fus_ServerPORT, 0x00, 5);
				//파일업로드 서버 아이피
				sprintf(fus_ServerIP, "%s", string(*(itrData + 3)).c_str());
				//파일업로드 서버 포트
				sprintf(fus_ServerPORT, "%s", string(*(itrData + 4)).c_str());
			}

			//
			if (vtBuff.size() > 0) vtBuff.clear();
			break;
		}
		/* 서버 시간 응답 */
		case CPacketCtrlKTC::TYPE_SERVER_TIME_ACK: {
			if (vtBuff.size() > 0) vtBuff.clear();
			if (packetKTC.getData_ServerTimeAck(buff, vtBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_ServerTimeAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_ServerTimeAck ERROR...");
			}
 			vector<string>::iterator itrData;
			itrData = vtBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::SERVER_TIME_ACK::[%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData + 1)).c_str());
			log(tp->logMsg, 0, 0);

			//서버 시간
			sprintf(tp->mas_ServerTime, "%s", string(*(itrData + 1)).c_str());

			if (vtBuff.size() > 0) vtBuff.clear();
			break;
		}
		/* 실서버 연동 응답 */
		case CPacketCtrlKTC::TYPE_BIND_ACK: {
			if (vtBuff.size() > 0) vtBuff.clear();
			if (packetKTC.getData_BindAck(buff, vtBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_BindAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_BindAck ERROR...");
			}
			vector<string>::iterator itrData;
			itrData = vtBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::BIND_ACK::[%s][%s][%s][][][]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(),
			string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str());
			
			log(tp->logMsg, 0, 0);

			sprintf(tp->mas_SessionID, "%s", string(*(itrData + 1)).c_str());

			if (strcmp((char*)string(*itrData).c_str(), "0") != 0)
			{
				activeProcess = false;
			}

			if (vtBuff.size() > 0) vtBuff.clear();

			break;
		}
		/* 파일 저장 응답 */
		case CPacketCtrlKTC::TYPE_FILE_SAVE_ACK: {
			if (vtFileBuff.size() > 0) vtFileBuff.clear();
			if (packetKTC.getData_FileSaveAck(buff, vtFileBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_FileSaveAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_FileSaveAck ERROR...");
			}
 			vector<string>::iterator itrData;
			itrData = vtFileBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::FILE_SAVE_ACK::[%s][%s][%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(),
			string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str(), string(*(itrData + 3)).c_str());
			log(tp->logMsg, 0, 0);

			//결과가 성공이면.
			
			if (strcmp(string(*itrData).c_str(), "0") == 0)
			{
				sprintf(tp->fus_AuthTicket, "%s", string(*(itrData + 1)).c_str());
				sprintf(tp->fus_FilePath, "%s", string(*(itrData + 2)).c_str());
				memset(tp->fus_CustomMessageID, 0, TMP_BUFF);
				sprintf(tp->fus_CustomMessageID, "%s", string(*(itrData + 3)).c_str());
			}

			if (vtFileBuff.size() > 0) vtFileBuff.clear();
			break;
		}
		case CPacketCtrlKTC::TYPE_FILE_UPLOAD_ACK: {
			if (packetKTC.getData_FileUploadAck(buff, vtFileBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_FileUploadAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_FileUploadAck ERROR...");
			}
 			vector<string>::iterator itrData;
			itrData = vtFileBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::FILE_UPLOAD_ACK::[%s][%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(),
			string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str());
			log(tp->logMsg, 0, 0);

			if (vtFileBuff.size() > 0) vtFileBuff.clear();
			break;
		}
		/* 파일 전송 완료 응답 */
		case CPacketCtrlKTC::TYPE_FINISH_UPLOAD_ACK: {
			if (packetKTC.getData_FinishUploadAck(buff, vtFileBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_FileFinishAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_FileFinishAck ERROR...");
			}
 			vector<string>::iterator itrData;
			itrData = vtFileBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::UPLOAD_FINISH_ACK::[%s][%s]", PROCESS_NAME, tp->sockfd,
			string(*itrData).c_str(), string(*(itrData + 1)).c_str());
			log(tp->logMsg, 0, 0);
			if (vtFileBuff.size() > 0) vtFileBuff.clear();
			break;
		}
		/* 핑 요청 응답 */
		case CPacketCtrlKTC::TYPE_PONG: {
			break;
		}
		/* 전문 전송 응답 */
		case CPacketCtrlKTC::TYPE_DELIVER_ACK: {
			if (packetKTC.getData_SndAck(buff, vtDeliverBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_SndAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_SndAck ERROR...");
			}
			vector<string>::iterator itrData;
			itrData = vtDeliverBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::DELIVER_ACK::[%s][%s][%s][%s][%s][%s]", PROCESS_NAME, tp->sockfd,
				string(*itrData).c_str(), string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str(), string(*(itrData + 3)).c_str(),
				string(*(itrData + 4)).c_str(), string(*(itrData + 5)).c_str());
			log(tp->logMsg, 0, 0);

			//if(g_oracle.setSndAckData(TELCO_ID, tp->ctx, vtDeliverBuff) < 0)
			if(g_oracle.setSndAckData(nTelcoNo, tp->ctx, vtDeliverBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][setSndAckData ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "setSndAckData ERROR...");
			}
		
			//result가 0(정상)이 아닐 경우 재전송 한다.
			if(*itrData != "0")
			{
				g_prop.load(s_args);
				sprintf(retry_q,"%s",g_prop.getProperty("gw.retry_q"));
				
				if (g_oracle._putMsgRetryDB(atoll(string(*(itrData + 2)).c_str()), retry_q, 26, tp->ctx) != 0 )
				{
					sprintf(tp->logMsg, "[%d][%s():%d][_putMsgRetryDB1 ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
					throw new myException(0, "_putMsgRetryDB ERROR...");
				}
				sprintf(tp->logMsg, "ACK Error and  RETRY(%s INPUT) mmsid[%s]", retry_q, string(*(itrData + 2)).c_str());
				log(tp->logMsg, 0, 0);
			}
			
			if (vtDeliverBuff.size() > 0) vtDeliverBuff.clear();
			break;
		}
		case CPacketCtrlKTC::TYPE_DELIVER_ALL_ACK: {
			break;
		}
		/* 레포트 수신 */
		case CPacketCtrlKTC::TYPE_REPORT: {
			if (packetKTC.getData_Report(buff, vtReportBuff, fus_OneTimeKey) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_Report ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_Report ERROR...");
			}
			vector<string>::iterator itrData;
			itrData = vtReportBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::REPORT::[%s][%s][%s][%s][%s][%s][%s][%s][%s][%s][][%s][%s][%s][%s]",
				 PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str(),
				 string(*(itrData + 3)).c_str(), string(*(itrData + 4)).c_str(), string(*(itrData + 5)).c_str(),
				 string(*(itrData + 6)).c_str(), string(*(itrData + 7)).c_str(), string(*(itrData + 8)).c_str(),
				 string(*(itrData + 9)).c_str(), /*string(*(itrData + 10)).c_str(),*/ string(*(itrData + 11)).c_str(),
				 string(*(itrData + 12)).c_str(), string(*(itrData + 13)).c_str(), string(*(itrData + 14)).c_str());
			 log(tp->logMsg, 0, 0);

			 sprintf(tp->jobid, "%s", (char*)string(*(itrData + 5)).c_str());

			//트래픽 초과 인 경우 KTC로 전송
			if (strcmp(string(*(itrData + 2)).c_str(),"7103") == 0 )
			{
				g_prop.load(s_args);
				sprintf(retry_q,"%s",g_prop.getProperty("gw.retry_q"));
				if (g_oracle._putMsgRetryDB(atoll(string(*(itrData + 4)).c_str()), retry_q, 26, tp->ctx) != 0 )
				{
					sprintf(tp->logMsg, "[%d][%s():%d][_putMsgRetryDB2 ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
					throw new myException(0, "_putMsgRetryDB ERROR...");
				}
				sprintf(tp->logMsg, "7103 CODE RETRY(%s INPUT) mmsid[%s]", retry_q, string(*(itrData + 4)).c_str());
				log(tp->logMsg, 0, 0);
			}
			else if(strcmp(string(*(itrData + 2)).c_str(),"33") == 0)
			{				
				g_prop.load(s_args);
				sprintf(retry_q,"%s",g_prop.getProperty("gw.retry_q"));
				if(limit_error_count >= 10)
				{
					limit_error_count = 0;
					_Alert("33 Error Limite Month");
				}
				++limit_error_count;
				if (g_oracle._putMsgRetryDB(atoll(string(*(itrData + 4)).c_str()), retry_q, 26, tp->ctx) != 0 )
				{
					sprintf(tp->logMsg, "[%d][%s():%d][_putMsgRetryDB3 ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
					throw new myException(0, "_putMsgRetryDB ERROR...");
				}
				sprintf(tp->logMsg, "33 CODE RETRY(%s INPUT) mmsid[%s]", retry_q, string(*(itrData + 4)).c_str());
				log(tp->logMsg, 0, 0);	
			}
			else
			{
				//재전송인 경우 레포트 테이블에 결과 기록하지 않는다.
				//if (g_oracle.setReportData(TELCO_ID, tp->ctx, vtReportBuff) < 0)
				if (g_oracle.setReportData(nTelcoNo, tp->ctx, vtReportBuff) < 0)
				{
					sprintf(tp->logMsg, "[%d][%s():%d][setReportData ERROR...]", tp->sockfd, __func__, __LINE__);
						mnt(tp->logMsg, 0, 0);
					throw new myException(0, "setReportData ERROR...");
				}
			}

			memset(buff, 0x00, sizeof(char)*MAX_TCP_BUF);
			msgSize = packetKTC.getMsg_REPORT_ACK(buff, tp->jobid);
			buff[msgSize] = '\0';
			//log(buff, 0, 0);
			if (sockInst.sendMessage(buff, strlen(buff)+1) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getMsg_REPORT_ACK ERROR...]", tp->sockfd, __func__, __LINE__);
				mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getMsg_REPORT_ACK sendMessage ERROR...");
			}
			if (vtReportBuff.size() > 0) vtReportBuff.clear();
			break;
		}
		/* 로그아웃 응답 */
		case CPacketCtrlKTC::TYPE_LOGOUT_ACK: {
			if (vtBuff.size() > 0) vtBuff.clear();
			if (packetKTC.getData_LogoutAck(buff, vtBuff) < 0)
			{
				sprintf(tp->logMsg, "[%d][%s():%d][getData_LogoutAck ERROR...]", tp->sockfd, __func__, __LINE__);
					mnt(tp->logMsg, 0, 0);
				throw new myException(0, "getData_LogoutAck ERROR...");
			}
			vector<string>::iterator itrData;
			itrData = vtBuff.begin();
			sprintf(tp->logMsg, "[%s:%d]::LOGOUT_ACK::[%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str());
			log(tp->logMsg, 0, 0);
			if (vtBuff.size() > 0) vtBuff.clear();
			break;
		}
		default:
			break;
		}
	}
	catch (myException* excp) {
		if (vtBuff.size() > 0) vtBuff.clear();
		delete excp;

		return -1;
	}
	return 1;
}

void Init_Server()
{
	setpgrp();
	printf("Process Running.. Please wait 3 seconds.\n");
	sleep(3);
	signal(SIGHUP, CloseProcess);
	signal(SIGCLD, SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess);
	signal(SIGINT, CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess);
}

void CloseProcess(int sig)
{

	char logMsg[512];
	printf("CloseProcess Start & Exit [%d]\n", sig);
	sprintf(logMsg,"CloseProcess Start & Exit [%d]\n", sig);
	mnt(logMsg, 0, 0);
	activeProcess = false;
}

void log(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

void mnt(char *buf, int st, int err)
{
	if (ml_sub_send_moni(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_moni ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

void getCertFileData(char* filename, char* strAuthKey, int index)
{
    FILE* fp=NULL;
    char buff[TMP_BUFF];

    fp = fopen(filename,"rt");
    if( fp == NULL )
    {
        printf("ERROR [%s]\n",strerror(errno));
        return;
    }
    memset(buff,0x00,sizeof(char)*TMP_BUFF);
    fgets(buff,sizeof(char)*TMP_BUFF,fp);

	snprintf(strAuthKey, 128+1, "%s", buff+index);
    fclose(fp);
}

int getAllocResultTable(char* filename,CODETABLE** ppstCodeTable)
{

    FILE* fp=NULL;
    char buff[TMP_BUFF];
    int cnt=0;
    char* szTmp;

    fp = fopen(filename,"rt");
    if( fp == NULL )
    {
        printf("ERROR [%s]\n",strerror(errno));
        return -1;
    }

// count
    memset(buff,0x00,sizeof(char)*TMP_BUFF);
    while(fgets(buff,sizeof(char)*TMP_BUFF,fp) )
    {
        if(buff[0] == '=')
            cnt++;
    }

    *ppstCodeTable = (CODETABLE*)malloc(sizeof(CODETABLE)*cnt);
    memset(*ppstCodeTable,0x00,sizeof(CODETABLE)*cnt);

    fseek(fp,0L,SEEK_SET);
    cnt=0;
    memset(buff,0x00,sizeof(char)*TMP_BUFF);
// set data
    while(fgets(buff,sizeof(char)*TMP_BUFF,fp) )
    {
        if(buff[0] != '=')
            continue;

        szTmp = (char*)strtok(buff," =");
        if( szTmp == NULL)
            continue;

        memcpy((*ppstCodeTable)[cnt].tCode, szTmp,strlen(szTmp));
        szTmp = (char*)strtok(NULL," =");
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].kCode, szTmp,strlen(szTmp));

        szTmp = (char*)strtok(NULL,"=");
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].desc, szTmp,strlen(szTmp));
        cnt++;
    }


    fclose(fp);

    return cnt;
}

int findCodeTab(CODETABLE* pstCodeTable, char* tCode, int cnt)
{
    int i=0;

    for(i=0;i<cnt;i++)
    {
		if(strcmp(pstCodeTable[i].tCode, tCode) == 0)
		{
			return i;
		}
    }

    return -1;
}

void HexToChar(char *Data, char *Result, int length)
{
	int i=0;
	int j=0;
	char temp[3];
	unsigned long tmp;
	if (Data == NULL) return;
	for(j = 0; j<length; j++)
	{
		temp[0] = Data[j];
		temp[1] = Data[j+1];
		temp[2] = '\0';
		j++;
		tmp = strtoul(temp, NULL, 16);

		// stdlib.h 필요
		Result[i] = (char)tmp;
		i++;
	}
}

int socketSendRecv(char *buff, CPacketCtrlKTC& packetKTC, KSKYB::CSocketTCP& sockInst, int nLength)
{
	int msgSize = 0;
	int nRet = 0;
	//mnt(buff, 0, 0);
	if (sockInst.sendMessage(buff, nLength) < 0)
	{
		return -1;
	}

	memset(buff, 0x00, sizeof(char)*MAX_TCP_BUF);
	if (sockInst.recieveMessage(buff) < 0)
	{
		return -2;
	}
	msgSize = strlen(buff);
	buff[msgSize] = '\0';

	return 0;
}

int encrypto2(ThreadParam* tp, char *szSID, char *szPWD, char *mas_AuthKey, char *szFinalAuthData)
{
		char szTemp[MAX_BUFF], szTemp2[MAX_BUFF];
		char KeyData[MAX_BUFF], SrcData[MAX_BUFF], DstData[MAX_BUFF];
		int len;

		memset(KeyData, 0x00, sizeof(char)*MAX_BUFF);
		memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);
		memset(DstData, 0x00, sizeof(char)*MAX_BUFF);

/*
		//1단계 SHA-1 변환은 OK
		memset(szTemp, 0x00, sizeof(szTemp));
		SHA1_String(tp->AuthKey, strlen(tp->AuthKey), szTemp, 40);	//SHA1 HASHING

		len = strlen(szTemp);
		szTemp[len]='\0';
		//sprintf(tp->logMsg, "#1(SHA1 HASHING) : [%s], Length : [%d]", szTemp, strlen(szTemp));
		//log(tp->logMsg, 0, 0);
		//

		//2단계 XOR 변환은 FAIL
		//2단계 구현 필요

		//3단계
		memset(szTemp, 0x00, sizeof(szTemp));
		SHA1_String(szTemp2, strlen(szTemp2), szTemp, 40);	// SHA1 HASHING
		len = strlen(szTemp);
		szTemp[len]='\0';
		//sprintf(tp->logMsg, "#3(SHA1 HASHING) : [%s], Length : [%d]", szTemp, strlen(szTemp));
		//log(tp->logMsg, 0, 0);
		//
*/
		//4단계 3단계 최종 암호키 16바이트 자르기 OK
		//3단계 최종 암호키를 고정.
		sprintf(szTemp, "%s", g_prop.getProperty("code.certKey"));

		memset(KeyData, 0x00, sizeof(char)*MAX_BUFF);
		snprintf(KeyData, 32+1, "%s", szTemp);	// 16바이트 자르기

		//5단계 원본 생성은 OK
		sprintf(SrcData, "%s|%s|%s|%s|%s", g_prop.getProperty(szSID), g_prop.getProperty(szPWD),
					g_prop.getProperty(szSID), tp->mas_ServerTime, "kskyb");

		//6단계 AES 암호화 후 7단계 BASE64 인코딩
		//KeyData 키값
		//SrcData 원본데이터
		//DstData 최종 암호화 데이터
		memset(DstData, 0x00, sizeof(char)*MAX_BUFF);
		if (AESandBASE64_Encode(KeyData, SrcData, DstData) < 0)
		{
			return -1;
		}
		//

		//암호화 과정(AuthTicket 생성)
		//1.Cert File Public Key 생성(128바이트)
		//2.Cert File Public Key를 SHA1방식으로 hashing하여 Public Key 생성
		//3.생성한 20byte의 Public와 0x36으로 채워진 64byte를 byte단위로 XOR 연산하여 다시 SHA1방식으로 Hashing
		//4.AuthTicket 원본 생성.(SPID|SPPWD|EU-ID|서버시간|fus_OneTimeKey)
		//5.4의 AuthTicket 원본을 3에서 생성한 Public Key를 AES 128 BIT 블록암호화 알고리즘으로 Encrypt
		//6.5에서 생성한 암호화된 값을 Base64 Encode
		//

		// 디코드 확인
		memset(SrcData, 0x00, sizeof(char)*MAX_BUFF);
		if (AESandBASE64_Decode(KeyData, DstData, SrcData) < 0)
		{
			return -1;
		}

		//최종 암호키 저장
		sprintf(szFinalAuthData, "%s", DstData);

		return 0;
}


void retry(long long msgid, char *q_name, int telcoid, sql_context ctx)
{
	char logMsg[4096]={0,};
	if (g_oracle._putMsgRetryDB(msgid, q_name, telcoid, ctx) != 0 )
	{
		sprintf(logMsg, "[%s():%d][_putMsgRetryDB ERROR...(MMS -> SKT)]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
		throw new myException(0, "_putMsgRetryDB ERROR...(MMS -> SKT))");
	}
	sprintf(logMsg, "7103 CODE RETRY(SKT QUEUE INPUT) mmsid[%lld]", msgid);
	log(logMsg, 0, 0);
}

