# CMakeLists.txt for daemon_ktc_key
cmake_minimum_required(VERSION 3.16)
project(daemon_ktc_key CXX)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_EXTENSIONS ON)  # Enable GNU extensions for gnu++11

# Generate compile commands database (improves CLion code analysis)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# CLion build flag configuration (for standalone builds)
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()

if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "daemon_ktc_key: CLion build mode enabled")
endif()

# Debug path output
message(STATUS "CMAKE_SOURCE_DIR: ${CMAKE_SOURCE_DIR}")
message(STATUS "CMAKE_CURRENT_SOURCE_DIR: ${CMAKE_CURRENT_SOURCE_DIR}")

# Oracle environment configuration
set(OR<PERSON><PERSON>_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Database connection information
# Option 1: Use environment variables (recommended for CI/CD)
# Option 2: Use separate config file (recommended for local development)

# Include config file if exists
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()

# Read database info from environment variables (takes precedence over config file)
if(DEFINED ENV{MMS_DBSTRING})
    set(MMS_DBSTRING "$ENV{MMS_DBSTRING}")
endif()
if(DEFINED ENV{MMS_DBID})
    set(MMS_DBID "$ENV{MMS_DBID}")
endif()
if(DEFINED ENV{MMS_DBPASS})
    set(MMS_DBPASS "$ENV{MMS_DBPASS}")
endif()

# Build mode configuration (use actual database connection)
option(ENABLE_DB_CONNECTION "Enable database connection for Pro*C compilation" ON)

# SQLCHECK mode configuration (use FULL for PL/SQL blocks)
if(DEFINED ENV{PROC_SQLCHECK})
    set(PROC_SQLCHECK "$ENV{PROC_SQLCHECK}")
else()
    set(PROC_SQLCHECK "FULL")  # Use FULL for PL/SQL block support (requires DB connection)
endif()

# Verify required variables (only when FULL or SEMANTICS mode)
if(PROC_SQLCHECK STREQUAL "FULL" OR PROC_SQLCHECK STREQUAL "SEMANTICS")
    if(NOT DEFINED MMS_DBSTRING OR MMS_DBSTRING STREQUAL "" OR MMS_DBSTRING STREQUAL "your_mms_database_string")
        message(FATAL_ERROR "MMS_DBSTRING not set properly for ${PROC_SQLCHECK} check. Set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBID OR MMS_DBID STREQUAL "" OR MMS_DBID STREQUAL "your_mms_database_id")
        message(FATAL_ERROR "MMS_DBID not set properly for ${PROC_SQLCHECK} check. Set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBPASS OR MMS_DBPASS STREQUAL "" OR MMS_DBPASS STREQUAL "your_mms_database_password")
        message(FATAL_ERROR "MMS_DBPASS not set properly for ${PROC_SQLCHECK} check. Set environment variables or create db_config.cmake from template")
    endif()
    message(STATUS "SQLCHECK=${PROC_SQLCHECK} mode enabled - database connection required")
else()
    message(STATUS "SQLCHECK=${PROC_SQLCHECK} mode enabled - no database connection required")
endif()

# Debug info (don't print actual values)
message(STATUS "Database configuration loaded successfully")

# Directory configuration
set(ORG_D ${CMAKE_CURRENT_SOURCE_DIR})
set(BIN_D ${ORG_D}/bin)
set(OBJ_D ${ORG_D}/obj)
set(LIB_D ${ORG_D}/lib)
set(INC_D ${ORG_D}/inc)
set(SRC_D ${ORG_D}/src)
set(CRYPTOPP_D ${ORG_D}/cryptopp)

# Conditionally set include directory path
if(EXISTS "${CMAKE_SOURCE_DIR}/command_5telco_key/inc")
    # Building from top-level project
    set(EXT_INC "${CMAKE_SOURCE_DIR}/command_5telco_key/inc")
    message(STATUS "Build location: top-level project")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_5telco_key/inc")
    # Building from sub-project
    set(EXT_INC "${CMAKE_CURRENT_SOURCE_DIR}/../command_5telco_key/inc")
    message(STATUS "Build location: sub-project")
else()
    # Use CLion project absolute path
    set(EXT_INC "${CMAKE_CURRENT_SOURCE_DIR}/../command_5telco_key/inc")
    message(STATUS "Build location: using relative path")
endif()

# Conditionally set object file path
if(EXISTS "${CMAKE_SOURCE_DIR}/command_5telco_key/obj/sms_ctrlsub++.o")
    # Building from top-level project
    set(EXT_LIB "${CMAKE_SOURCE_DIR}/command_5telco_key/obj/sms_ctrlsub++.o")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_5telco_key/obj/sms_ctrlsub++.o")
    # Building from sub-project
    set(EXT_LIB "${CMAKE_CURRENT_SOURCE_DIR}/../command_5telco_key/obj/sms_ctrlsub++.o")
else()
    # Use relative path
    set(EXT_LIB "${CMAKE_CURRENT_SOURCE_DIR}/../command_5telco_key/obj/sms_ctrlsub++.o")
    message(STATUS "Build location: using relative path (object file)")
endif()

# Path debug output
message(STATUS "EXT_INC: ${EXT_INC}")
message(STATUS "EXT_LIB: ${EXT_LIB}")

# Find Oracle Pro*C compiler
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)
if(NOT PROC_EXECUTABLE)
    message(FATAL_ERROR "Oracle Pro*C compiler not found. Please check ORACLE_HOME environment variable.")
endif()

# Compilation definitions and flags
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# DEBUG level configuration (enable debug code during development)
option(ENABLE_DEBUG "Enable debug output (DEBUG >= 5)" OFF)

# Set DEBUG level based on build type
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug build: DEBUG=5 enabled")
elseif(ENABLE_DEBUG)
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug mode manually enabled (DEBUG=5)")
else()
    add_definitions(-DDEBUG=0)
    message(STATUS "Release build: DEBUG=0 (debug disabled)")
endif()

# Compiler flags configuration - set as individual flags instead of strings
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall")

# Oracle Pro*C preprocessing function
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)

    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # Copy .cpp to .pc
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # DatabaseORA_MMS uses THREADS=YES, others don't
    if(source_name STREQUAL "DatabaseORA_MMS")
        set(THREADS_OPTION "THREADS=YES")
    else()
        set(THREADS_OPTION "")
    endif()

    # SQLCHECK option and userid configuration
    set(SQLCHECK_OPTION "SQLCHECK=${PROC_SQLCHECK}")
    if(PROC_SQLCHECK STREQUAL "FULL" OR PROC_SQLCHECK STREQUAL "SEMANTICS")
        set(USERID_OPTION "userid=${MMS_DBID}/${MMS_DBPASS}@${MMS_DBSTRING}")
    else()
        set(USERID_OPTION "")
    endif()
    set(PARSE_OPTION "PARSE=NONE")  # Changed to PARSE=NONE to resolve header compatibility issues
    set(DEFINE_OPTIONS "define=__sparc")
    set(CTIMEOUT_OPTION "CTIMEOUT=10")  # Increase timeout for database connection

    # Oracle Pro*C preprocessing
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            "PATH=${ORACLE_HOME}/bin:$ENV{PATH}"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V8
            UNSAFE_NULL=YES
            iname=${pc_file}
            include=${INC_D}
            include=${PROC_INCLUDE}
            include=${EXT_INC}
            include=${LIB_D}
            ${THREADS_OPTION}
            CPP_SUFFIX=cpp
            CODE=CPP
            ${PARSE_OPTION}
            ${CTIMEOUT_OPTION}
            ${DEFINE_OPTIONS}
            config=${PROC_CONFIG}
            ${SQLCHECK_OPTION}
            ${USERID_OPTION}
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C (SQLCHECK=${PROC_SQLCHECK})"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# Include directories
include_directories(
    ${INC_D}
    ${LIB_D}
    ${PROC_INCLUDE}
    ${EXT_INC}
    ${CRYPTOPP_D}
)

# Library directories
link_directories(
    ${ORACLE_HOME}/lib
    ${ORACLE_HOME}/plsql/lib
    ${ORACLE_HOME}/network/lib
)

# General library (files without Oracle Pro*C)
add_library(ktc_key_lib STATIC
    ${LIB_D}/Properties.cpp
    ${LIB_D}/myException.cpp
    ${LIB_D}/SocketTCP.cpp
    ${LIB_D}/PacketCtrlKTC_MMS.cpp
    ${LIB_D}/cryptopp.cpp
    ${LIB_D}/xmlParser.cpp
    ${LIB_D}/sha1.c
)

# CLion: explicitly add header files for code navigation
target_sources(ktc_key_lib PRIVATE
    ${LIB_D}/Properties.h
    ${LIB_D}/myException.h
    ${LIB_D}/SocketTCP.h
    ${LIB_D}/PacketCtrlKTC_MMS.h
    ${LIB_D}/cryptopp.h
    ${LIB_D}/xmlParser.h
    ${LIB_D}/xml.h
    ${LIB_D}/sha1.h
    ${LIB_D}/DatabaseORA_MMS.h
)

# DatabaseORA_MMS (requires Oracle Pro*C) - apply special compilation options
add_library(database_ora_mms STATIC)

# Pro*C preprocessing always performed
add_proc_source(database_ora_mms ${LIB_D}/DatabaseORA_MMS.cpp)
message(STATUS "Using DatabaseORA_MMS.cpp with Pro*C (SQLCHECK=${PROC_SQLCHECK})")

# CLion: add original source file reference (doesn't affect build)
target_sources(database_ora_mms PRIVATE
    ${LIB_D}/DatabaseORA_MMS.h
)

# Executable - telco_ktc_mms (requires Oracle Pro*C conversion - uses sql_context)
add_executable(telco_ktc_mms)
add_proc_source(telco_ktc_mms ${SRC_D}/telco_ktc_new_mms.cpp)

# Link configuration
target_link_libraries(telco_ktc_mms
    ktc_key_lib
    database_ora_mms
    ${EXT_LIB}
    clntsh
    crypto
    ssl
    pthread
)

# Output directory configuration
set_target_properties(telco_ktc_mms PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${BIN_D}"
)

# CLion only: target referencing all source files (not built)
if(ENABLE_CLION_BUILD)
    set(ALL_DAEMON_KTC_KEY_SOURCES
        # Library source files
        ${LIB_D}/Properties.cpp
        ${LIB_D}/Properties.h
        ${LIB_D}/myException.cpp
        ${LIB_D}/myException.h
        ${LIB_D}/SocketTCP.cpp
        ${LIB_D}/SocketTCP.h
        ${LIB_D}/PacketCtrlKTC_MMS.cpp
        ${LIB_D}/PacketCtrlKTC_MMS.h
        ${LIB_D}/cryptopp.cpp
        ${LIB_D}/cryptopp.h
        ${LIB_D}/xmlParser.cpp
        ${LIB_D}/xmlParser.h
        ${LIB_D}/xml.h
        ${LIB_D}/sha1.c
        ${LIB_D}/sha1.h
        # Database related files (Pro*C original)
        ${LIB_D}/DatabaseORA_MMS.cpp
        ${LIB_D}/DatabaseORA_MMS.h
        # Executable source
        ${SRC_D}/telco_ktc_new_mms.cpp
        # Header files
        ${INC_D}/stdafx.h
        ${INC_D}/telco_ktc_new_mms.h
    )

    add_custom_target(daemon_ktc_key_all_sources
        SOURCES ${ALL_DAEMON_KTC_KEY_SOURCES}
        COMMENT "CLion reference target for all daemon_ktc_key sources"
    )

    message(STATUS "CLion reference target 'daemon_ktc_key_all_sources' created for better code navigation")
endif()

