cmake_minimum_required(VERSION 3.16)
project(daemon_logon7 CXX)

# Compiler standard and warnings (match friendtalk style)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS ON)  # GNU extensions (gnu++11)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall -std=gnu++11 -w -Wno-deprecated-declarations")

# CLion build option
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()
if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "daemon_logon7: CLion build mode enabled with Oracle 21")
endif()

# Export compile commands for IDEs
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Debug macro (default like makefile had -DDEBUG=3)
set(DEBUG 3 CACHE STRING "Debug level (0=off)")
add_definitions(-DDEBUG=${DEBUG})

# Optional DB config (same pattern as friendtalk)
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()
if(DEFINED ENV{DBSTRING})
    set(DBSTRING "$ENV{DBSTRING}")
endif()
if(DEFINED ENV{DBID})
    set(DBID "$ENV{DBID}")
endif()
if(DEFINED ENV{DBPASS})
    set(DBPASS "$ENV{DBPASS}")
endif()
if(DBSTRING AND DBID AND DBPASS)
    set(HAVE_DB_CREDS TRUE)
    message(STATUS "Database credentials detected for Pro*C semantics check")
else()
    set(HAVE_DB_CREDS FALSE)
    message(STATUS "No DB credentials provided; using SQLCHECK=SYNTAX for Pro*C")
endif()

# Oracle 21 client configuration (matches provided environment guidance)
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG  "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Export Oracle environment for custom commands
set(ENV{ORACLE_HOME} "${ORACLE_HOME}")
set(ENV{LD_LIBRARY_PATH} "${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}")
set(ENV{TNS_ADMIN} "${ORACLE_HOME}/network/admin")

message(STATUS "Oracle 21 configuration:")
message(STATUS "  ORACLE_HOME: ${ORACLE_HOME}")
message(STATUS "  PROC_INCLUDE: ${PROC_INCLUDE}")
message(STATUS "  PROC_CONFIG: ${PROC_CONFIG}")

# Find Pro*C compiler
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin NO_DEFAULT_PATH)
if(NOT PROC_EXECUTABLE)
    message(FATAL_ERROR "Oracle Pro*C (proc) not found in ${ORACLE_HOME}/bin")
endif()
message(STATUS "Using Pro*C: ${PROC_EXECUTABLE}")

# Common include directories
include_directories(
    inc
    lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../command_mms/inc
    ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb/inc
    ${PROC_INCLUDE}
    $ENV{HOME}/library
)

# Common link directories
link_directories(
    ${ORACLE_HOME}/lib
    ${ORACLE_HOME}/plsql/lib
    ${ORACLE_HOME}/network/lib
    $ENV{HOME}/library
    ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/orapp
)

# Common compile definitions
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
    -DORACLE_21
)

# Oracle compile flags passed to targets that require Pro*C
set(ORACLE_COMPILE_FLAGS
    "-DORACLE_21"
    "-D_LOGON_MODE"
)

# Helper: process a .cpp with Pro*C when EXEC SQL is present
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)
    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} -> ${pc_file}"
    )

    if(HAVE_DB_CREDS)
        set(_SQLCHECK "SEMANTICS")
        set(_USERID "userid=${DBID}/${DBPASS}@${DBSTRING}")
    else()
        set(_SQLCHECK "SYNTAX")
        set(_USERID "")
    endif()

    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:${ORACLE_HOME}/plsql/lib:${ORACLE_HOME}/network/lib"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            "PATH=${ORACLE_HOME}/bin:$ENV{PATH}"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${CMAKE_CURRENT_SOURCE_DIR}/inc
            include=${PROC_INCLUDE}
            include=${CMAKE_SOURCE_DIR}/libsrc/libkskyb/inc
            CPP_SUFFIX=cpp
            CODE=CPP
            PARSE=NONE
            CTIMEOUT=3
            config=${PROC_CONFIG}
            SQLCHECK=${_SQLCHECK}
            ${_USERID}
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Pro*C processing ${pc_file}"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# Build a static lib from lib/*.cpp (excluding DatabaseORA_MMS*.cpp handled via Pro*C)
file(GLOB DAEMON_LIB_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/lib/*.cpp")
list(FILTER DAEMON_LIB_SOURCES EXCLUDE REGEX ".*DatabaseORA_MMS.*\\.cpp$")
if(DAEMON_LIB_SOURCES)
    add_library(daemon_logon7_lib STATIC ${DAEMON_LIB_SOURCES})
    target_include_directories(daemon_logon7_lib PUBLIC
        inc
        lib
        ${CMAKE_CURRENT_SOURCE_DIR}/../command_mms/inc
        ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb/inc
        ${PROC_INCLUDE}
        $ENV{HOME}/library
    )
endif()

# Database Pro*C static libraries (require DB credentials when PL/SQL is used)
if(HAVE_DB_CREDS)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)
        add_library(database_ora_mms STATIC)
        add_proc_source(database_ora_mms ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)
        target_compile_options(database_ora_mms PRIVATE ${ORACLE_COMPILE_FLAGS})
    endif()
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS_Key.cpp)
        add_library(database_ora_mms_key STATIC)
        add_proc_source(database_ora_mms_key ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS_Key.cpp)
        target_compile_options(database_ora_mms_key PRIVATE ${ORACLE_COMPILE_FLAGS})
    endif()
else()
    message(STATUS "Skipping Pro*C database static libraries (no DB credentials)")
endif()

# Convenience function to add an executable; if file contains EXEC SQL, use Pro*C
function(add_logon_exe exe_name src_rel)
    set(src_abs ${CMAKE_CURRENT_SOURCE_DIR}/${src_rel})
    if(NOT EXISTS ${src_abs})
        message(STATUS "Skipping ${exe_name}: missing source ${src_rel}")
        return()
    endif()

    file(READ ${src_abs} CONTENTS)
    string(FIND "${CONTENTS}" "EXEC SQL" HAS_EXEC_SQL)
    if(HAS_EXEC_SQL GREATER -1 AND NOT HAVE_DB_CREDS)
        message(STATUS "Skipping ${exe_name}: Pro*C requires DB credentials")
        return()
    endif()

    add_executable(${exe_name})
    target_include_directories(${exe_name} PRIVATE
        inc lib ${CMAKE_CURRENT_SOURCE_DIR}/../command_mms/inc ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb/inc ${PROC_INCLUDE}
    )

    if(HAS_EXEC_SQL GREATER -1)
        add_proc_source(${exe_name} ${src_abs})
        target_compile_options(${exe_name} PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(${exe_name} PRIVATE ${src_rel})
    endif()

    # Common links
    target_link_libraries(${exe_name} PRIVATE daemon_logon7_lib pthread dl)
    if(TARGET database_ora_mms)
        target_link_libraries(${exe_name} PRIVATE database_ora_mms)
    endif()
    if(TARGET database_ora_mms_key)
        target_link_libraries(${exe_name} PRIVATE database_ora_mms_key)
    endif()
    # External libs (present in $HOME/library or system)
    target_link_libraries(${exe_name} PRIVATE kssocket ksconfig)
    find_library(ORAPP_LIB NAMES orapp PATHS ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/orapp)
    if(ORAPP_LIB)
        target_link_libraries(${exe_name} PRIVATE ${ORAPP_LIB})
    endif()
    find_library(CLNTSH_LIB NAMES clntsh PATHS ${ORACLE_HOME}/lib)
    if(CLNTSH_LIB)
        target_link_libraries(${exe_name} PRIVATE ${CLNTSH_LIB})
    endif()
endfunction()

# Targets (reflecting makefile's 'all' list)
add_logon_exe(logonSession          src/logonSession.cpp)
add_logon_exe(logonDB               src/logonDB.cpp)
add_logon_exe(admin                 src/admin.cpp)
add_logon_exe(senderMMSDB           src/senderMMSDB.cpp)
add_logon_exe(reportMMSDB           src/reportMMSDB.cpp)
add_logon_exe(monitorProcess        src/monitorProcess.cpp)
add_logon_exe(adminProcess          src/adminProcess.cpp)
add_logon_exe(senderMMSProcKey      src/senderMMSProcKeyDB.cpp)
add_logon_exe(reportMMSProcess      src/reportMMSProcess.cpp)
add_logon_exe(bcFileReceiver        src/bcFileReceiver.cpp)
add_logon_exe(senderMMSProTalk      src/senderMMSProcessDBTalk.cpp)

# Some executables need extra libs according to makefile (ksthread, ksbase64)
foreach(tgt IN ITEMS monitorProcess adminProcess senderMMSDB senderMMSProcKey senderMMSProTalk)
    if(TARGET ${tgt})
        target_link_libraries(${tgt} PRIVATE ksthread)
    endif()
endforeach()
foreach(tgt IN ITEMS adminProcess senderMMSDB senderMMSProcKey senderMMSProTalk reportMMSProcess)
    if(TARGET ${tgt})
        target_link_libraries(${tgt} PRIVATE ksbase64)
    endif()
endforeach()

