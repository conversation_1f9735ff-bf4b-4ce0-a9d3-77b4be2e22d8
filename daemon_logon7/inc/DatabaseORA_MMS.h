/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <stdafx.h>
#include <string>
#include <set>
#include "senderDbInfo.h"
#include "reportDbInfo.h"

using namespace std;
#include <ml_ctrlsub.h>

extern char _DATALOG[64];

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int connectToOracle(char*, char*);
	int closeFromOracle();
	int commitOracle();
	int rollbackOracle();
	int getCTNID();
	int setMMSCTNTBL(CSenderDbMMSCTNTBL &ctn_data);
	int setMMSTBL(CSenderDbMMSTBL &mms_data);
	int setMMSMSG(CSenderDbMMSMSG &que_data);
	int getReportDB(CReportDbInfo &rpt_data);
	int setRPTTBL(CSenderDbMMSRPTTBL &rpt_data);
	int selectTblCallback(set<string>& set_callback_list, int _pid);
	int selectAllowDialCode(set<string>& set_dialcode_list, char *_dial_code_type);
	char* trim(char* szOrg, int leng);
	int setMMSMSG_TALK(CSenderDbMMSMSG_TALK &que_data);
	int getMMSID();


private:
	char tmpLog3[1024];
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
};

}

#endif /* DATABASEORA_H_ */
