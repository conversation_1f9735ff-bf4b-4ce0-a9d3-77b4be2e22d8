/*****************************************************************************
 * File Name   : stdafx.h
 * Author      : <PERSON><PERSON><PERSON>,Lim - kskyb.com
 * Date        : 2004.08.06
 * Description : Common Header & Define
 *****************************************************************************/
#ifndef _STDAFX_H_
#define _STDAFX_H_
#include "stdsms.h"

#include "command_util.h"

#define SOCKET_BUFF 2048
/* viewPack */
#define VIEWPACK_MAX_SIZE 2048

#define CREATE_FLAGS (O_WRONLY | O_CREAT | O_APPEND )
#define CREATE_PERMS (S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH )

int _logPrint(char* szPath, char* szLog);
int _monPrint(char* szPath, char* szLog);

void get_timestring(char *fmt, long n, char *s);
void get_timestring2(char *s);


int SendData(int sd, const char *ptr, int nbytes);
int RecvData(int sd, char *ptr, int nbytes);
void log_history(int type, int errornum, const char *format, ...);
void wait_a_moment(int interval);
void monitoring(char *buf, int st, int err);
void logmessage(char *buf, int st, int err);
char* disnull(char* szOrg, int leng);
char* trim(char* szOrg, int leng);
int chkNum(char *inp);
void sql_error(char* msg);
void Init_Server();
void Init_Server_Fork();
int UnixSockOpenNon(int* sockfd, int svrPort);
int UnixSockOpenNon(int* sockfd, char* svrIp, int svrPort);
void sig_rtn(int sig);
void sig_rtn1(int sig);
void viewPack(char *a,int n);
void viewPackPrint(char *a,int n);
int str2int(char* buf, int size);
int numchk(char* number, int len);

#endif


