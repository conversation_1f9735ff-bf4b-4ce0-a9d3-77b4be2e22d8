#ifndef _LOGON_DB_H_
#define _LOGON_DB_H_

#include <iostream>
#include <set>

#include "stdafx.h"
#include "kssocket.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "ksconfig.h"
#include "packetUtil.h"

extern "C" {
#include <oci.h>
}

#include "orapp.hh"



using namespace std;


int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];



class CConfigLogonDB{
    public:
        char logonDBName[64];
        char dbID[16];
        char dbPASS[16];
        char dbSID[16];
};

CConfigLogonDB gConf;


bool orapp_connect(ORAPP::Connection &db, const char *tns, const char *user, const char *pass); 
bool orapp_disconnect(ORAPP::Connection &db);
int checkLogon(ORAPP::Connection &db,char* buff,int type);
int checkLogonMMS(ORAPP::Connection &db,char* buff,int type);
int configParse(char* file);
int classifyProtocol(ORAPP::Connection &db,CKSSocket& newSockfd,int size,char* bindPacket);
int logonType(ORAPP::Connection &db,CKSSocket& newSockfd,int type,char* bindPacket);
int logonTypeMMS(ORAPP::Connection &db,CKSSocket& newSockfd,int type,char* bindPacket);

/* 변작금지 기능 개발에 의한 추가 함수*/
int getCallback(ORAPP::Connection &db,CKSSocket& newSockfd,int type,char* bindPacket);
int loadCallback(ORAPP::Connection &db, char* buff, int type, set<string> &set_callback_list);
int getDialCode(ORAPP::Connection &db, CKSSocket& newSockfd, int type, char* bindPacket);
int loadDialCode(ORAPP::Connection &db, char* buff, int type, set<string> &set_dialcode_list);

#endif


