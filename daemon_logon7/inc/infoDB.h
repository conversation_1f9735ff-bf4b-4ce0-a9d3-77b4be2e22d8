#ifndef _LOGON_DB_H_
#define _LOGON_DB_H_

#include <iostream>
#include "stdafx.h"
#include "kssocket.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "ksconfig.h"

extern "C" {
#include <oci.h>
}

#include "orapp.hh"



using namespace std;


int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];



class CConfiginfoDB{
    public:
        char infoDBName[64];
};

CConfigLogonDB gConf;


bool orapp_connect(ORAPP::Connection &db, const char *tns, const char *user, const char *pass); 
bool orapp_disconnect(ORAPP::Connection &db);
int checkLogon(ORAPP::Connection &db,char* buff);
int configParse(char* file);
int classifyProtocol(ORAPP::Connection &db,CKSSocket& newSockfd,int size,char* bindPacket);
int logonType5(ORAPP::Connection &db,CKSSocket& newSockfd,int size,char* bindPacket);

#endif


