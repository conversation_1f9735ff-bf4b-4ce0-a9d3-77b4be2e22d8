/*****************************************************************************
 * File Name   : stdafx.h
 * Author      : <PERSON><PERSON><PERSON>,Lim - kskyb.com
 * Date        : 2004.08.06
 * Description : Common Header & Define
 *****************************************************************************/
#ifndef _STDSMS_H_
#define _STDSMS_H_
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <signal.h>
#include <fcntl.h>
#include <stdarg.h>
#include <unistd.h>

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/types.h>
#include <sys/wait.h>


#include <sys/socket.h>
#include <arpa/inet.h>

#include <pthread.h>

#include <syslog.h>
#include <sys/stat.h>

#define  CCL(a)              memset(a,0,sizeof(a))
#define  MAX_TCP_BUF         4096

#define  TRUE                1
#define  FALSE               0

#define  true                1
#define  false               0



#endif


