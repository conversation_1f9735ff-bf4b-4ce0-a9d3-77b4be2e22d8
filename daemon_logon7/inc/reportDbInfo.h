#ifndef _REPORT_DB_INFO_H_
#define _REPORT_DB_INFO_H_

#include "smsPacketStruct.h"
#include "logonDbInfo.h"

/* proc_get_report_time 
 *
 * type 
 * 1 : reportProcess -> reportDB 
 * 전송결과(레포트) 요청
 *
 * 2 : reportProcess <- reportDB
 * 전송결과 전송
 *
 * 3 : reportProcess -> reportDB
 * 전송결과 재입력 ( 고객사로 레포트전송중 오류 발생시 )
 * 2번 전문 내용을 그대로 type 만 수정 해서 전송해야 된다.
 *
 * 4 : reportProcess <- reportDB
 * 3번 전문 내용을 유지한채 type 만 수정 
 * 재입력 Ack
 *
 * */
/*
	char msgType[ 2];
	char msgLeng[ 4];
*/


class CReportDbInfo {
    public:
        TypeHeader header;
        long long nMsgId;
        int nJobCode;
        int nTelcoId;
        int nCnt;
        int nPtnId;
        char szAppName[10+1];
        char szPtnsn[16+1];
        char szResCode[16+1];
        char szResvData[200+1]; /* report_msg_time: object type : oracle */
        char szDstAdr[12+1];
        char szEndTelco[8+1];
        char szRptDate[16];
        int nSqlCode;
        int result;
        char szCid[12+1]; /* report_mms_msg: object type: oracle */
};

#endif

