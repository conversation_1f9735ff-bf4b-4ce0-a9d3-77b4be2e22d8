/** @file   mmsPacketUtil.h
 * @brief   mmsPacket 처리 유틸 헤드 파일 
 * @date    2008-01-23
 * <AUTHOR>
 */

#ifndef _KSKYB_MMS_PACKET_UTIL_H_
#define _KSKYB_MMS_PACKET_UTIL_H_

#include <stdio.h>
#include <string>
#include <list>

using namespace std;

class CContentType {
    public:
        string strType;
        string strBoundary;
        string strCharset;
        string strName;
};

class CMData {
    public:
        CContentType contentType;
        string strEncoding;
        string strSvc;
        string strData;
};

// CMMSPacketSend
// CMMSPacketBase
// CMMSPacketAck
// CMMSPacketRpt
// CMMSPacketRak
class CMMSPacketUtil {
    private:
        typedef list<string>::iterator listBcastDataPosition;
        typedef list<CMData>::iterator listMdataPosition;

        string strKey;
        string strExtend;
        string strSubject;
        string strReceiver;
        string strSender;
        string strContentCnt;
        string strPsktyn;
        string strPktfyn;
        string strPlgtyn;
        string strTextCnt;
        string strImgCnt;
        string strAudCnt;
        string strMpCnt;
        string strBcastCnt;
        list<string> listBcastData;
        string strMDataVersion;
        CContentType contentType;
        list<CMData> listMData;
        string strErrorMsg;

    public:
        int parse(char* data);
        void display();
    private:
        void parseBcastData(char* szOrg);
        void parseMData(char* szOrg);

        char* setMimeVersion(char* szOrg);
        char* setMDataHeader(char* szOrg);
        char* setMDataBody(char* szOrg,char* szTag);
        

        char* getContentType(char* szOrg,CContentType &contentType);
        char* getEachMData(char*szOrg, char* szTag, char* szBoundary, string &data);
        char* getContentEncoding(char* szOrg, char* szTag, CMData &mData);
        char* getContentSvc(char* szOrg, char* szTag, CMData &mData);

        char* getEachMDataBodyHeader(char* szOrg, char* szTag,string &strMDataBodyHeader);
        char* getEachMDataBodyData(char* szOrg, char* szTag,string &strMDataBodyData);

        int getCMData(CMData& mData,char* header, char* body);

        // findLine
        char* matchString(char* szOrg, char* szTag, string &szVal);
        // findField
        char* findFirstField(char* szOrg, char* szTag, string &szVal);
        char* findOtherField(char* szOrg, char* szTag, string &szVal);

    public:
        char* getKeyValue();
};

#endif



/*
 *
 * content-svc 
 * 
 * text 일경우
 *
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_KTF
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_LGT
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_SKT
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_SSN
 *
 */
