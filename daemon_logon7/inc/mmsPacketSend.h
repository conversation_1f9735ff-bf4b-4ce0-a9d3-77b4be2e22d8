/** @file   mmsPacketUtil.h
 * @brief   mmsPacket 처리 유틸 헤드 파일 
 * @date    2008-01-23
 * <AUTHOR>
 */

#ifndef _KSKYB_MMS_PACKET_UTIL_H_
#define _KSKYB_MMS_PACKET_UTIL_H_

#include <stdio.h>
#include <string>
#include <list>
#include <vector>
#include "mmsPacketBase.h"

using namespace std;

class CContentType {
    public:
        string strType;
        string strBoundary;
        string strCharset;
        string strName;
};

class CMData {
    public:
        CContentType contentType;
        string strEncoding;
        string strSvc;
        string strData;
        int cntid;
        int ctnseq;
};

class CBcastData {
    public:
        string strSeq;
        string strKey;
        string strExtend;
        string strReceiver;
};


// CMMSPacketSend
// CMMSPacketBase
// CMMSPacketAck
// CMMSPacketRpt
// CMMSPacketRak
class CMMSPacketSend : public CMMSPacketBase {
    private:
        typedef list<CBcastData>::iterator listCBcastDataPosition;
        typedef list<string>::iterator listBcastDataPosition;
        typedef list<CMData>::iterator listMdataPosition;


        listMdataPosition m_MDataPos;
        listCBcastDataPosition m_BcastData;

        string strId;
        string strKey;
        string strExtend;
        string strSubject;
        string strReceiver;
        string strSender;
        string strContentCnt;
        string strPsktyn;
        string strPktfyn;
        string strPlgtyn;
        string strTextCnt;
        string strImgCnt;
        string strAudCnt;
        string strMpCnt;
        string strBcastCnt;
		string strSenderKey;
		string strTmplCd;
        list<CBcastData> listCBcastData;
        list<string> listBcastData;
        string strMDataVersion;
        CContentType contentType;
        list<CMData> listMData;
        string strErrorMsg;
        string strIdCode;       // 식별코드
        int ctnseq;
        int nCtnType;
        int nCtnTypeCnt;
        int nTextCnt;
        int nImgCnt;
        int nAugCnt;
        int nMpCnt;

    public:
        CMMSPacketSend();
        ~CMMSPacketSend();
        int parse(char* data);
        int getCtnType();
        void display();
    private:
        void parseBcastData(char* szOrg);
        void pushBcastData(string data);


//        void parseMData(char* szOrg);				// 20140218 return int 
        int  parseMData(char* szOrg);

        char* setMimeVersion(char* szOrg);
        char* setMDataHeader(char* szOrg);
        char* setMDataBody(char* szOrg,char* szTag);


        void setCtnType();
        

        char* getContentType(char* szOrg,CContentType &contentType);
        char* getEachMData(char*szOrg, char* szTag, char* szBoundary, string &data);
        char* getContentEncoding(char* szOrg, char* szTag, CMData &mData);
        char* getContentSvc(char* szOrg, char* szTag, CMData &mData);

        char* getEachMDataBodyHeader(char* szOrg, char* szTag,string &strMDataBodyHeader);
        char* getEachMDataBodyData(char* szOrg, char* szTag,string &strMDataBodyData);

        int getCMData(CMData& mData,char* header, char* body);

        // findLine
        char* matchString(char* szOrg, char* szTag, string &szVal);
        // findField
        char* findFirstField(char* szOrg, char* szTag, string &szVal);
        char* findOtherField(char* szOrg, char* szTag, string &szVal);

    public:
        char* getIdValue();
        char* getKeyValue();
        char* getExtendValue();
        char* getSubjectValue();
        char* getReceiverValue();
        char* getSenderValue();
        char* getContentCntValue();
        char* getPsktynValue();
        char* getPktfynValue();
        char* getPlgtynValue();
        char* getTextCntValue();
        char* getImgCntValue();
        char* getAudCntValue();
        char* getMpCntValue();
        char* getBcastCntValue();
		char* getSenderKeyValue();
		char* getTmplCdValue();
        char* getErrorMsg();
        char* getIdCode();


        int getTextCnt() { return nTextCnt; }
        int getImgCnt() { return nImgCnt; }
        int getAugCnt() { return nAugCnt; }
        int getMpCnt() { return nMpCnt; }

        int getCtnSeq();

        int getMDataFirst(CMData& mData);
        int getMDataNext(CMData& mData);

        int getBcastFirst(CBcastData& bCastData);
        int getBcastNext(CBcastData& bCastData);

        inline string trim_left(const string& str)
        {
            int n = str.find_first_not_of(" \t\v\n");
            return n == string::npos ? str : str.substr(n, str.length());
        }

        inline string trim_right(const string& str)
        {
            int n = str.find_last_not_of(" \t\v\n");
            return n == string::npos ? str : str.substr(0, n + 1);
        } 

        void split(std::string& text, std::string& separators, std::vector<std::string>& words);

};

#endif



/*
 *
 * content-svc 
 * 
 * text 일경우
 *
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_KTF
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_LGT
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_SKT
 * [김은영:졸리]가볍게~비워버리고!님의 말:
 * TXT_SSN
 *
 */
