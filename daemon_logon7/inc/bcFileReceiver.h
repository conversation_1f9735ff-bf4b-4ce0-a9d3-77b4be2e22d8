#ifndef _BC_FILE_RECEIVER_H_
#define _BC_FILE_RECEIVER_H_

#include <iostream>
#include "stdafx.h"
#include "smsPacketStruct.h"
#include "kssocket.h"
#include "logonDbInfo.h"
#include "ksconfig.h"

#include        <sqlca.h>

using namespace std;

#define N_SIZE_MSG_COMMON sizeof(TypeCommon)

#define CMD_INIT 600
#define CMD_ACK_INIT 610
#define CMD_START 1
#define CMD_START_ACK 1
#define CMD_FILEDATA_END 3
#define CMD_FILEDATA_END_ACK 3
#define CMD_END 4
#define CMD_END_ACK 4

#define CMD_FILEINFO 630
#define CMD_FILEINFO_ACK 640
#define CMD_DATASEND 320
#define CMD_RETIREDREQ 620
#define CMD_RETIRED_ACK 300
#define CMD_RETIRED_DATA 310

#define SIZE_CMD_START_ACK sizeof(TypeMsg06000610)
#define SIZE_CMD_FILEDATA_END_ACK sizeof(TypeMsg06000610)
#define SIZE_CMD_END_ACK sizeof(TypeMsg06000610)
#define SIZE_CMD_FILEINFO_ACK sizeof(TypeMsg06300640)
#define SIZE_CMD_RETIRED_ACK sizeof(TypeMsg0300)



typedef struct _TYPE_COMMON { /* common */
	char totalLen[  4];
	char codeUpmu[  3];
	char codeSender[2];
	char codeJunmun[4];
	char codeGubun;
	char flagSend;
	char szFilename[8];
	char szAck[     3];
} TypeCommon;



typedef struct _MSG06000610 {
	TypeCommon header;
	char szSendDate[10];
	char szInfoUpmu[ 3];
	char szSender[  20];
	char szPassword[ 16];
} TypeMsg06000610;

typedef struct _MSG0620 {
	TypeCommon header;
	char szBlockNo[4];
	char szSequenceNo[ 3];
} TypeMsg0620;

typedef struct _MSG06300640 {
	TypeCommon header;
	char szFileInfo[24];
} TypeMsg06300640;

typedef struct _MSG0300 {
	TypeCommon header;
	char szBlockNo[4];
	char szSequenceNo[3];
	char szRetiredCnt[3];
	char szRetiredInfo[100];
} TypeMsg0300;

typedef struct _MSG03100320 {
	TypeCommon header;
	char szBlockNo[4];
	char szSequenceNo[3];
	char szDataByte[4];
} TypeMsg03100320;

typedef struct _MSG031000320DATA {
	char szBlockNo[4];
	char szSequenceNo[3];
	char szDataByte[4];
	char szData[4096];
} TypeMsg03100320DATA;

//data_type
#define DATA_TP_HEAD	"BH"
#define DATA_TP_BODY	"BD"
#define DATA_TP_TAIL	"BT"

#define SZ_DATA_TP	2

typedef struct _TYPE_HEAD { /* common */
	char data_type[  2];
	char std_date [  8];
	char data_id  [  5];
} TypeHead;
#define SZ_TYPE_HEAD sizeof(TypeHead)

typedef struct _MSGBL501 {
	char data_type[  2];
	char cust_no  [  9];
	char filler   [  4];
} TypeMsgBL501;
#define SZ_MSGBL501 sizeof(TypeMsgBL501)

typedef struct _TYPE_TAIL {
	char data_type[  2];
	char body_cnt [  8];
	char filler   [  5];
} TypeTail;
#define SZ_TYPE_TAIL sizeof(TypeTail)

class CConfigReceiver{
    public:
        char serverIP[16];
        int serverPORT;
        int process_sleep_time;
        char logonDBName[64];
        char bindir[64];
        char cfgdir[64];
        char logPath[64];
        char domainPath[64];
		char datadir[64];
        char dbID[16];
        char dbPASS[16];
        char dbSID[16];
		int file_datasize;
};

int activeProcess = TRUE;
struct _message_info    message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


CConfigReceiver gConf;

int getAccept(int hOpenSocket,char* szIP,int nPort);
int requestLogon(int sockfd,CLogonDbInfo &logonDbInfo);
int checkServerInfo(CLogonDbInfo &logonDbInfo,char* szIP,int nPort);
int checkLoginDup(CLogonDbInfo &logonDbInfo);
int configParse(char* file);
int readmsg(CKSSocket newSockfd,char* buff,int total_size, int wait_sec);
int classfyCMD(char* buff);
int readPacket(CKSSocket newSockfd,char* buff, int wait_sec);
int ackPacket(CKSSocket newSockfd,char* ret_buff,int cmd, int send_size, char* ack);
int reciveFile(CKSSocket newSockfd,char* buff_init);


#endif


