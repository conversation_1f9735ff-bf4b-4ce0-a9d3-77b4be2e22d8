COPY=cp
#DBSTRING=NEO228
DBSTRING=NEO223
DBID=neomms2
DBPASS=password

PROC=proc
CC=g++
RM=rm
LINT=lint
#CFLAGS = -g -D_DEBUG -lrt
#CFLAGS = -g -lrt
#CFLAGS = -g -lrt -DDEBUG=5 
CFLAGS = -std=gnu++11 -g -lrt -DDEBUG=5

#ORG_D=${HOME}/daemon_logon7
ORG_D=${HOME}/CLionProjects/neomms_248/daemon_logon7
#BIN_D=${ORG_D}/bin
BIN_D=${ORG_D}/bin_tmp
INST_D=$(ORG_D)/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

#EXT_LIB=/user/neomms/command_mms/obj/sms_ctrlsub++.o
EXT_LIB=${HOME}/CLionProjects/neomms_248/command_mms/obj/sms_ctrlsub++.o
#EXT_INC=/user/neomms/command_mms/inc
EXT_INC=${HOME}/CLionProjects/neomms_248/command_mms/inc

#EXT_SVC_LIB=/user/neomms/command_svc/obj/sms_ctrlsub++.o
EXT_SVC_LIB=${HOME}/CLionProjects/neomms_248/command_mms/obj/sms_ctrlsub++.o
#EXT_SVC_INC=/user/neomms/command_svc/inc
EXT_SVC_INC=EXT_SVC_INC=/user/neomms/command_svc/inc/inc

INCLUDE = -I$(INC_D) 

KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library

ORACLE_VERSION  = 9
#ORACLE_INCLUDES = -I$(ORACLE_HOME)/rdbms/demo -I$(ORACLE_HOME)/rdbms/public
ORACLE_INCLUDES = -I/usr/include/oracle/21/client64
ORACLE_LIBS     = -L$(ORACLE_HOME)/lib

GEN_FLAGS    =  -fno-exceptions -fno-rtti -D_REENTRANT=1
GEN_INCLUDES =
GEN_LIBS     = -lorapp -ldl -lclntsh  -lpthread

BC_FILE_RECEIVER_OBJ = $(OBJ_D)/bcFileReceiver.o \
		    $(OBJ_D)/cust_lib_common.o
		    
BC_FILE_RECEIVER_TEST_OBJ = $(OBJ_D)/bcFileReceiver_TEST.o \
		    $(OBJ_D)/cust_lib_common.o

BC_FILTER_RECEIVER_OBJ = $(OBJ_D)/bcFilterReceiver.o \
		    $(OBJ_D)/cust_lib_common.o
		    
LOGON_SESSION_MMSB_OBJ = $(OBJ_D)/logonSessionMMSB.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o

LOGON_SESSION_MMSB_PART_OBJ = $(OBJ_D)/logonSessionMMSB_PART.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o

LOGON_SESSION_MMSBS_OBJ = $(OBJ_D)/logonSessionMMSBS.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcessBS.o\
		    $(OBJ_D)/monitor.o\
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/adminUtil.o

LOGON_SESSION_OBJ = $(OBJ_D)/logonSession.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o

SENDERPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/monitor.o

SENDERMMSPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/monitor.o\
		    $(OBJ_D)/checkCallback.o

SENDERMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o\
		    $(OBJ_D)/checkCallback.o

SENDERMMSPROCESSDB_KEY_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/DatabaseORA_MMS_Key.o\
		    $(OBJ_D)/monitor.o\
		    $(OBJ_D)/checkCallback.o
REPORTPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/monitor.o

REPORTMMSPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/monitor.o

REPORTMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
#ORA_INC = ${ORACLE_HOME}/precomp/public
ORA_INC = /usr/include/oracle/21/client64

INCLUDE =   $(PRECOMPPUBLIC) -I$(INC_D) -I$(ORA_INC)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC)

ORALIB = -lclntsh

LIBS = -lnsl  -lpthread



all: 	logonSession \
	logonDB \
	admin \
	senderMMSDB \
	reportMMSDB \
	monitorProcess \
	adminProcess \
	senderMMSProcKey \
	reportMMSProcess \
	bcFileReceiver
#	senderMMSProTalk	
#	senderMMSDB_PART \
#	senderMMSProcess \
#	logonSessionMMSBS \
#	logonSessionMMSB \
#	logonSessionMMSB_PART \
#	bcFileReceiver_TEST \

logonDB: $(OBJ_D)/logonDB.o $(OBJ_D)/cust_lib_common.o $(OBJ_D)/packetUtil.o
	${CC} $(CFLAGS)  $(GEN_FLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig  $(ORACLE_LIBS) $(GEN_LIBS)  ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

infoDB: $(OBJ_D)/infoDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)   $(GEN_FLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig  $(ORACLE_LIBS) $(GEN_LIBS)  ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderDB: $(OBJ_D)/senderDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@


senderMMSDB: $(OBJ_D)/senderMMSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderMMSDB_PART: $(OBJ_D)/senderMMSDB_PART.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportDB: $(OBJ_D)/reportDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportMMSDB: $(OBJ_D)/reportMMSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

bcFileReceiver: $(BC_FILE_RECEIVER_OBJ)
	${CC} $(CFLAGS)  $^ $(EXT_SVC_LIB) -I$(EXT_SVC_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

bcFileReceiver_TEST: $(BC_FILE_RECEIVER_TEST_OBJ)
	${CC} $(CFLAGS)  $^ $(EXT_SVC_LIB) -I$(EXT_SVC_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

bcFilterReceiver: $(BC_FILTER_RECEIVER_OBJ)
	${CC} $(CFLAGS)  $^ $(EXT_SVC_LIB) -I$(EXT_SVC_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

logonSession: $(LOGON_SESSION_OBJ) 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

logonSessionMMSB: $(LOGON_SESSION_MMSB_OBJ) 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@
#	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

logonSessionMMSB_PART: $(LOGON_SESSION_MMSB_PART_OBJ)
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

logonSessionMMSBS: $(LOGON_SESSION_MMSBS_OBJ) 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig  -lksbase64 ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

monitorProcess: $(OBJ_D)/monitorProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@


adminProcess: $(OBJ_D)/adminProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread -lksconfig -lksbase64 ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

#senderMMSProcess: $(SENDERMMSPROCESS_OBJ) $(OBJ_D)/senderMMSProcess.o
#	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderMMSProcess: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderMMSProcessDB.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@
	
senderMMSProcKey: $(SENDERMMSPROCESSDB_KEY_OBJ) $(OBJ_D)/senderMMSProcKeyDB.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@	

senderMMSProcessDB: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderMMSProcessDB.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderMMSProTalk: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderMMSProTalk.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderProcess: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderProcess.o
	${CC} $(CFLAGS)   $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderKNProcess: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderKNProcess.o
	${CC} $(CFLAGS)   $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig -lksseedbyte ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@


senderProcess_v3: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderProcess_v3.o
	${CC} $(CFLAGS)   -DVER3 $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderProcess_v4: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderProcess_v4.o
	${CC} $(CFLAGS)   -DVER4 $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@ 
senderProcess_v5: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderProcess_v5.o
	${CC}  $(CFLAGS)  -DVER5 $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderProcess_v6: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderProcess_v6.o
	${CC}  $(CFLAGS)  -DVER6 $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderProcess_v8: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderProcess_v8.o
	${CC}  $(CFLAGS)  -DVER8 $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

#reportMMSProcess: $(REPORTMMSPROCESS_OBJ) $(OBJ_D)/reportMMSProcess.o
#	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportMMSProcess: $(REPORTMMSPROCESSDB_OBJ) $(OBJ_D)/reportMMSProcessDB.o
	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportProcess: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportProcess.o
	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportKNProcess: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportKNProcess.o
	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@



reportProcess_v3: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportProcess_v3.o
	${CC}  $(CFLAGS)  -DVER3  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportProcess_v4: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportProcess_v4.o
	${CC}  $(CFLAGS)  -DVER4  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportProcess_v5: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportProcess_v5.o
	${CC}  $(CFLAGS)  -DVER5  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportProcess_v6: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportProcess_v6.o
	${CC} $(CFLAGS)   -DVER6  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportProcess_v8: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportProcess_v8.o
	${CC} $(CFLAGS)   -DVER8  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@


admin: $(OBJ_D)/admin.o $(OBJ_D)/adminUtil.o
	${CC} $(CFLAGS)  $^ $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket  -lksconfig ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

$(OBJ_D)/logonDB.o: $(SRC_D)/logonDB.cpp
	$(RM) -rf $@
	$(CC) $(CFLAGS)   $(GEN_FLAGS) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(ORACLE_INCLUDES) -c $^

$(OBJ_D)/infoDB.o: $(SRC_D)/infoDB.cpp
	$(RM) -rf $@
	$(CC) $(CFLAGS)   $(GEN_FLAGS) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(ORACLE_INCLUDES) -c $^

#$(OBJ_D)/senderDB.o: $(SRC_D)/senderDB.cpp
#	$(RM) -rf $@
#	$(CC) -O2  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(ORACLE_INCLUDES) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o ${LINKFLAGS} $(ORALIB) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp
	
$(OBJ_D)/DatabaseORA_MMS_Key.o: $(LIB_D)/DatabaseORA_MMS_Key.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS_Key.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS_Key.cpp $(OBJ_D)/DatabaseORA_MMS_Key.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS_Key.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS_Key.o ${LINKFLAGS} $(ORALIB) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS_Key.cpp

$(OBJ_D)/senderMMSProcessDB.o: $(SRC_D)/senderMMSProcessDB.cpp
	$(RM) -rf $(OBJ_D)/senderMMSProcessDB.*
	$(COPY) $(SRC_D)/senderMMSProcessDB.cpp $(OBJ_D)/senderMMSProcessDB.pc
	$(PROC) iname=$(OBJ_D)/senderMMSProcessDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderMMSProcessDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderMMSProcessDB.cpp
	
$(OBJ_D)/senderMMSProcKeyDB.o: $(SRC_D)/senderMMSProcKeyDB.cpp
	$(RM) -rf $(OBJ_D)/senderMMSProcKeyDB.*
	$(COPY) $(SRC_D)/senderMMSProcKeyDB.cpp $(OBJ_D)/senderMMSProcKeyDB.pc
	$(PROC) iname=$(OBJ_D)/senderMMSProcKeyDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderMMSProcKeyDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderMMSProcKeyDB.cpp

$(OBJ_D)/senderMMSProTalk.o: $(SRC_D)/senderMMSProcessDBTalk.cpp
	$(RM) -rf $(OBJ_D)/senderMMSProTalk.*
	$(COPY) $(SRC_D)/senderMMSProcessDBTalk.cpp $(OBJ_D)/senderMMSProcessDBTalk.pc
	$(PROC) iname=$(OBJ_D)/senderMMSProcessDBTalk.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderMMSProTalk.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderMMSProcessDBTalk.cpp

$(OBJ_D)/reportMMSProcessDB.o: $(SRC_D)/reportMMSProcessDB.cpp
	$(RM) -rf $(OBJ_D)/reportMMSProcessDB.*
	$(COPY) $(SRC_D)/reportMMSProcessDB.cpp $(OBJ_D)/reportMMSProcessDB.pc
	$(PROC) iname=$(OBJ_D)/reportMMSProcessDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/reportMMSProcessDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportMMSProcessDB.cpp

$(OBJ_D)/senderDB.o: $(SRC_D)/senderDB.cpp
	$(RM) -rf $(OBJ_D)/senderDB.*
	$(COPY) $(SRC_D)/senderDB.cpp $(OBJ_D)/senderDB.pc
	$(PROC) iname=$(OBJ_D)/senderDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderDB.cpp


$(OBJ_D)/senderMMSDB.o: $(SRC_D)/senderMMSDB.cpp
	$(RM) -rf $(OBJ_D)/senderMMSDB.*
	$(COPY) $(SRC_D)/senderMMSDB.cpp $(OBJ_D)/senderMMSDB.pc
	$(PROC) iname=$(OBJ_D)/senderMMSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderMMSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderMMSDB.cpp

$(OBJ_D)/senderMMSDB_PART.o: $(SRC_D)/senderMMSDB_PART.cpp
	$(RM) -rf $(OBJ_D)/senderMMSDB_PART.*
	$(COPY) $(SRC_D)/senderMMSDB_PART.cpp $(OBJ_D)/senderMMSDB_PART.pc
	$(PROC) iname=$(OBJ_D)/senderMMSDB_PART.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderMMSDB_PART.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderMMSDB_PART.cpp

$(OBJ_D)/reportDB.o: $(SRC_D)/reportDB.cpp
	$(RM) -rf $(OBJ_D)/reportDB.*
	$(COPY) $(SRC_D)/reportDB.cpp $(OBJ_D)/reportDB.pc
	$(PROC) iname=$(OBJ_D)/reportDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/reportDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportDB.cpp


$(OBJ_D)/reportMMSDB.o: $(SRC_D)/reportMMSDB.cpp
	$(RM) -rf $(OBJ_D)/reportMMSDB.*
	$(COPY) $(SRC_D)/reportMMSDB.cpp $(OBJ_D)/reportMMSDB.pc
	$(PROC) iname=$(OBJ_D)/reportMMSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/reportMMSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportMMSDB.cpp

$(OBJ_D)/bcFileReceiver.o: $(SRC_D)/bcFileReceiver.cpp
	$(RM) -rf $(OBJ_D)/bcFileReceiver.*
	$(COPY) $(SRC_D)/bcFileReceiver.cpp $(OBJ_D)/bcFileReceiver.pc
	$(PROC) iname=$(OBJ_D)/bcFileReceiver.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/bcFileReceiver.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/bcFileReceiver.cpp

$(OBJ_D)/bcFileReceiver_TEST.o: $(SRC_D)/bcFileReceiver_TEST.cpp
	$(RM) -rf $(OBJ_D)/bcFileReceiver_TEST.*
	$(COPY) $(SRC_D)/bcFileReceiver_TEST.cpp $(OBJ_D)/bcFileReceiver_TEST.pc
	$(PROC) iname=$(OBJ_D)/bcFileReceiver_TEST.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/bcFileReceiver_TEST.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/bcFileReceiver_TEST.cpp

$(OBJ_D)/bcFilterReceiver.o: $(SRC_D)/bcFilterReceiver.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

#$(OBJ_D)/bcFileReceiver.o: $(SRC_D)/bcFileReceiver.cpp
#	$(RM) -rf $@
#	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

#$(OBJ_D)/reportDB.o: $(SRC_D)/reportDB.cpp
#	$(RM) -rf $@
#	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/logonSession.o: $(SRC_D)/logonSession.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/logonSessionMMSB.o: $(SRC_D)/logonSessionMMSB.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/logonSessionMMSB_PART.o: $(SRC_D)/logonSessionMMSB_PART.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/logonSessionMMSBS.o: $(SRC_D)/logonSessionMMSBS.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitorProcess.o: $(SRC_D)/monitorProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/adminProcess.o: $(SRC_D)/adminProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

#$(OBJ_D)/senderMMSProcess.o: $(SRC_D)/senderMMSProcess.cpp
#	$(RM) -rf $@
#	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/senderProcess.o: $(SRC_D)/senderProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/senderKNProcess.o: $(SRC_D)/senderKNProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^



$(OBJ_D)/senderProcess_v3.o: $(SRC_D)/senderProcess.cpp
	$(RM) -rf $@
	$(CC)  -DVER3 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/senderProcess_v4.o: $(SRC_D)/senderProcess.cpp
	$(RM) -rf $@
	$(CC)  -DVER4 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/senderProcess_v5.o: $(SRC_D)/senderProcess.cpp
	$(RM) -rf $@
	$(CC)  -DVER5 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/senderProcess_v6.o: $(SRC_D)/senderProcess.cpp
	$(RM) -rf $@
	$(CC)  -DVER6 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/senderProcess_v8.o: $(SRC_D)/senderProcess.cpp
	$(RM) -rf $@
	$(CC)  -DVER8 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/admin.o: $(SRC_D)/admin.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/mmsPacketUtil.o: $(LIB_D)/mmsPacketUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsPacketSend.o: $(LIB_D)/mmsPacketSend.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsFileProcess.o: $(LIB_D)/mmsFileProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsFileProcessBS.o: $(LIB_D)/mmsFileProcessBS.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsPacketBase.o: $(LIB_D)/mmsPacketBase.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/adminUtil.o: $(LIB_D)/adminUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitor.o: $(LIB_D)/monitor.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/reportMMSProcess.o: $(SRC_D)/reportMMSProcess.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/reportProcess.o: $(SRC_D)/reportProcess.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/reportKNProcess.o: $(SRC_D)/reportKNProcess.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/reportProcess_v3.o: $(SRC_D)/reportProcess.cpp
	$(RM) -rf $@
	$(CC) -DVER3 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/reportProcess_v4.o: $(SRC_D)/reportProcess.cpp
	$(RM) -rf $@
	$(CC) -DVER4 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/reportProcess_v5.o: $(SRC_D)/reportProcess.cpp
	$(RM) -rf $@
	$(CC) -DVER5 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/reportProcess_v6.o: $(SRC_D)/reportProcess.cpp
	$(RM) -rf $@
	$(CC) -DVER6 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/reportProcess_v8.o: $(SRC_D)/reportProcess.cpp
	$(RM) -rf $@
	$(CC) -DVER8 -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/cust_lib_common.o: $(LIB_D)/cust_lib_common.c
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -c $^

$(OBJ_D)/logonUtil.o: $(LIB_D)/logonUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/dbUtil.o: $(LIB_D)/dbUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/packetUtil.o: $(LIB_D)/packetUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/checkCallback.o: $(LIB_D)/checkCallback.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/logonSession* $(BIN_D)/logonDB $(BIN_D)/admin $(BIN_D)/senderMMS* $(BIN_D)/monitorProcess $(BIN_D)/adminProcess $(BIN_D)/reportMMS* $(BIN_D)/bcFileReceiver $(INST_D)/
