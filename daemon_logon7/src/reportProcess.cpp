#include "reportProcess.h"

int main(int argc,char* argv[])
{
    /*
     * 1 : sockfd
     * 2 : pipe
     * 3 : version 
     * 4 : conf file
     */
    int sockfd;
    int fd;
    int ret;
    char buff[SOCKET_BUFF];
    CLogonDbInfo logonDbInfo;

    sockfd = atoi(argv[1]);
    fd = atoi(argv[2]);
    memset(&logonDbInfo,0x00,sizeof(logonDbInfo));
    read(fd,(char*)&logonDbInfo,sizeof(logonDbInfo));
    close(fd);

    CCL(_DATALOG);
    CCL(_MONILOG);
    char* p;
    sprintf(_DATALOG,"%s/",logonDbInfo.szLogPath);
    sprintf(_MONILOG,"%s/",logonDbInfo.szLogPath);



    CCL(szReportID);
    strcpy(szReportID,logonDbInfo.szCID);

    p = strtok(logonDbInfo.szLogFilePath,"|");
    if( p )
        strcat(_MONILOG,p);
    else {
        logPrintR(0,"logonDbInfo.szLogFilePath[%s]",
                logonDbInfo.szLogFilePath);
        return -1;
    }

    p = strtok(NULL,"|");
    if( p )
        strcat(_DATALOG,p);
    else {
        logPrintR(0,"logonDbInfo.szLogFilePath[%s]",
                logonDbInfo.szLogFilePath);
        return -1;
    }

    logPrintR(0,"logFile[%s]monFile[%s]",
            _DATALOG,
            _MONILOG);

    char szReportDBIP[16];
    char szReportDBName[64];

    strcpy(szReportDBName,logonDbInfo.szReportDBName);

     p = strtok(szReportDBName,":");
    if( p )
        strcpy(szReportDBIP,p);
    else 
    {
        logPrintR(0,"logonDbInfo.szReportDBName[%s]",
                logonDbInfo.szReportDBName);
        return -1;
    }

    sprintf(reportDbDomainName,"%s/",logonDbInfo.szDomainPath);

     p = strtok(NULL,":");
    if( p )
        strcat(reportDbDomainName,p);
    else 
    {
        logPrintR(0,"logonDbInfo.szReportDBName[%s]",
                logonDbInfo.szReportDBName);
        return -1;
    }

    logPrintR(0,"szReportDBName[%s]",
                reportDbDomainName);


    ret = configParse(argv[4]);
    if( ret != 0 )
    {
        exit(1);
    }

    logPrintR(0,"[%s]",
            gConf.logonDBName);



    reportProcess(sockfd,logonDbInfo);

    return 0;
}


void reportProcess(int sockfd,CLogonDbInfo& logonDbInfo)
{
    int ret;
    CLogonUtil util;
    CAdminUtil admin;
    CKSSocket db;
    char szAppName[16];
    CProcessInfo processInfo;
    CMonitor monitor;

    memset(&processInfo,0x00,sizeof(processInfo));

    CCL(szAppName);
//    sprintf(szAppName,"%d_%d",logonDbInfo.nmPID,logonDbInfo.nmJOB);
    strcpy(szAppName,logonDbInfo.szAPPName);

     strcpy(processInfo.processName,logonDbInfo.szReportName);
    get_timestring("%04d%02d%02d%2d%2d%2d", time(NULL), processInfo.startTime);
    sprintf(processInfo.szPid,"%d",getpid());
    strcpy(processInfo.logonDBName,gConf.logonDBName);
 
    logPrintR(0,"START sockfd[%d][%s]currentTime[%s]pid[%s]",
            sockfd,
            logonDbInfo.szCID,
            processInfo.startTime,
            processInfo.szPid);

    util.displayLogonDbInfo(logonDbInfo,_MONILOG);

    CKSSocket hRemoteSock;
    char buff[SOCKET_BUFF];
    int recvLen;
    hRemoteSock.attach(sockfd);

    
    ret = admin.createDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
    if( ret != 0 )
    {
        logPrintR(0,"DOMAIN Create Error [%s][%c]path[%s]",
                logonDbInfo.szCID,
                logonDbInfo.classify,
                gConf.domainPath);
        bRActive = false;
    }


    monitor.Init("logon7",
            "report",
            processInfo.processName,
            logonDbInfo.szCID,
            logonDbInfo.nmPID,
            logonDbInfo.szIP);

    time(&RLastTLink);
    while(bRActive)
    {
        wait_a_moment(logonDbInfo.nRptWait); 
        // check admin packet
        ret = admin.checkPacket(processInfo,logonDbInfo,sum); 
        if( ret < 0 )
        {
            logPrintR(0,"DOMAIN check Error [%s][%s]",logonDbInfo.szCID,admin.getErrMsg());
            bRActive = false;
            continue;
        }

        switch(ret) {
            case 3: // end
                bRActive = false;
                continue;
            default:
                break;
        }


        time(&RThisT);
        ret = (int)difftime(RThisT,RLastTLink);
        if( ret > gConf.socketLinkTime)
        {
            ret = sendLink(hRemoteSock,util);
            if( ret < 0 )
            {
                bRActive = false;
                continue;
            }

            get_timestring("%04d%02d%02d%2d%2d%2d", 
                    time(NULL), 
                    processInfo.linkTime);
            monitor.setLinkTime();
            logPrintR(0,"report Link Send");

        }


         ret = (int)difftime(RThisT,monLastT);
        if( ret > 30 )
        {
            monitor.setDataSum(sum);
            monitor.setCurDate();
            monitor.send(gConf.monitorName);
            time(&monLastT);
            sum=0;
        }





        CCL(buff);
        ret = util.recvPacket(hRemoteSock,buff,0,10000);
        if( ret < 0) {

            logPrintR(0,util.getErrorMsg());
            
            bRActive = false;
            continue;
        }

        if( ret > 0 )
        {
            ret = classifyR(hRemoteSock,buff);
            if( ret < 0 ) {
                bRActive = false;
                continue;
            }
        }

        // get Report and send Rpt
        ret = getReprot(monitor,db,hRemoteSock,util,szAppName,logonDbInfo.nRptNoDataSleep);
        if( ret < 0 )
        {
            bRActive = false;
            continue;
        }




    }

    logPrintR(0,"END sockfd[%d][%s]",hRemoteSock.getSockfd(),logonDbInfo.szCID);
    hRemoteSock.close();
/*
    ret = admin.deleteDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
    if( ret != 0 )
    {
        logPrintR(0,"DOMAIN delete Error [%s][%c]path[%s]",
                logonDbInfo.szCID,
                logonDbInfo.classify,
                gConf.domainPath);
    }

*/

    return ;
}



int classifyR(CKSSocket& hRemoteSock,char* buff)
{
    int ret=0;
    TypeHeader* pHeader = (TypeHeader*)buff;
    int nType = 0;
    nType = str2int(pHeader->msgType,sizeof(pHeader->msgType));


    switch(nType) {
        default:
            logPrintR(0,"not define type [%d]",nType);
            viewPackReport(buff,str2int(pHeader->msgLeng,sizeof(pHeader->msgLeng)));
            ret = -1;
            break;
    }

    return ret;
}

int sendLink(CKSSocket& hRemoteSock, CLogonUtil& util)
{
    int ret;
    TypeMsgDataAck link;
    memset(&link,0x00,sizeof(link));
    char buff[SOCKET_BUFF];

    strcpy(link.header.msgType,"7");
    strcpy(link.header.msgLeng,"18");
    memcpy(link.szResult,"00",2);

    ret = hRemoteSock.send((char*)&link,sizeof(link));
    if( ret != sizeof(link))
    {
        logPrintR(0,"Link send Error[%s][%d/%d]",
                strerror(errno),
                ret,
                sizeof(link));
        return -1;
    }


    CCL(buff);
    ret = util.recvPacket(hRemoteSock,buff,5,10000);
    if( ret < 0) {

            logPrintR(0,util.getErrorMsg());
        return -1;
    }
    if( ret == 0 )
    {
        logPrintR(0,"5 sec 간 Link Ack 가 없습니다.");
        return -1;
    }

    TypeHeader* pHead = (TypeHeader*)buff;
    int nType = str2int(pHead->msgType,sizeof(pHead->msgType));
    if( nType != 8 )
    {
        logPrintR(0,"link ack type Error");
        viewPackReport(buff,str2int(pHead->msgLeng,sizeof(pHead->msgLeng)));
        return -1;
    }

    time(&RLastTLink);

    return 0;
}

void logPrintR(int type, const char *format, ...)
{
    va_list args;
    char logMsg[SOCKET_BUFF];
    char tmpMsg[SOCKET_BUFF];

    va_start(args, format);
    vsprintf(tmpMsg, format, args);
    va_end(args);

    sprintf(logMsg,"[R][%s] %s",
            szReportID,
            tmpMsg);

	if (type==1) _logPrint(_DATALOG,logMsg);
        else _monPrint(_MONILOG,logMsg);


}


int getReprot(CMonitor& monitor,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        CLogonUtil& util,
        char* szAppName,
        int nRptNoDataSleep)
{
    int ret;
    char dbBuff[SOCKET_BUFF];
    static bool sleepReportFlag = false;
    static time_t LastT;
    int nMsgId;
    CReportDbInfo rptData;





    if( sleepReportFlag )
    {
        time_t ThisT;
        time(&ThisT);

        if( difftime(ThisT,LastT) > nRptNoDataSleep )
            sleepReportFlag = false;

        return 0;
    }


    memset((char*)&rptData,0x00,sizeof(CReportDbInfo));
    strcpy(rptData.header.msgType,"1");
    strcpy(rptData.szResvData,szAppName);

    ret = db.connectDomain(reportDbDomainName);
    if( ret != 0 )
    {
        logPrintR(0,"reportDB conn Error",strerror(ret));
        sleepReportFlag = true;
        time(&LastT);
        db.close();
        return 0;
    }

    ret = db.send((char*)&rptData,sizeof(CReportDbInfo));
    if( ret != sizeof(CReportDbInfo))
    {
        logPrintR(0,"reportDB send Error ret[%d/%d][%s]",
                ret,
                sizeof(CReportDbInfo),
                strerror(errno));
        sleepReportFlag = true;
        time(&LastT);
        db.close();
        return 0;
    }

    ret = db.select(5,0);
    if( ret == 0 )
    {
        logPrintR(0,"reportDB recv timeout Error 5 sec");
        sleepReportFlag = true;
        time(&LastT);
        db.close();
        return 0;
    }

    if( ret < 0 )
    {
        logPrintR(0,"reportDB select Error [%s]",strerror(errno));
        sleepReportFlag = true;
        time(&LastT);
        db.close();
        return 0;
    }

    CCL(dbBuff);
    ret = db.rcvmsg(dbBuff);
    if( ret == 0 || ret < 0 )
    {
        logPrintR(0,"reportDB rcvmsg Error [%s]ret[%d]",strerror(errno),ret);
        sleepReportFlag = true;
        time(&LastT);
        db.close();
        return 0;
    }
    db.close();

    memcpy((char*)&rptData,dbBuff,ret);
    /*
    viewPackReport(dbBuff,ret);
    */

    CReportDbInfo* pRpt = (CReportDbInfo*)dbBuff;
    if( memcmp(pRpt->szResCode,"99",2) == 0 )
    { /* no report */
/* #define NOREPORTSLEEPTIME  */
        sleepReportFlag = true;
        time(&LastT);
        db.close();
        return 0;
    }

#if (defined(VER3) || defined(VER4) || defined(VER5) || defined(VER6) )
    TypeMsgDataRpt_v3 rpt;
#else
    /* ver 5_2 & 8 */
    TypeMsgDataRpt rpt;
#endif
    memset(&rpt,0x00,sizeof(rpt));
    strcpy(rpt.header.msgType,"5");

#if (defined(VER3) || defined(VER4) || defined(VER5) || defined(VER6) )
    strcpy(rpt.header.msgLeng,"26");
#else
    /* ver 5_2 & 8 */
    strcpy(rpt.header.msgLeng,"42");
#endif

    memcpy(rpt.szSerial,pRpt->szPtnsn ,sizeof(rpt.szSerial));
    memcpy(rpt.szTelInfo,pRpt->szEndTelco , sizeof(rpt.szTelInfo));

#if (defined(VER3) || defined(VER4) || defined(VER5) || defined(VER6) )
/* 없음 */
#else
    /* ver 5_2 & 8 */
    memcpy(rpt.szRptDate,pRpt->szRptDate , sizeof(rpt.szRptDate));
#endif

    memcpy(rpt.szResult,pRpt->szResCode , sizeof(rpt.szResult));


    ret = hRemoteSock.send((char*)&rpt,sizeof(rpt));
    if( ret != sizeof(rpt) )
    {

        logPrintR(0,"report send Error [%d/%d][%s]",
                ret,sizeof(rpt),
                strerror(errno));
        /* report 재전송 */
        retryReport(db,rptData);
        return -1;
    }

    nMsgId = pRpt->nMsgId;


#if (defined(VER3) || defined(VER4) || defined(VER5) || defined(VER6) )
    logPrintR(1,"[RPT][%d][%.16s][%.2s][%.3s]",
            nMsgId, 
            pRpt->szPtnsn,
            pRpt->szResCode,
            pRpt->szEndTelco);
#else
    /* ver 5_2 & 8 */
    logPrintR(1,"[RPT][%d][%.16s][%.2s][%.14s][%.3s]",
            nMsgId, 
            pRpt->szPtnsn,
            pRpt->szResCode,
            pRpt->szRptDate,
            pRpt->szEndTelco);
#endif




    CCL(dbBuff);
    ret = util.recvPacket(hRemoteSock,dbBuff,60,0);
    if( ret < 0 || ret == 0 )
    {

        logPrintR(0,util.getErrorMsg());
        logPrintR(0,"RAK Error [%.16s][%s]",
                rptData.szPtnsn,
                strerror(errno));

        /* report 재전송 */
        retryReport(db,rptData);
        return -1;
    }

    TypeMsgDataAck* pAck = (TypeMsgDataAck*)dbBuff;

    logPrintR(1,"[RAK][%d][%.16s][%.2s]",
            nMsgId,
            rpt.szSerial,
            pAck->szResult);
    sum++;
    monitor.setDataTime();

    return 0;
}


int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
    if( gConf.logonDBName == NULL ) strcpy(gConf.logonDBName,"");

    conf.strncpy2(gConf.monitorName , conf.FetchEntry("domain.monitor"),64);
    if( gConf.monitorName == NULL ) strcpy(gConf.monitorName,"");

    conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.self"),64);
    if( gConf.domainPath == NULL ) strcpy(gConf.domainPath,"");


    gConf.socketLinkTime = conf.FetchEntryInt("socket.linktime");
    if( gConf.socketLinkTime <= 0 )
        gConf.socketLinkTime = 1;

    return 0;
}


void viewPackReport(char *a,int n)
{
    int i;
    char logMsg[VIEWPACK_MAX_SIZE];
    char strtmp[VIEWPACK_MAX_SIZE];

    memset(logMsg,0x00, sizeof logMsg);
    memset(strtmp,0x00, sizeof strtmp);
    for(i=0;i<n;i++)
    {
        if( a[i] == 0x00 )
            strtmp[i] = '.';
        else
            memcpy(strtmp+i,a+i,1);
    }

    sprintf(logMsg,"info:[%s]",strtmp);
    _monPrint(_MONILOG,logMsg);
    return ;
}



int retryReport(CKSSocket& db, CReportDbInfo& rptData)
{

    int ret;


    ret = db.connectDomain(reportDbDomainName);
    if( ret != 0 )
    {
        logPrintR(0,"reportDB conn Error",strerror(ret));
        db.close();
        return -1;
    }


    strcpy(rptData.header.msgType,"3");
    ret = db.send((char*)&rptData,sizeof(CReportDbInfo));
    if( ret != sizeof(CReportDbInfo))
    {
        logPrintR(0,"retryReport func Error ret[%d/%d] [%s]",
                ret,
                sizeof(CReportDbInfo),
                strerror(errno));
        return -1;
    }

    ret = db.select(5,0);
    if( ret == 0 )
    {
        logPrintR(0,"retryReport func timeout Error 5 sec");
        return -1;
    }

    if( ret < 0 )
    {
        logPrintR(0,"retryReport func recv Error[%s]",strerror(errno));
        return -1;
    }

    ret = db.rcvmsg((char*)&rptData);
    if( ret == 0 || ret < 0 )
    {
        logPrintR(0,"retryReport rcvmsg Error [%s]ret[%d]",strerror(errno),ret);
        return -1;
    }

    return 0;
}



