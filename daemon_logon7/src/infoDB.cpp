#include "infoDB.h"


void logfunc(const char *s) {
    log_history(0,0,"[%s]",s);
}


int main(int argc, char* argv[])
{
    int ret;
    char logMsg[SOCKET_BUFF];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;
    char buff[SOCKET_BUFF];
    int hNewSocket;
    TypeMsgBindSnd* pLogonData;

    ORAPP::log_to(logfunc);
    ORAPP::Connection db;



    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }


    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        printf("err\n");
        ml_sub_end();
        exit(1);
    }

    printf("[%s][%s]\n",
            PROCESS_NAME,
            gConf.logonDBName);



    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server();

    // ret = svrSockfd.createDomainNon(DOMAIN_LOGONDB);
    ret = svrSockfd.createDomainNon(gConf.logonDBName);
    if( ret !=0 )
    {
        log_history(0,0,"DOMAIN_LOGONDB 생성실패",strerror(errno));
        goto END;
    }

    if (!orapp_connect(db, "newsms_192", "smstest", "smstest"))
        goto END;


    while(activeProcess)
    {
        wait_a_moment(10000);
        hNewSocket = svrSockfd.accept();
        if( hNewSocket <= 0 )
            continue;

        // new connection 
        newSockfd.attach(hNewSocket);
        memset(buff,0x00,sizeof(buff));
        ret = newSockfd.rcvmsg(buff);
        if( ret == 0 )
        {
            newSockfd.close();
            log_history(0,0,"connection 후 3초간 데이터가 없어 연결을 close 합니다");
            continue;
        }

        if( ret < 0 )
        {
            newSockfd.close();
            log_history(0,0,"recv Error");
            continue;
        }


/* admin 통신 */
        viewPack(buff,ret);
        if( memcmp(buff,"getInfo",7) == 0 )
        {
            newSockfd.send("1",2);
            newSockfd.close();
            continue;
        }
/* admin 통신 */


        classifyProtocol(db,newSockfd,ret,buff);



        newSockfd.close();

    }

END:
    svrSockfd.close();
    orapp_disconnect(db);
    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}

bool orapp_connect(ORAPP::Connection &db, const char *tns, const char *user, const char *pass) {

    if (!db.connect(tns, user, pass)) {
        log_history(0,0,"   >>> (failed to connect) %s", db.error().c_str());
        return false;
    }

    log_history(0,0,"*** connected to: %s", db.version().c_str());

    return true;
}

bool orapp_disconnect(ORAPP::Connection &db) {
    log_history(0,0,"*** disconnecting...");

    if (!db.disconnect()) {
        log_history(0,0,"   >>> (failed to disconnect)");
        return false;
    }

    return true;
}

int checkLogon(ORAPP::Connection &db,char* buff) {

    ORAPP::Query *q = db.query();
    CLogonDbInfo logonDbInfo;

    char szSIP[100+1]; /* 고객사의 서버 IP list */
    char szAPPName[16]; /* report 를 위한 appname */
    char szErrMsg[512]; /* procedure 결과 string */
    int nmPID; /* 고객사 pid */
    int nmJOB;  /* 고객사 jobcode */
    int nmPRT; /* 고객사 우선순위 */
    int nmCNT;  /* sender Sleep : 한건전송후 쉬는 시간  */
    int nmRST; /* succ : 0 fail: 음수 -2 : 없는 데이터 -1 : 기타 */
    int nUrlJob; /* url 사용시 jobcode : 사용하지 않을수 -99 */
    int nRptWait; /* report Sleep : 한건전송후 쉬는 시간 */

    char szServerInfo[128]; /* xxx.xxx.xxx.xxx:xxxxx => 21 + 1(|:구분자) = 22 * 5 = 110 대략 128 */
    int nRptNoDataSleep; /* default 3 sec */
    char szSenderName[64]; /* home directory 부터 상대 패스 */
    char szReportName[64]; /* home directory 부터 상대 패스 */
    char szSenderDBName[64]; /* home directory 부터 상대 패스 localhost or ip:port */
    char szReportDBName[64]; /* home directory 부터 상대 패스 localhost or ip:port */
    char szLogFilePath[128]; /* 전체 경로 */
    char szReserve[128]; /* 추가 예약 필드 etc */

    TypeMsgBindSnd* pSend = (TypeMsgBindSnd*)buff;

    memset(&logonDbInfo,0x00,sizeof(logonDbInfo)); 
    memcpy(logonDbInfo.szCID,pSend->szCID,sizeof(pSend->szCID));
    memcpy(logonDbInfo.szPWD,pSend->szPWD,sizeof(pSend->szPWD));

    trim(logonDbInfo.szCID,strlen(logonDbInfo.szCID));
    trim(logonDbInfo.szPWD,strlen(logonDbInfo.szPWD));

    CCL(szSIP);
    CCL(szAPPName);
    CCL(szErrMsg);

    CCL(szServerInfo);
    CCL(szSenderName);
    CCL(szReportName);
    CCL(szSenderDBName);
    CCL(szReportDBName);
    CCL(szLogFilePath);
    CCL(szReserve);
    nRptNoDataSleep = 3;
    nmRST = -1;

    *q << "BEGIN "
        << "proc_check_login_ext_v2("
        << "in_cid=>'" << logonDbInfo.szCID << "',"
        << "in_pwd=>'" << logonDbInfo.szPWD << "',"
        << "ot_appname=>:szAPPName,"
        << "ot_sip=>:szSIP,"
        << "ot_pid=>:nmPID,"
        << "ot_job=>:nmJOB,"                /* 업체 업무코드*/
        << "ot_c_job=>:nUrlJob,"            /* callback jobcode */
        << "ot_prt=>:nmPRT,"                /* 전문 우선순위*/
        << "ot_cnt=>:nmCNT,"                /* 초당 전송건수*/
        << "ot_rpt_cnt=>:nRptWait,"         /* 연속 rpt 전송시 sleep 시간 */
        << "ot_server_info=>:szServerInfo,"
        << "ot_rpt_sleep_cnt=>:nRptNoDataSleep,"
        << "ot_sender_proc=>:szSenderName,"
        << "ot_report_proc=>:szReportName,"
        << "ot_senderdb_info=>:szSenderDBName,"
        << "ot_reportdb_info=>:szReportDBName,"
        << "ot_logfile_info=>:szLogFilePath,"
        << "ot_etc=>:szReserve,"
        << "ot_rst=>:nmRST,"                /* 실행 결과    */
        << "ot_rstmsg=>:szErrMsg);"         /* sql err msg */
        << "END;";
    
    q->bind(":szAPPName", szAPPName,sizeof(szAPPName));
    q->bind(":szSIP", szSIP,sizeof(szSIP));
    q->bind(":nmPID", nmPID);
    q->bind(":nmJOB", nmJOB);
    q->bind(":nUrlJob", nUrlJob);
    q->bind(":nmPRT", nmPRT);
    q->bind(":nmCNT", nmCNT);
    q->bind(":nRptWait", nRptWait);
    q->bind(":szServerInfo",szServerInfo,sizeof(szServerInfo));
    q->bind(":nRptNoDataSleep",nRptNoDataSleep);
    q->bind(":szSenderName",szSenderName,sizeof(szSenderName));
    q->bind(":szReportName",szReportName,sizeof(szReportName));
    q->bind(":szSenderDBName",szSenderDBName,sizeof(szSenderDBName));
    q->bind(":szReportDBName",szReportDBName,sizeof(szReportDBName));
    q->bind(":szLogFilePath",szLogFilePath,sizeof(szLogFilePath));
    q->bind(":szReserve",szReserve,sizeof(szReserve));
    q->bind(":nmRST", nmRST);
    q->bind(":szErrMsg", szErrMsg,sizeof(szErrMsg));

    if (!q->execute()) {
        log_history(0,0,"   >>> (select failed) %s\n", db.error().c_str());
        return -1;
    }

    if( nmRST != 0 )
    {
        log_history(0,0,"proc_check_logon_ext_v2: nmRST[%d][%s]",
                nmRST,szErrMsg);
        return nmRST;
    }

    memcpy(logonDbInfo.szSIP,trim(szSIP,strlen(szSIP)),sizeof(logonDbInfo.szSIP));
    memcpy(logonDbInfo.szAPPName,trim(szAPPName,strlen(szAPPName)),sizeof(logonDbInfo.szAPPName) );
    memcpy(logonDbInfo.szServerInfo,trim(szServerInfo,strlen(szServerInfo)),sizeof(logonDbInfo.szServerInfo));
    logonDbInfo.nRptNoDataSleep = nRptNoDataSleep;
    memcpy(logonDbInfo.szSenderName,trim(szSenderName,strlen(szSenderName)),sizeof(logonDbInfo.szSenderName));
    memcpy(logonDbInfo.szReportName,trim(szReportName,strlen(szReportName)),sizeof(logonDbInfo.szReportName));
    memcpy(logonDbInfo.szSenderDBName,trim(szSenderDBName,strlen(szSenderDBName)),sizeof(logonDbInfo.szSenderDBName));
    memcpy(logonDbInfo.szReportDBName,trim(szReportDBName,strlen(szReportDBName)),sizeof(logonDbInfo.szReportDBName));
    memcpy(logonDbInfo.szLogFilePath,trim(szLogFilePath,strlen(szLogFilePath)),sizeof(logonDbInfo.szLogFilePath));
    memcpy(logonDbInfo.szReserve,trim(szReserve,strlen(szReserve)),sizeof(logonDbInfo.szReserve));

    logonDbInfo.nmPID = nmPID;
    logonDbInfo.nmJOB = nmJOB;
    logonDbInfo.nmPRT = nmPRT;
    logonDbInfo.nmCNT = nmCNT;
    logonDbInfo.nmRST = nmRST;
    logonDbInfo.nUrlJob = nUrlJob;
    logonDbInfo.nRptWait = nRptWait;
    logonDbInfo.classify = pSend->classify;

    memset(buff,0x00,SOCKET_BUFF);
    memcpy(buff,&logonDbInfo,sizeof(logonDbInfo));

    return 0;
}


int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.infoDBName , conf.FetchEntry("domain.infodb"),64);
    if( gConf.infoDBName == NULL ) strcpy(gConf.infoDBName,"");


    return 0;
}

int classifyProtocol(ORAPP::Connection &db,CKSSocket& newSockfd,int size,char* bindPacket)
{
    int ret;
    switch(size)
    {
        case sizeof(TypeMsgBindSnd):
            logonType5(db,newSockfd,size,bindPacket);
            break;
        case sizeof(TypeMsgBindSnd_v3):
            break;
        default:
            log_history(0,0,"classifyProtocol Error : not matching protocol");
            ret = -1;
            break;
    }

    return 0;
}


int logonType5(ORAPP::Connection &db,CKSSocket& newSockfd,int size,char* bindPacket)
{
    int ret=-1;
    TypeMsgBindAck ack;
    memset(&ack,0x00,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");
    ret = checkLogon(db,bindPacket);
    if( ret < 0 )
    {
        switch(ret){
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"34",2);
                break;
            default:
                memcpy(ack.szResult,"33",2);
                break;
        }
    } else {
        memcpy(ack.szResult,"00",2);
    }

    newSockfd.send((char*)&ack,sizeof(ack));
    if( ret < 0 )
        return 0; /* logon Fail */


    /* logonDbInfo send */
    char buff[SOCKET_BUFF];
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"logonDbInfo OK 대기중 session 에서 socket close:Error");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"logonDbInfo OK 대기중 session 에서 socket Error[%s]",
                strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"logonDbInfo OK 대기중 session 에서 socket packet not ok: Error[%.2s]",
                buff);
        return 0;

    }

    ret = newSockfd.send(bindPacket,sizeof(CLogonDbInfo));
    if( ret != sizeof(CLogonDbInfo) )
        log_history(0,0,"logonDbInfo ->session send socket Error[%s]",
                strerror(errno));


    return 0;
}







