/*!
 @file
 	bcFileReceiver.c
 @date
 	20??. ?. ?.
 <AUTHOR>
 @brief
 	BC카드 FTP서비스 연동 프로세스
 	파일수신 -> 'L501' : filter_load.sh 실행 파일 DB 등록
 	         -> 그외 전문: java모듈에서 실행
*/
#include "bcFileReceiver.h"
#define	OVERTIME 1000
#define	RECV_SELECT_TIME	100000

#include <queue>
#include <list>
#include <semaphore.h> 

#include	<time.h>
#include	<unistd.h>

EXEC SQL BEGIN DECLARE SECTION;
#define MAX_DB_CONNECTION 2
EXEC SQL END DECLARE SECTION;

#define CALL_WEB	"211.43.202.31"
#define PORT_WEB	80

sem_t m_sem;

queue<void*, list<void*> > dbConnQ;

typedef struct _THREAD_PARAM {
	int sockfd;
	struct in_addr inaddr;
	pthread_t tid;
} ThreadParam;

void Init_Oracle(sql_context ctx);

void*   doService(void* param);
int startProcess(char* szConfigFile);
char *matchString(char* szOrg, char* szTag, string& strVal);

int AlertRecv2Admin(char* pForm,...);

int main(int argc, char* argv[])
{
    int ret;
    int hOpenSocket;
    char logMsg[1024];
    int clisock = 0 ;
    socklen_t clilen=0;
    struct sockaddr_in cli_addr;
    struct timeval outtime;
    fd_set rset;
    char szIP[16];
    int nPort;
	ThreadParam* param;
	int hNewSocket = 0 ; /** new connection socket */

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }

    EXEC SQL BEGIN DECLARE SECTION;
    sql_context db[MAX_DB_CONNECTION];
    EXEC SQL END DECLARE SECTION;
    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        ml_sub_end();
        exit(1);
    }

    printf("[%s][%d][%d][%s]\n",
            gConf.serverIP,
            gConf.serverPORT,
            gConf.process_sleep_time,
            gConf.logonDBName);


    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server_Fork();

    EXEC SQL ENABLE THREADS;
    for(int i=0;i<MAX_DB_CONNECTION;i++)
    {
        EXEC SQL CONTEXT ALLOCATE :db[i];

        Init_Oracle(db[i]);
        if( sqlca.sqlcode !=0 )
        {
            monitoring("db connection fail error",0,errno);
            ml_sub_end();
            return -1;
        }

        log_history(0,0,"db[%x]CONNECT",db[i]);
        dbConnQ.push(db[i]);
    }
	
    CCL(szIP);

    strcpy(szIP,gConf.serverIP);
    nPort = gConf.serverPORT;

    /* socket open */
    ret=UnixSockOpenNon(&hOpenSocket,szIP,nPort);
    if ( ret!=0 ) {
        monitoring("SOCKET OPEN ERROR.",0,errno);
        ml_sub_end();
        if ( hOpenSocket ) close( hOpenSocket );
        return -1;
    }

    while(activeProcess)
    {
        wait_a_moment(gConf.process_sleep_time);
        outtime.tv_sec = 0;
        outtime.tv_usec = 50000;
        FD_ZERO(&rset);
        FD_SET(hOpenSocket,&rset);
        ret = select(hOpenSocket+1, (fd_set*)&rset, (fd_set*)NULL, (fd_set*)NULL, &outtime);
        if ( ret>0 ) {

			clilen = sizeof(cli_addr);
			hNewSocket = accept(hOpenSocket, (struct sockaddr*)&cli_addr, &clilen);
			if( hNewSocket < 0 )   {
				log_history(0,0,"Accept Client Socket Error[%s]",strerror(errno));
				return -1;
			}
			else if (hNewSocket > 0) {
				
				log_history(0,0,"connect [%s]",inet_ntoa(cli_addr.sin_addr));

				param = (ThreadParam*)malloc(sizeof(ThreadParam));
				memset( param, 0x00, sizeof(ThreadParam) );
				param->sockfd = hNewSocket;
				memcpy(&param->inaddr, &cli_addr.sin_addr, sizeof(struct in_addr));


				/* thread Create */
				if( pthread_create(&param->tid, NULL, doService, (void*)param) ) {
					log_history(0,0,"THREAD CREATE ERROR.[%d][%s]",0,errno);
					close(hNewSocket);
				}

			}

        }
 
    }
	
    EXEC SQL BEGIN DECLARE SECTION;
    sql_context pDB;
    EXEC SQL END DECLARE SECTION;


    sleep(2);
    while( dbConnQ.size()>0 )
    {

        pDB = dbConnQ.front();
        if( pDB == NULL ) break;

        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL COMMIT WORK RELEASE;
        EXEC SQL CONTEXT FREE :pDB;
        dbConnQ.pop();
    }
    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    if( hOpenSocket) close(hOpenSocket);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}

int readmsg(CKSSocket newSockfd,char* buff,int total_size, int wait_sec) {
	
	int ret = 0;
	int read_size = 0;
	int len = 0;

	if(wait_sec <= 0 ) wait_sec = 3;

	/* recv queue data */
    while(1) {
        len = newSockfd.recv(buff+read_size,total_size);
        if( len == 0 )
        {
			log_history(0,0,"recv ZERO[%s]total_size[%d]",strerror(errno), total_size);
			sleep(1);
            return -1;
        }
        if( len < 0 ) {
			log_history(0,0,"recv client close[%s]",strerror(errno));
            return len;
        }
		total_size -= len;
		read_size += len;
		if(total_size==0) break;
    }

	return read_size;

}

int classfyCMD(char* buff) {
	int cmd = 0;
	TypeCommon* pCommon;
	TypeMsg06000610* pMsg0600;
	char tmp[8];
	int ret=0;
	
	pCommon = (TypeCommon*)buff;
	memset(tmp,0x00,sizeof(tmp));
	memcpy(tmp,pCommon->codeJunmun,sizeof(pCommon->codeJunmun));

	
	ret = atoi(tmp);
	printf("pCommon->codeJunmun[%s][%d]\n", tmp,ret);
	if(ret == CMD_INIT) {
		pMsg0600 = (TypeMsg06000610*)buff;
		memset(tmp,0x00,sizeof(tmp));
		memcpy(tmp,pMsg0600->szInfoUpmu,sizeof(pMsg0600->szInfoUpmu));
		ret = atoi(tmp);
		printf("pMsg0600->szInfoUpmu[%s][%d]\n", tmp,ret);
		if (ret == 1 ) ret = CMD_START;
		else if (ret == 3 ) ret = CMD_FILEDATA_END;
		else if (ret == 4 ) ret = CMD_END;
		else ret = 999;
	}
	return ret;
}

int readPacket(CKSSocket newSockfd,char* buff, int wait_sec) {
	int retSel = 0;
	TypeCommon* pCommon;
	char codeJunmun[4+1];
	int ret = 0;
	char tmp[4+1];
	int total_size = 0;
	int remain_size = 0;
	int buff_position = 0;
	int isError = 0;
	int cmd = 0;

	if(wait_sec <= 0 ) wait_sec = 60;

	retSel = newSockfd.select(wait_sec,0);
	if(retSel>0) {
		// read header
		// -1 :             sprintf(szErrorMsg,"close by peer");
		// < -1 : strerror(errno));
		ret = readmsg(newSockfd,buff,N_SIZE_MSG_COMMON, 3);
		//log_history(0,0,"header[%s]",buff);
		if( ret == -1 )
		{
			log_history(0,0,"header recv client close");
			return -1;
		} else if( ret < -1 )
		{
			log_history(0,0,"hreader recv Error");
			return -2;
		} else if(ret != N_SIZE_MSG_COMMON ) {
			log_history(0,0,"hreader recv packet size error[%d]", ret);
			return -3;
		}
		log_history(0,0,"hreader recv: [%d][%.*s]",ret, N_SIZE_MSG_COMMON, buff);
	
	
		pCommon = (TypeCommon*)buff;
		memset(codeJunmun,0x00,sizeof(codeJunmun));
		memcpy(codeJunmun,pCommon->codeJunmun,sizeof(pCommon->codeJunmun));
		ret = atoi(codeJunmun);
	
		// 전문길이 설정
		memset(tmp,0x00,sizeof(tmp));
		memcpy(tmp,pCommon->totalLen,sizeof(pCommon->totalLen));
		total_size = atoi(tmp);
		// 추가 길이
		remain_size = total_size - N_SIZE_MSG_COMMON + 4;
		log_history(0,0,"body len: total_size[%d]+[%d]+4 = [%d]",total_size, N_SIZE_MSG_COMMON, remain_size);
	
		ret = readmsg(newSockfd,buff+N_SIZE_MSG_COMMON,remain_size, 3);
		//log_history(0,0,"body[%.300s]",buff);
		if( ret == -1 )
		{
			log_history(0,0,"body recv client close");
			return -1;
		} else if( ret < -1 )
		{
			log_history(0,0,"body recv Error");
			return -2;
		} else if(ret != remain_size ) {
			log_history(0,0,"body recv packet size error[%d]", ret);
			return -3;
		}
		
		printf("buff=[%s]\n", buff);
	} else {
		return -4;
	}
		
	return 0;

}


void* doService(void* param)
{
    int hNewSocket = 0 ; /** new connection socket */
    socklen_t clilen=0; /** new connection socket size */
    struct sockaddr_in cli_addr; /** new connection info struct */
    struct timeval outtime;
    fd_set rset;
	int ret;

	CKSSocket newSockfd;
	char header[N_SIZE_MSG_COMMON+1];
	char buff[4096];
	char tmp[8];
	char ack[100];

	TypeCommon* pCommon;
	TypeMsg06000610* pMsg06000610;

	char ErrBuf[512];
	char logMsg[4096];

	string strKey;

	FILE *      oFD;
	char        file[256], date[32], buf[256];

	int total_size = 0;
	time_t	LastT, ThisT;
	fd_set readfds;
	fd_set tempfds;

	ThreadParam* prop = (ThreadParam*)param;
	pthread_detach(pthread_self());

    pid_t childPid;
    char szKillCmd[32];
	int nReadData = 0;
	int bodySize = 0;
	int retSel = 0;
	int isFalse = 1;
	int cmd = 0;
	char szSubcmd[3+1];
	int subcmd = 0;
	char szFilename[8+1];


	CCL(logMsg);
	sprintf(logMsg,"THREAD_INIT:[%s][%d]",inet_ntoa(prop->inaddr),prop->sockfd);
	monitoring(logMsg,0,0);

	hNewSocket = prop->sockfd;
	newSockfd.attach(hNewSocket);

	// header read

	retSel = newSockfd.select(60,0);
	if(retSel>0) {

		ret = readPacket(newSockfd, buff, 3);
		if( ret == -1 )
		{
			log_history(0,0,"header recv client close[%d]",ret);
			isFalse = 0;
			goto SenderEND;
		} else if( ret == -2 )
		{
			log_history(0,0,"hreader recv Error[%d]",ret);
			isFalse = 0;
			goto SenderEND;
		} else if( ret == -3 )
		{	
			log_history(0,0,"packet size error[%d]",ret);
			isFalse = 0;
			goto SenderEND;
		} else if( ret == -4 )
		{	
			log_history(0,0,"readPacket timeout[%d]",ret);
			isFalse = 0;
			goto SenderEND;
		}

		cmd = classfyCMD(buff);
		log_history(0,0,"cmd[%d]buff[%s]",cmd,buff);
		if(cmd == CMD_START ) ret = reciveFile(newSockfd,buff);

	} else {
		log_history(0,0,"read timeout[%d]",retSel);
	}

SenderEND:
    CCL(logMsg);	
    sprintf(logMsg,"THREAD OUT:[%s]",inet_ntoa(prop->inaddr));
	monitoring(logMsg,0,0);
	newSockfd.close();
	free(prop);
	
	return 0;
}

int ackPacket(CKSSocket newSockfd,char* ret_buff,int cmd, int send_size, char* ack) {
	int ret;
	TypeCommon* pCommon;
	TypeMsg06000610* pMsg06000610;
	char tmp[8];

	pCommon = (TypeCommon*)ret_buff;

	CCL(tmp);
	sprintf(tmp,"%04d",send_size-4);
	memcpy(pCommon->totalLen,tmp,sizeof(pCommon->totalLen));
	
	pCommon->flagSend = 'E';


	if(cmd == CMD_START_ACK || cmd == CMD_FILEDATA_END_ACK || cmd ==CMD_END_ACK ) {
		memcpy(pCommon->codeJunmun,"0610",4);
		pMsg06000610 = (TypeMsg06000610*)ret_buff;
		sprintf(tmp,"%03d",cmd);
		memcpy(pMsg06000610->szInfoUpmu,tmp,3);
	} else {
		sprintf(tmp,"%04d",cmd);
		memcpy(pCommon->codeJunmun,tmp,4);
	}
	memcpy(pCommon->szAck,ack,strlen(ack));

	ret = newSockfd.send(ret_buff,send_size);
	if( ret == 0 )
	{
		log_history(0,0,"ack write client close");
		return -1;
	}
	if( ret < 0 )
	{
		log_history(0,0,"ack header write  Error");
		return -2;
	}
	log_history(0,0,"ack [%s]([%d]/[%d])",ret_buff,ret,send_size);

	return ret;

}

int reciveFile(CKSSocket newSockfd,char* buff_init) {
	int ret;
	char buff[4096];
	char ret_buff[4096];

EXEC SQL BEGIN DECLARE SECTION;
    char szSqlErrorMsg[512];
    int nSqlCode = -999;

	char szSendDate[10+1];
	char szInfoUpmu[3+1];
	char szSender[20+1];
	char szPassword[16+1];
	char szCommonFileName[8+1];
	char szSaveFileName[256];
	char szReqSendDate[14+1];
	char szReqUser[10+1];
	char szReqPWD[10+1];
	
	
	int nStep;
	long nSize;
	int nDataCnt;
	int nDataCntInFile;
	int nSeqno;

	int n_SEQ_TBL_BC_RESERV_F_GRP;
	int n_SEQ_TBL_BC_RESERV_F_GRP_SND;

EXEC SQL END DECLARE SECTION;

	int	ii = 0;
	int	f_cnt = 0;
	int	f_L501 = 0;
	char szWorkType[4+1];
	TypeMsgBL501 *BL501;
	
	char szFileName[256];
	char szFileNameSucc[256];

	char fileinfo[24+1];
	char tmp[32];
	
	char szFileData[gConf.file_datasize];

	void* pDB;

	long filesize;
	int packet_size;
	char buff_data[4096+1];
	int block_seq[10];
	int seq_size = 0;
	int block_size = 0;
	char szRetiredInfo[100+1];
	char szBlockNo[4+1];
	char szSequenceNo[3+1];
	int nSequenceNo;
	int nBlockNo;
	int real_size;
	int isFalse=0;
	int cmd = 0;
	int i=0;
	int recv_cnt = 0;
	int f_succ = 0;

		int readsize = gConf.file_datasize;
		int copy_start_position = 0;
		int copy_remain_size = 0;
		int copy_size = 0;


	char alertmsgbuf[512];
	char body_cnt[8+1];
	
	char* seqinfo;
	FILE *      oFD;
	char *filedata;
	
	if(readsize == 0 ) readsize = 2000;
	TypeCommon* pCommon;
	TypeMsg0620* pMsg0620;
	TypeMsg0300* pMsg0300;
	TypeMsg06300640* pMsg06300640;
	TypeMsg06000610* pMsg06000610;
	TypeMsg03100320* pMsg03100320;
	TypeMsg03100320DATA* pMsg03100320DATA;
	
	pMsg06000610 = (TypeMsg06000610*)buff_init;

	CCL(szSendDate);
	CCL(szInfoUpmu);
	CCL(szSender);
	CCL(szPassword);

	memcpy(szSendDate,pMsg06000610->szSendDate,sizeof(pMsg06000610->szSendDate));
	memcpy(szInfoUpmu,pMsg06000610->szInfoUpmu,sizeof(pMsg06000610->szInfoUpmu));
	memcpy(szSender,pMsg06000610->szSender,sizeof(pMsg06000610->szSender));
	memcpy(szPassword,pMsg06000610->szPassword,sizeof(pMsg06000610->szPassword));

	log_history(0,0,"szSendDate[%s]",szSendDate);
	log_history(0,0,"szInfoUpmu[%s]",szInfoUpmu);
	log_history(0,0,"szSender[%s]",szSender);
	log_history(0,0,"szPassword[%s]",szPassword);

	// ack 610001
	CCL(ret_buff);
	memcpy(ret_buff,buff_init,sizeof(TypeMsg06000610));
	
	CCL(tmp); strcpy(tmp,"000");	// set ack

	ret = ackPacket(newSockfd,ret_buff,CMD_START_ACK,SIZE_CMD_START_ACK,tmp);
	if(ret <0 ) {
		isFalse = 1;
		goto SenderEND;	
	}
	
	nStep = 0;
	// read 0630
	CCL(buff);
	ret = readPacket(newSockfd, buff, 60);
	if( ret <  0)
	{
		log_history(0,0,"header recv client close[%d]",ret);
		isFalse = 1;
		goto SenderEND;
	} else if( ret == -2 )
	{
		log_history(0,0,"hreader recv Error[%d]",ret);
		isFalse = 1;
		goto SenderEND;
	} else if( ret == -3 )
	{	
		log_history(0,0,"packet size error[%d]",ret);
		isFalse = 1;
		goto SenderEND;
	} else if( ret == -4 )
	{	
		log_history(0,0,"timeout [%d]",ret);
		isFalse = 1;
		goto SenderEND;
	}

	cmd = classfyCMD(buff);
	log_history(0,0,"cmd[%d]buff[%s]",cmd,buff);
	if(cmd != CMD_FILEINFO ) {
		isFalse = 1;
		goto SenderEND;
	}

	pMsg06300640 = (TypeMsg06300640*)buff;
	memset(fileinfo,0x00,sizeof(fileinfo));
	memcpy(fileinfo,pMsg06300640->szFileInfo,sizeof(pMsg06300640->szFileInfo));
	
	log_history(0,0,"fileinfo[%s]",fileinfo);

	memset(szCommonFileName,0x00,sizeof(szCommonFileName));
	memcpy(szCommonFileName,fileinfo,8);

	memset(szWorkType,0x00,sizeof(szWorkType));
	memcpy(szWorkType,fileinfo,4);
	log_history(0,0,"WorkType[%s]",szWorkType);
	
	//"L501" 전문용도
	if (memcmp(szWorkType, "L501", 4) == 0)
		f_L501 = 1;


	memset(tmp,0x00,sizeof(tmp));
	memcpy(tmp,fileinfo+8,12);
	filesize = atol(tmp);
	
	nSize = filesize;

	log_history(0,0,"filesize[%s][%d]",tmp,filesize);

	memset(block_seq,0,sizeof(block_seq));

	memset(tmp,0x00,sizeof(tmp));
	memcpy(tmp,fileinfo+20,4);
	packet_size = atoi(tmp);

	log_history(0,0,"packet_size[%s][%d]",tmp,packet_size);

	// ack 0640
	CCL(ret_buff);
	memcpy(ret_buff,buff,sizeof(TypeMsg06300640));
	pMsg06300640 = (TypeMsg06300640*)ret_buff;
	memset(fileinfo+9,'0',12);
	memcpy(pMsg06300640->szFileInfo,fileinfo,sizeof(pMsg06300640->szFileInfo));
	CCL(tmp); strcpy(tmp,"000");	// set ack
	ret = ackPacket(newSockfd,ret_buff,CMD_FILEINFO_ACK,SIZE_CMD_FILEINFO_ACK,tmp);
	if(ret <0 ) {
		isFalse = 1;
		goto SenderEND;	
	}

	seq_size = (int) ( filesize / (packet_size-sizeof(TypeMsg03100320)) ) +1;
	
	
	log_history(0,0,"seq_size[%d]",seq_size);
	log_history(0,0,"filedata malloc [%d]",sizeof(TypeMsg03100320DATA)*seq_size);

	filedata = (char *)malloc(sizeof(TypeMsg03100320DATA)*seq_size);
	memset(filedata,0x00,sizeof(TypeMsg03100320DATA)*seq_size);

	block_size = (int) ( seq_size / 100 ) +1;
	
	log_history(0,0,"block_size[%d]",block_size);

	seqinfo = (char *)malloc(block_size*100);

	memset(seqinfo,'0',block_size*100);
	
	CCL(szSaveFileName);
	CCL(szFileName);
	
//	if (f_L501)
//		sprintf(szSaveFileName,"%s",szCommonFileName); //L501MMDD
//	else
		sprintf(szSaveFileName,"%s_%s",szSendDate,szCommonFileName);
		
	sprintf(szFileName,"%s/%s",gConf.datadir,szSaveFileName);

	// read 0320
	CCL(szRetiredInfo);
	CCL(szBlockNo);
	CCL(szSequenceNo);
	CCL(szRetiredInfo);
	
	nStep = 1;
	
	while(1) {
		CCL(buff);
		ret = readPacket(newSockfd, buff, 60);
		if( ret == -1 )
		{
			log_history(0,0,"header recv client close[%d]",ret);
			isFalse = 1;
			break;
		} else if( ret == -2 )
		{
			log_history(0,0,"hreader recv Error[%d]",ret);
			isFalse = 1;
			break;
		} else if( ret == -3 )
		{	
			log_history(0,0,"packet size error[%d]",ret);
			isFalse = 1;
			break;
		} else if( ret == -4 )
		{	
			log_history(0,0,"timeout [%d]",ret);
			isFalse = 1;
			break;
		}
		cmd = classfyCMD(buff);

		log_history(0,0,"cmd[%d]buff[%.100s]",cmd,buff);


		if( cmd == CMD_DATASEND || cmd == CMD_RETIRED_DATA ) {
			
			pMsg03100320  = (TypeMsg03100320*)buff;

			CCL(tmp);
			memcpy(tmp,pMsg03100320->szDataByte,sizeof(pMsg03100320->szDataByte));
			real_size = atoi(tmp);
			
			memcpy(szBlockNo,pMsg03100320->szBlockNo,sizeof(pMsg03100320->szBlockNo));
			nBlockNo = atoi(szBlockNo);

			memcpy(szSequenceNo,pMsg03100320->szSequenceNo,sizeof(pMsg03100320->szSequenceNo));
			nSequenceNo = atoi(szSequenceNo);
			
			if( block_seq[(nBlockNo-1)] < nSequenceNo ) block_seq[(nBlockNo-1)] = nSequenceNo;

			seqinfo[(nBlockNo-1)*100+nSequenceNo-1] = '1';

			log_history(0,0,"nBlockNo[%d]nSequenceNo[%d]real_size[%d]",nBlockNo ,nSequenceNo,real_size);
			
			pMsg03100320DATA = (TypeMsg03100320DATA *)(filedata+(( (nBlockNo-1)*100+nSequenceNo -1)* sizeof(TypeMsg03100320DATA)));
			memcpy(pMsg03100320DATA->szBlockNo,pMsg03100320->szBlockNo,sizeof(pMsg03100320->szBlockNo));
			memcpy(pMsg03100320DATA->szSequenceNo,pMsg03100320->szSequenceNo,sizeof(pMsg03100320->szSequenceNo));
			memcpy(pMsg03100320DATA->szDataByte,pMsg03100320->szDataByte,sizeof(pMsg03100320->szDataByte));
			memcpy(pMsg03100320DATA->szData,buff+sizeof(TypeMsg03100320),real_size);
			
			nStep = 2;
		} else if ( cmd == CMD_RETIREDREQ) {
			// 결번확인지시
			pMsg0620  = (TypeMsg0620*)buff;

			CCL(szBlockNo);
			memcpy(szBlockNo,pMsg0620->szBlockNo,sizeof(pMsg0620->szBlockNo));
			nBlockNo = atoi(szBlockNo);

			CCL(szSequenceNo);
			memcpy(szSequenceNo,pMsg0620->szSequenceNo,sizeof(pMsg0620->szSequenceNo));
			nSequenceNo = atoi(szSequenceNo);
			
			log_history(0,0,"CMD_RETIREDREQ nBlockNo[%d]nSequenceNo[%d]",nBlockNo ,nSequenceNo);

			CCL(ret_buff);
			pMsg0300 = (TypeMsg0300*)ret_buff;
			memcpy(ret_buff,buff,sizeof(TypeCommon));
			
			memcpy(pMsg0300->szBlockNo,pMsg0620->szBlockNo,sizeof(pMsg0620->szBlockNo));
			memcpy(pMsg0300->szSequenceNo,pMsg0620->szSequenceNo,sizeof(pMsg0620->szSequenceNo));

			memset(szRetiredInfo,' ',sizeof(szRetiredInfo));
			szRetiredInfo[sizeof(szRetiredInfo)-1] = 0x00;
			int i = 0;
			int nRetiredCnt= 0;
			for(i=0;i<nSequenceNo;i++) {
				if( seqinfo[(nBlockNo-1)*100+i] == '1' ) {
					szRetiredInfo[i] = '1';
				} else {
					nRetiredCnt++;
					szRetiredInfo[i] = '0';
				}
			}

			memset(tmp,' ',sizeof(tmp));
			sprintf(tmp,"%03d", nRetiredCnt);
			memcpy(pMsg0300->szRetiredCnt,tmp,3);
			
			memcpy(pMsg0300->szRetiredInfo,szRetiredInfo,strlen(szRetiredInfo));

			CCL(tmp); strcpy(tmp,"000");	// set ack

			log_history(0,0,"CMD_RETIRED_ACK[%s]",ret_buff);

			ret = ackPacket(newSockfd,ret_buff,CMD_RETIRED_ACK,SIZE_CMD_RETIRED_ACK,tmp);
			if(ret <0 ) {
				isFalse = 1;
				goto SenderEND;	
			}

			nStep = 3;
		} else if( cmd == CMD_FILEDATA_END ) {
			break;
		} else {
			break;
		}

	}

	if( cmd == CMD_FILEDATA_END ) {
		// ack SIZE_CMD_FILEDATA_END_ACK

		CCL(ret_buff);
		pMsg06000610 = (TypeMsg06000610*)ret_buff;
		memcpy(ret_buff,buff,sizeof(TypeMsg06000610));
		CCL(tmp); strcpy(tmp,"000");	// set ack
		ret = ackPacket(newSockfd,ret_buff,CMD_FILEDATA_END_ACK,SIZE_CMD_FILEDATA_END_ACK,tmp);

		// read more CMD_END
		CCL(buff);
		ret = readPacket(newSockfd, buff, 60);
		if( ret == -1 )
		{
			log_history(0,0,"header recv client close");
			isFalse = 1;
			goto SenderEND;
		} else if( ret == -2 )
		{
			log_history(0,0,"hreader recv Error");
			isFalse = 1;
			goto SenderEND;
		} else if( ret == -3 )
		{	
			log_history(0,0,"packet size error");
			isFalse = 1;
			goto SenderEND;
		} else if( ret == -4 )
		{	
			log_history(0,0,"timeout [%d]",ret);
			isFalse = 1;
			goto SenderEND;
		}
		cmd = classfyCMD(buff);

		log_history(0,0,"cmd[%d]buff[%.100s]",cmd, buff);

		nStep = 4;

		if(cmd == CMD_END ) {
			// OK

			// ack SIZE_CMD_END_ACK
			CCL(ret_buff);
			pMsg06000610 = (TypeMsg06000610*)ret_buff;
			memcpy(ret_buff,buff,sizeof(TypeMsg06000610));
			CCL(tmp); strcpy(tmp,"000");	// set ack
			ret = ackPacket(newSockfd,ret_buff,CMD_END_ACK,SIZE_CMD_END_ACK,tmp);

			isFalse = 0;
			nStep = 5;


		}

		log_history(0,0,"END RECV");
	}
		
SenderEND:

	nDataCnt = 0;

	// header read
	pMsg03100320DATA = (TypeMsg03100320DATA *)(filedata);
	CCL(tmp);
	memcpy(tmp,pMsg03100320DATA->szDataByte,sizeof(pMsg03100320DATA->szDataByte));
	real_size = atoi(tmp);
	CCL(szReqSendDate);
	if(real_size > 0 ) {
		CCL(buff_data);
		memcpy(buff_data,pMsg03100320DATA->szData, real_size);
		memcpy(szReqSendDate,buff_data+12,14);
	}

	log_history(0,0,"szFileName[%s]isFalse[%d]",szFileName,isFalse);
	if ((oFD = fopen(szFileName, "w")) == NULL)
	{
		log_history(0,0,"log file open error[%s][%s]",szFileName,strerror(errno));
	} else {
		
		CCL(szFileData);
		

		nDataCntInFile = 0;

		for(i=0;i<seq_size;i++) {

			pMsg03100320DATA = (TypeMsg03100320DATA *)(filedata+( i* sizeof(TypeMsg03100320DATA)));
			CCL(tmp);
			memcpy(tmp,pMsg03100320DATA->szDataByte,sizeof(pMsg03100320DATA->szDataByte));
			real_size = atoi(tmp);
			if(real_size > 0 ) 
			{
				CCL(buff_data);
				memcpy(buff_data,pMsg03100320DATA->szData, real_size);
				
				if (f_L501)
				{	
					/*
					 * HEADER/BODY/TAIL 모두 100byte 이므로
					 * SZ_MSGBL501로 나눠서 처리
					 */
					f_cnt = real_size / SZ_MSGBL501;
					log_history(0,0,"f_cnt [%d], real_size[%d], SZ_MSGBL501[%d]", f_cnt, real_size, SZ_MSGBL501);
					for (ii = 0; ii < f_cnt; ii++)
					{
						BL501 = (TypeMsgBL501*)&buff_data[SZ_MSGBL501*ii];
						//log_history(0,0,"BL501 [%d][%.*s]", ii, SZ_MSGBL501, BL501);
						if (memcmp(BL501->data_type, DATA_TP_HEAD, SZ_DATA_TP) == 0 )
						{
							continue;
						}
						else if (memcmp(BL501->data_type, DATA_TP_BODY, SZ_DATA_TP) == 0)
						{
							fprintf(oFD,"%.*s,\n",sizeof(BL501->cust_no), BL501->cust_no);
							++recv_cnt;
						}
						else if (memcmp(BL501->data_type, DATA_TP_TAIL, SZ_DATA_TP) == 0)
						{
							TypeTail *BL501Tail = (TypeTail*)&buff_data[SZ_MSGBL501*ii];
							sprintf(body_cnt, "%.*s", sizeof(BL501Tail->body_cnt), BL501Tail->body_cnt);
							if (recv_cnt == atoi(body_cnt))
								f_succ = 1;
								
							log_history(0,0,"BL501->body_cnt[%.*s] recv_cnt[%d]f_succ[%d]"
										, sizeof(BL501Tail->body_cnt), BL501Tail->body_cnt
										, recv_cnt
										, f_succ);
						}
					}
				}
				else
				{
					if( copy_start_position == 0 ) CCL(szFileData);
					
					copy_remain_size = real_size;
					log_history(0,0,"FileCheck S :seq[%d]real_size[%d]copy_start_position[%d]copy_remain_size[%d]",
												i,real_size,copy_start_position,copy_remain_size);
					// 
	
					printf("1:real_size[%d]copy_start_position[%d]copy_remain_size[%d]copy_size[%d]\n",
							real_size,copy_start_position,copy_remain_size,copy_size);
	
					if(copy_start_position != 0 ) {
						// 남은바이트만큼 먼저 읽는다
						copy_size = readsize-copy_start_position;
						if(copy_remain_size >= copy_size ) {
							memcpy(szFileData+copy_start_position,buff_data+(real_size-copy_remain_size),copy_size);
							if(szFileData[0] == 'D') nDataCnt++;
							if(szFileData[0] == 'T' ) {
								CCL(tmp);
								memcpy(tmp,szFileData+1,11);
								nDataCntInFile = atoi(tmp);
							}
							copy_start_position = 0;
							copy_remain_size -= copy_size;
							printf("2-1:real_size[%d]copy_start_position[%d]copy_remain_size[%d]copy_size[%d]\n"
										,real_size,copy_start_position,copy_remain_size,copy_size);
						} else {
							memcpy(szFileData+copy_start_position,buff_data+(real_size-copy_remain_size),copy_remain_size);
							copy_start_position += copy_remain_size;
							copy_remain_size = 0;
							printf("2-2:real_size[%d]copy_start_position[%d]copy_remain_size[%d]copy_size[%d]\n"
										,real_size,copy_start_position,copy_remain_size,copy_size);
						}
						
					}
					while( copy_remain_size >= readsize ) {
						copy_size = readsize;
						memcpy(szFileData+copy_start_position,buff_data+(real_size-copy_remain_size),readsize);
	
						printf("3:real_size[%d]copy_start_position[%d]copy_remain_size[%d]copy_size[%d]\n"
										,real_size,copy_start_position,copy_remain_size,copy_size);
	
						if(szFileData[0] == 'D') nDataCnt++;
						if(szFileData[0] == 'T' ) {
							CCL(tmp);
							memcpy(tmp,szFileData+1,11);
							nDataCntInFile = atoi(tmp);
						}
						copy_start_position = 0;
						copy_remain_size -= readsize;
					}
					if(copy_remain_size>0 ) {
						copy_size = copy_remain_size;
						memcpy(szFileData+copy_start_position,buff_data+(real_size-copy_remain_size),copy_size);
						copy_start_position = copy_size;
	
						printf("4:real_size[%d]copy_start_position[%d]copy_remain_size[%d]copy_size[%d]\n"
										,real_size,copy_start_position,copy_remain_size,copy_size);
	
					}
				
					fprintf(oFD,"%s",buff_data);
					printf("5:real_size[%d]copy_start_position[%d]copy_remain_size[%d]copy_size[%d]\n"
										,real_size,copy_start_position,copy_remain_size,copy_size);
	
					log_history(0,0,"FileCheck E :seq[%d]real_size[%d]copy_start_position[%d]copy_remain_size[%d]D[%d]/[%d]",
												i,real_size,copy_start_position,copy_remain_size,nDataCnt,nDataCntInFile);
				}
				log_history(0,0,"buff_data [%.150s]", buff_data);
				

			}

			//log_history(0,0,"szFileName Write:seq[%d]real_size[%d]",i,real_size);
		}

		fclose(oFD);
	}
	if(filedata!= NULL) 
	{
		free(filedata);
		filedata = NULL;
	}

	if (f_L501)
	{
		/* 
		 * 'L501' 전문은 SHELL을 통한 DB작업
		 * FORK로 shell 호출
		 * 테이블 등록 및 삭제 작업 처리
		 */
		if (f_succ)
		{
			CCL(szFileNameSucc);
			sprintf(szFileNameSucc,"%s/%s_",gConf.datadir,szSaveFileName);
			if (rename(szFileName, szFileNameSucc) == -1)
				log_history(0,0,"파일변경 오류");
			log_history(0,0,"L501 SUCCESS : Filename Change : [%s]",szFileNameSucc);


			int child_pid;
			char tmpPath[512];
			char execProcessName[512];
			char tmpname[512];
			char filename[64];
			strcpy(tmpname,"filter_load_proc.sh");
			strcpy(execProcessName,"/user/neomms/scripts/FILTER/filter_load_proc.sh");
			if( (child_pid = fork()) == 0 )
			{
				if (seqinfo!= NULL) 
				{
					free(seqinfo);
					seqinfo = NULL;
				}
					
				log_history(0,0,"L501 FORK_START!!");
				memset(tmpPath,0x00,sizeof(tmpPath));
				sprintf(tmpPath,"/user/neomms/scripts/FILTER/");
				if (chdir(tmpPath) < 0)
				{
					log_history(0,0,"[startProcess] %s chdir error.[%s]",tmpPath,strerror(errno));
					exit(0);
				}
				sprintf(filename,"%s_",szSaveFileName);
				if (execlp(execProcessName,tmpname, filename, (char*)0)  < 0 )
				{
					log_history(0,0,"[startProcess] %s execlp error.<%s>",
							execProcessName, strerror(errno));
					exit(0);
				}
				exit(0);
			} else if( child_pid < 0 ) {
				log_history(0,0,"startProcess Error [%s]",strerror(errno));
				CCL(alertmsgbuf);
				 sprintf(alertmsgbuf, "BC LMS WHITE_LIST FILE FORK FAIL [%s]", szSaveFileName);
		        if ( AlertRecv2Admin("msgbody=%s&type=%d", alertmsgbuf , 0) < 0 )
		            monitoring("[FileRecv]AlertRecv2Admin Failed...",0,0);
			} 	
			sleep(1);
			log_history(0,0,"L501 FORK_SUCCESS!!");
		
		}
		else
		{
			log_history(0,0,"L501 FAIL : RECV_COUNT_ERR ");
			
			CCL(alertmsgbuf);
			 sprintf(alertmsgbuf, "BC LMS WHITE_LIST FILE FAIL [%s]", szSaveFileName);
	        if ( AlertRecv2Admin("msgbody=%s&type=%d", alertmsgbuf , 0) < 0 )
	            monitoring("[FileRecv]AlertRecv2Admin Failed...",0,0);
		}	
		goto ENDDOTHREAD;		
	}

	log_history(0,0,"DB GET");

    if( dbConnQ.size() > 0 )
    {
        pDB = dbConnQ.front();
        if( pDB != NULL ) 
        {
            dbConnQ.pop();
        }
    } else {
        pDB = NULL;
    }

/*
0071
SPS
72
0600
S
C
L0030503
000
0503180850
001
BCCARDFTP           
BCCARDCO.LTD    


	char szSendDate[10+1];
	char szInfoUpmu[3+1];
	char szSender[20+1];
	char szPassword[16+1];
	char szCommonFileName[8+1];
	char szSaveFileName[256];
	char szReqSendDate[14+1];
	char szReqUser[10+1];
	char szReqPWD[10+1];

*/
	log_history(0,0,"DB GET OK");


	
	log_history(0,0,"szReqSendDate[%s]",szReqSendDate);

	n_SEQ_TBL_BC_RESERV_F_GRP = -99;
	n_SEQ_TBL_BC_RESERV_F_GRP_SND = -99;
	
	CCL(szSqlErrorMsg);
	nSqlCode = -999;

    EXEC SQL CONTEXT USE :pDB;
    EXEC SQL EXECUTE
        BEGIN
        PROC_SET_BC_FILE_RECVINFO(
				in_senddate  =>:szSendDate,
				in_sender =>:szSender,
				in_password  =>:szPassword,
				in_recv_filename =>:szCommonFileName,
				in_filename =>:szSaveFileName,
				in_req_day =>:szReqSendDate,
				in_recv_size =>:nSize,
				in_recv_step =>:nStep,
				in_msgcnt =>:nDataCnt,
				in_msgcnt_infile =>:nDataCntInFile,
				ot_seqno   =>:nSeqno,
				ot_f_grp_id =>:n_SEQ_TBL_BC_RESERV_F_GRP,
				ot_f_send_id =>:n_SEQ_TBL_BC_RESERV_F_GRP_SND,
				ot_sqlcode  =>:nSqlCode,
				ot_sqlmsg   =>:szSqlErrorMsg
                );
		END;
    END-EXEC;

    log_history(0,0,"-PROC_SET_BC_FILE_RECVINFO-[%s][%s][%s][%s][%s][%s][%d][%d][%d][%d]",
            szSendDate,szSender,szPassword,szCommonFileName,szSaveFileName,szReqSendDate,nSize,nStep,nDataCnt,nDataCntInFile
		);

    log_history(0,0,"-PROC_SET_BC_FILE_RECVINFO-[%d][%d][%s]",
            nSeqno,
            nSqlCode,
            szSqlErrorMsg);

    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            log_history(0,0,"PROC_SET_BC_FILE_RECVINFO sqlcode[%d] sqlmsg[%s]", 
                    sqlca.sqlcode,
                    trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc))
                    );
           
        } else {

	        log_history(0,0,"PROC_SET_BC_FILE_RECVINFO otReuslt[%d]errMsg[%s]", 
                nSqlCode,
                trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));

		}

    }

	
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL COMMIT;
    
	log_history(0,0,"DB dbConnQ.push");

    dbConnQ.push(pDB);

ENDDOTHREAD:

	/*
	 * 20140901 운영요구사항
	 * 성공시 SMS전송 보내지 않음
	 */
	/*
	if(nStep == 5) {
		CCL(alertmsgbuf);
		 sprintf(alertmsgbuf, "BC LMS FILE RECV [%s][%d/%d]", szSaveFileName,nDataCnt,nDataCntInFile);
        if ( AlertRecv2Admin("msgbody=%s&type=%d", alertmsgbuf , 0) < 0 )
            monitoring("[FileRecv]AlertRecv2Admin Failed...",0,0);

	}
	*/



	if(seqinfo!= NULL) 
	{
		free(seqinfo);
		seqinfo = NULL;
	}
	

	if(isFalse==1) return -1;
	return 0;
}


/* @brief logonInfo에서 가져온 IP:PORT 를 정보와 일치하는지 체크
 * @return succ 0 fail -1
 */
int checkServerInfo(CLogonDbInfo& logonDbInfo,char* szIP,int nPort)
{
    char szCmpString[32];

    CCL(szCmpString);
    sprintf(szCmpString,"%s:%d",szIP,nPort);

    /* 값이 없으면 체크 하지 않는다 */
    if( strlen(logonDbInfo.szServerInfo) != 0 )
    {
        if( strstr(logonDbInfo.szServerInfo,szCmpString) == 0 )
        {
            log_history(0,0,"ServerInfo[%s] This[%s] Error",
                    logonDbInfo.szServerInfo,
                    szCmpString);
            return -1;
        }
    }


    /* 허용 아이피 체크 0.0.0.0 : 모두 허용 */
    if( strstr(logonDbInfo.szSIP,"0.0.0.0") != NULL )
    {
        if( strstr(logonDbInfo.szSIP,logonDbInfo.szIP) != NULL )
        {
            log_history(0,0,"ServerInfoSIP[%s] customIP[%s] Error",
                    logonDbInfo.szSIP,
                    logonDbInfo.szIP);
            return -2;
        }
    }
    return 0;
}



int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.serverIP , conf.FetchEntry("gw.ip"),16);
    if( gConf.serverIP == NULL ) strcpy(gConf.serverIP,"");

    conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
    if( gConf.logonDBName == NULL ) strcpy(gConf.logonDBName,"");

    gConf.serverPORT = conf.FetchEntryInt("gw.port");
    gConf.process_sleep_time = conf.FetchEntryInt("process.sleeptime");

    gConf.file_datasize = conf.FetchEntryInt("file.datasize");
	if(gConf.file_datasize  == 0 ) gConf.file_datasize  = 2000;

    conf.strncpy2(gConf.bindir , conf.FetchEntry("process.bindir"),64);
    if( gConf.bindir == NULL ) strcpy(gConf.bindir,"");

    conf.strncpy2(gConf.cfgdir , conf.FetchEntry("process.cfgdir"),64);
    if( gConf.cfgdir == NULL ) strcpy(gConf.cfgdir,"");

    conf.strncpy2(gConf.logPath , conf.FetchEntry("log.path"),64);
    if( gConf.logPath == NULL ) strcpy(gConf.logPath,"");

    conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.path"),64);
    if( gConf.domainPath == NULL ) strcpy(gConf.domainPath,"");

    conf.strncpy2(gConf.datadir , conf.FetchEntry("process.datadir"),64);
    if( gConf.datadir == NULL ) strcpy(gConf.datadir,"");

    conf.strncpy2(gConf.dbID , conf.FetchEntry("db.id"),16);
    if( gConf.dbID == NULL ) strcpy(gConf.dbID,"");

    conf.strncpy2(gConf.dbPASS , conf.FetchEntry("db.pass"),16);
    if( gConf.dbPASS == NULL ) strcpy(gConf.dbPASS,"");

    conf.strncpy2(gConf.dbSID , conf.FetchEntry("db.sid"),16);
    if( gConf.dbSID == NULL ) strcpy(gConf.dbSID,"");


    return 0;
}


void Init_Oracle(sql_context ctx)
{
    EXEC SQL BEGIN DECLARE SECTION;
    VARCHAR Username[10];
    VARCHAR Password[10];
    VARCHAR dbstring[10];
    EXEC SQL END DECLARE SECTION;

    strcpy((char*)Username.arr, gConf.dbID);
    Username.len = strlen((char*)Username.arr);
    strcpy((char*)Password.arr, gConf.dbPASS);
    Password.len = strlen((char*)Password.arr);
    strcpy((char*)dbstring.arr, gConf.dbSID);
    dbstring.len = strlen((char*)dbstring.arr);

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
}

int AlertRecv2Admin(char* pForm,...)
{
	int sockfd, len;
	struct sockaddr_in serv_addr;
	struct timeval tv;

	char strParam[256];
	char transBuf[350];

	va_list pArg;
	va_start(pArg, pForm);
	vsprintf(strParam, pForm, pArg);
	va_end(pArg);

    strReplace(strParam," ","%20");
	memset(transBuf,0x00,sizeof(transBuf));
	sprintf(transBuf,"get %s?%s\n",WEB_PAGE,strParam);
	printf("\nAlert2Admin:[%s]\n",transBuf);

	memset((char*)&serv_addr,0x00,sizeof(serv_addr));
	serv_addr.sin_family = AF_INET;
	serv_addr.sin_addr.s_addr = inet_addr(CALL_WEB);
	serv_addr.sin_port = htons(PORT_WEB);

	if ( (sockfd=socket(AF_INET,SOCK_STREAM,0))<0 ) {
		return -1;
	}
	
	tv.tv_sec = 5;
	tv.tv_usec = 0;
	
	if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv)) < 0 ) {
        return -1;
	}

	if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv)) < 0 ) {
	    return -1;
	}
	
	if ( connect(sockfd,(struct sockaddr*)&serv_addr, sizeof(struct sockaddr))<0 ) {
		return -1;
	}
	len = write(sockfd,transBuf,strlen(transBuf));
	if( len < 0 )
	    return -1;
	memset(transBuf,0x00,sizeof(transBuf));
	len = read(sockfd,transBuf,sizeof(transBuf));
	close(sockfd);
	if (strncmp(transBuf,"OK",2) != 0 )
	    return -1;
	printf("\n%s\n",transBuf);
	return 0;
}
