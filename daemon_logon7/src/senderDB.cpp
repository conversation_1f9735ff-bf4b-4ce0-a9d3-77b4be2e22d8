#include "senderDB.h"

EXEC SQL BEGIN DECLARE SECTION;
#define MAX_DB_CONNECTION 1
EXEC SQL END DECLARE SECTION;

queue<void*, list<void*> > dbConnQ;

std::list <CThreadInfo*> listThreadHandle;
typedef std::list <CThreadInfo*>::iterator listPosition;

/*
 * queue<pthread_t, list<pthread_t> > threadQ;
 */

sem_t m_sem;

void logfunc(const char *s) {
    log_history(0,0,"[%s]",s);
}

int main(int argc, char* argv[])
{
    int ret;
    char logMsg[SOCKET_BUFF];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;
    CKSThread ksthread;
    CThreadInfo* pThreadInfo;
    char buff[SOCKET_BUFF];
    int hNewSocket;
    TypeMsgBindSnd* pLogonData;
    int i;
    CKSConfig conf;


    listPosition pos;
    listPosition posPrev;
    int nThreadCount = 0;


    EXEC SQL BEGIN DECLARE SECTION;
    sql_context db[MAX_DB_CONNECTION];
    EXEC SQL END DECLARE SECTION;

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }


    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        exit(1);
    }

    log_history(0,0,"[%s]",
            gConf.senderDBName);

    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server();

    //    void* db;

    EXEC SQL ENABLE THREADS;
    for(i=0;i<MAX_DB_CONNECTION;i++)
    {
        EXEC SQL CONTEXT ALLOCATE :db[i];

        Init_Oracle(db[i]);
        if( sqlca.sqlcode !=0 )
        {
            monitoring("db connection fail error",0,errno);
            ml_sub_end();
            return -1;
        }

        log_history(0,0,"db[%x]",db[i]);
        dbConnQ.push(db[i]);
    }

    sem_init(&m_sem,0,1);

    ret = svrSockfd.createDomainNon(gConf.senderDBName);
    if( ret !=0 ) {
        log_history(0,0,"DOMAIN_SENDERDB 생성실패[%s]",
                strerror(errno),
                gConf.senderDBName);
        goto ENDMAIN;
    }


    while(activeProcess)
    {

        wait_a_moment(100);
        pos = listThreadHandle.begin();
        nThreadCount = 0;
        while( pos != listThreadHandle.end() )
        {
            nThreadCount++;
            posPrev = pos++;


            if( (*posPrev)->tid <= 0 )
            {
                log_history(0,0,"bug--------------------------[%d]",(*posPrev)->tid);
                    listThreadHandle.erase(posPrev);
                continue;
            }

            ret =  pthread_kill((*posPrev)->tid,0);

            switch (ret) {
                case 0 : /* thread is alive */     
                    break;
                case ESRCH:
                case EINVAL:
                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
                default:
                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
            }
        }


        hNewSocket = svrSockfd.accept();
        if( hNewSocket <= 0 )
            continue;


        pThreadInfo = NULL;
        pThreadInfo = new CThreadInfo;
        if( pThreadInfo == NULL )
        {
            log_history(0,0,"new ThreadInfo Error [%s]",
                    strerror(errno));
            close(hNewSocket);
            continue;
        }

        pThreadInfo->sock = hNewSocket;
        ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
        if( ret != 0 )
        {
            log_history(0,0,"create thread Error [%s]",
                    strerror(errno));
            close(pThreadInfo->sock);
            delete pThreadInfo;
            continue;
        }
        listThreadHandle.push_back( pThreadInfo);
/*        threadQ.push(pThreadInfo->tid); */

    }

ENDMAIN:

    svrSockfd.close();

        pos = listThreadHandle.begin();
        nThreadCount = 0;
        while( pos != listThreadHandle.end() )
        {
            nThreadCount++;
            posPrev = pos++;
            ret =  pthread_kill((*posPrev)->tid,0);

            switch (ret) {
                case 0 : /* thread is alive */     
            log_history(0,0,"thread closing [%d]",(pthread_t)(*posPrev));
                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
                case ESRCH:
                case EINVAL:
                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
                default:
                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
            }
        }


    sem_destroy(&m_sem);

    EXEC SQL BEGIN DECLARE SECTION;
    sql_context pDB;
    EXEC SQL END DECLARE SECTION;


    sleep(2);
    while( dbConnQ.size()>0 )
    {

        pDB = dbConnQ.front();
        if( pDB == NULL ) break;

        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL COMMIT WORK RELEASE;
        EXEC SQL CONTEXT FREE :pDB;
        dbConnQ.pop();
    }


    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}


void* doService(void* param)
{
/*    pthread_detach(pthread_self());  */
    int ret;
    CKSSocket newSockfd;
    char buff[SOCKET_BUFF];
    CThreadInfo* info = (CThreadInfo*)param;
    CSenderDbInfoAck ack;

    void* pDB;
    struct sqlca sqlca;
    time_t ThisT,LastT;
    bool bFirst=true;

    memset((char*)&ack,0x00,sizeof(ack));

    newSockfd.attach(info->sock);
    CCL(buff);
    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 ) {
        log_history(0,0,"connection 후 3초간 데이터가 없어 연결을 close 합니다");
        goto ENDDOTHREAD;
    }

    if( ret < 0 ) {
        log_history(0,0,"recv Error");
        goto ENDDOTHREAD;
    }

#ifdef DEBUG
    viewPack(buff,ret);
#endif
    if( memcmp(buff,"getInfo",7) == 0 )
    {
        offerInfo(newSockfd);
        goto ENDDOTHREAD;
    }

    time(&LastT);

REGETDB:
    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        { 
            log_history(0,0,"세마포어 wait Error");
            goto ENDDOTHREAD;
        }
    /*   크리티컬 섹션
         값을 가져올때까지 반복하기(세마포어 풀고)  timeout 5 sec
         */

    if( dbConnQ.size() > 0 )
    {
        pDB = dbConnQ.front();
        if( pDB != NULL ) 
        {
/*            log_history(0,0,"pop NOT NULL pDB[%x] [%d]", pDB,dbConnQ.size()); 
 */
            dbConnQ.pop();
        }
    } else {
        pDB = NULL;
    }



    if( sem_post(&m_sem) == -1 )
    {
        log_history(0,0,"세마포어 해제 Error [%s]",strerror(errno));
        goto ENDDOTHREAD;
    }

    if( pDB == NULL ) {
        time(&ThisT);
        if( (ret = (int)difftime(ThisT,LastT)) > gConf.dbFindTimeOut )
        {
            log_history(0,0,"find db handle timeout [%d]sec",
                    gConf.dbFindTimeOut);
            goto ENDDOTHREAD;
        }
        if( ret > 2 && bFirst )
        {
            bFirst = false;
            log_history(0,0,"[%d]",ret);
        }
        wait_a_moment(1000);
        goto REGETDB;
    }


    /* DB 작업 : pDB */
  /*  log_history(0,0,"pDB [%x]",pDB); */
    alarm(15);
    ret = setMsgDB(pDB,sqlca,buff,ack);
    if( ret < 0 )
    {
        log_history(0,0,"setMsgDB Error");
        ret = errorDBprocess(pDB);
        alarm(0);
        goto ENDDOTHREAD;
    }
    alarm(0);


/*
    if( ret < 0 )
    {
        goto ENDDOTHREAD;
    }
*/

    sprintf(ack.szResult,"%02d",ret);

    ret = newSockfd.send((char*)&ack,sizeof(ack));
    if( ret == sizeof(ack))
    {
        /* commit */
        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL COMMIT;

    } else {
        /* rollback */
        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL ROLLBACK;
        log_history(0,0,"domain socket send(ack) Error",strerror(errno));
    }


    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        { 
            log_history(0,0,"세마포어 wait Error");
            goto ENDDOTHREAD;
        }
    /* 크리티컬 섹션 */
    dbConnQ.push(pDB);

    if( sem_post(&m_sem) == -1 )
    {
        log_history(0,0,"세마포어 해제 Error [%s]",strerror(errno));
        goto ENDDOTHREAD;
    }

ENDDOTHREAD:
    newSockfd.close();
/*    delete info; */
    return NULL;
}

int setMsgDB(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
    CSenderDbInfo* data = (CSenderDbInfo*)buff;
    EXEC SQL BEGIN DECLARE SECTION;


    char szTelco[36];
    int nCnt = -1;
    char szSqlErrorMsg[1024];
    int nmPID;
    char szPID[8];
    char szJOB[8];
    int nPID;
    int nJOB;


    char szAppName[16];
    char szPRT[4];
    int otResult = -1;
    char szSerial[16+1];
    char szDstadr[16+1];
    char szCalbck[16+1];
    char szSndmsg[100+1];
    char szReserve[128];
    int nPRT;
    int nTelco;


    int nMsgId;


    EXEC SQL END DECLARE SECTION;


    CCL(szSndmsg);
    CCL(szReserve);
    CCL(szDstadr);
    CCL(szCalbck);
    CCL(szSerial);
    CCL(szAppName);
    CCL(szPRT);
CCL(szTelco);


    /* gettimeofday */            
    struct timeval timefirst, timesecond; 
    struct timezone tzp;      
    int secBuf, microsecBuf;  
    float timeBuf;    


    gettimeofday(&timefirst,&tzp); 
    /* gettimeofday */   


    CCL(szTelco);
    CCL(szSqlErrorMsg);
    CCL(szPID);
    CCL(szJOB);

    nPID = data->logonDbInfo.nmPID;

data->smsData.szMsgType[3] = 0x00;

    if( atoi(data->smsData.szMsgType) == 1 )
    {
        nJOB = data->logonDbInfo.nUrlJob; /* url */
        strcpy(szTelco,data->senderInfo.szUrlTelcoInfo);
    }
    else
    {
        nJOB = data->logonDbInfo.nmJOB; /* sms */ 
        strcpy(szTelco,data->senderInfo.szSmsTelcoInfo);
    }



    if( nJOB == -99 )
        return 56;


/*
    nStatus = -999;

    EXEC SQL CONTEXT USE :pDB;
    EXEC SQL EXECUTE
        BEGIN
        proc_get_telinfo_ext_l(
                in_ptn_id      =>    :nPID,
                in_jobcode     =>    :nJOB,
                ot_ptn_telco   =>    :szTelco,
                ptn_status     =>    :nStatus,
                ot_cnt         =>    :nCnt,
                ot_ErrMsg      =>    :szSqlErrorMsg);
    END;
    END-EXEC;

    if( nStatus != 0 )
    {
        log_history(0,0,"proc_get_telinfo status [%d] errmsg[%s]", nStatus,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        if( nStatus == -999 )
        {
            log_history(0,0,"proc_get_telinfo sqlcode[%d]", 
                    sqlca.sqlcode
                   );
            return -1;
        }
        return 96;
    }
*/

    nTelco = getTelcoId(szTelco,data->smsData.szDstadr);
    CCL(szAppName);
    CCL(szPRT);
    CCL(szSqlErrorMsg);
    sprintf(szAppName,"%s_%s",szPID,szJOB);
    sprintf(szPRT,"%d", data->logonDbInfo.nmPRT);

    nPRT = data->logonDbInfo.nmPRT;
    memcpy(szSerial,data->smsData.szSerial,16);
    memcpy(szDstadr,data->smsData.szDstadr,12);
    memcpy(szCalbck,data->smsData.szCalbck,12);
    memcpy(szSndmsg,data->smsData.szSndmsg,85);
    strcpy(szReserve,data->szReserve);

    otResult = -999;

    if( atoi(data->smsData.szMsgType) == 1 )
    {
        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL EXECUTE      
            BEGIN
            proc_set_urldata_ext2(         
                    ptn_id=>:nPID,
                    telco_id=>:nTelco,           
                    ptn_sn=>:szSerial,             
                    dstaddr=>:szDstadr,            
                    callback=>:szCalbck,           
                    msg_body=>:szSndmsg,           
                    resv_data=>:szReserve,              
                    priority=>:nPRT,             
                    job_code=>:nJOB,             
                    ot_msgid=>:nMsgId,             
                    ot_result=>:otResult,        
                    ot_errm=>:szSqlErrorMsg);           
        END;
        END-EXEC;
    } else {
        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL EXECUTE
            BEGIN
            proc_set_msgdata_ext3(
                    ptn_id      =>    :nPID,
                    telco_id     =>   :nTelco ,
                    ptn_sn   =>    :szSerial,
                    dstaddr         =>    :szDstadr,
                    callback      =>    :szCalbck,
                    msg_body     =>    :szSndmsg,
                    resv_data     =>    :szReserve,
                    priority     =>    :nPRT,
                    job_code     =>    :nJOB,
                    ot_msgid    =>  :nMsgId,
                    ot_result     =>    :otResult,
                    ot_rstmsg     =>    :szSqlErrorMsg);
        END;
        END-EXEC;
    }



    if( otResult != 0 )
    {
        if( otResult == -999 )
        {
            log_history(0,0,"proc_set_msgdata type[%.2s]sqlcode[%d] sqlmsg[%s]", 
                    data->smsData.szMsgType,
                    sqlca.sqlcode,
                    trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc))
                    );
            return -1;
        }

        log_history(0,0,"proc_set_msgdata type[%s]otReuslt[%d]errMsg[%s]", 
                data->smsData.szMsgType,
                otResult,
                trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        return 58;
    }

    ack.msgid = nMsgId;
    gettimeofday(&timesecond,&tzp);

    secBuf = (timesecond.tv_sec - timefirst.tv_sec);
    microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
    timeBuf = microsecBuf;
    timeBuf = timeBuf / 1000000;
    timeBuf = timeBuf + secBuf;
    log_history(0,0,"db time [%f]",timeBuf);


    return 55;
}



void Init_Oracle(sql_context ctx)
{
    EXEC SQL BEGIN DECLARE SECTION;
    VARCHAR Username[10];
    VARCHAR Password[10];
    VARCHAR dbstring[10];
    EXEC SQL END DECLARE SECTION;

    strcpy((char*)Username.arr, gConf.dbID);
    Username.len = strlen((char*)Username.arr);
    strcpy((char*)Password.arr, gConf.dbPASS);
    Password.len = strlen((char*)Password.arr);
    strcpy((char*)dbstring.arr, gConf.dbSID);
    dbstring.len = strlen((char*)dbstring.arr);

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
}


int errorDBprocess(void* pDB)
{
    CKSThread dbErr;
    int ret;
    pthread_t tid;

    ret = dbErr.create(&(tid),NULL,doDBError,(void*)pDB);
    if( ret != 0 )
    {
        log_history(0,0,"create thread Error [%s] Current queSize[%d]",
                strerror(errno),
                dbConnQ.size());
    }

    return -1;
}


void* doDBError(void* param)
{
    pthread_detach(pthread_self()); 
    struct sqlca sqlca;
    EXEC SQL BEGIN DECLARE SECTION;
    sql_context pDB = (sql_context)param;
    EXEC SQL END DECLARE SECTION;



DBERRCONN:

    EXEC SQL CONTEXT USE :pDB;
    EXEC SQL COMMIT WORK RELEASE;
    EXEC SQL CONTEXT FREE :pDB;


    if( activeProcess == false )
        return NULL;

    EXEC SQL CONTEXT ALLOCATE :pDB;

    Init_Oracle(pDB);
    if( sqlca.sqlcode !=0 )
    {
        monitoring("db connection fail error",0,errno);
        wait_a_moment(900000);
        goto DBERRCONN;
    }

    log_history(0,0,"new db[%x]",pDB);
    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        { 
            log_history(0,0,"세마포어 wait Error");
            wait_a_moment(900000);
            goto DBERRCONN;
        }
    /* 크리티컬 섹션 */
    dbConnQ.push(pDB);

    if( sem_post(&m_sem) == -1 )
    {
        log_history(0,0,"세마포어 해제 Error [%s][%x]",
                strerror(errno),
                pDB);
        return NULL;
    }

    log_history(0,0,"current queSize[%d]",dbConnQ.size());

    return NULL;
}

int getTelcoId(char* szTelco,char* szDstaddr)
{
    char* p;
    int telcoArray[7];  
    char CID[4];
    int i=0;
    memset(telcoArray,0x00,sizeof(telcoArray));

    p = strtok(szTelco,"|");
    if( p == NULL ) return gConf.telcoDefaultID;
    telcoArray[0] = atoi(p);



    while(p = strtok(NULL,"|") )
    {
        telcoArray[++i]= atoi(p);
        if( i > 6 ) break;
    }
/*    if( i != 6 ) return gConf.telcoDefaultID;
*/

    CCL(CID);
    memcpy(CID,szDstaddr,3);

    switch(atoi(CID))
    {
        case 11 :
            return telcoArray[0];

        case 16 :
            return telcoArray[1];

        case 17 :
            return telcoArray[2];

        case 18 :
            return telcoArray[3];

        case 19 :
            return telcoArray[4];

        case 10 :
            return telcoArray[5];

        case 50 :
            return telcoArray[6];
    }

    return gConf.telcoDefaultID;
}



/* queue<void*, list<void*> > dbConnQ;
 */

int offerInfo(CKSSocket& newSockfd)
{
    char szQueSize[4];
    CCL(szQueSize);
    sprintf(szQueSize,"%d",dbConnQ.size());
    newSockfd.send(szQueSize,strlen(szQueSize));
    return 0;
}

int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.senderDBName , conf.FetchEntry("domain.senderdb"),64);
    if( gConf.senderDBName == NULL ) strcpy(gConf.senderDBName,"");

    gConf.telcoDefaultID = conf.FetchEntryInt("telco.defaultid");
    
    gConf.dbFindTimeOut = conf.FetchEntryInt("db.findtimeout");
    if( gConf.dbFindTimeOut <= 0 )
        gConf.dbFindTimeOut = 2;

    conf.strncpy2(gConf.dbID , conf.FetchEntry("db.id"),16);
    if( gConf.dbID == NULL ) strcpy(gConf.dbID,"");

    conf.strncpy2(gConf.dbPASS , conf.FetchEntry("db.pass"),16);
    if( gConf.dbPASS == NULL ) strcpy(gConf.dbPASS,"");

    conf.strncpy2(gConf.dbSID , conf.FetchEntry("db.sid"),16);
    if( gConf.dbSID == NULL ) strcpy(gConf.dbSID,"");


    return 0;
}



