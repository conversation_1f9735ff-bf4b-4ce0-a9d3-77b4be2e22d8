#include "senderMMSDB.h"

EXEC SQL BEGIN DECLARE SECTION;
#define MAX_DB_CONNECTION 8
EXEC SQL END DECLARE SECTION;

queue<void*, list<void*> > dbConnQ;

std::list <CThreadInfo*> listThreadHandle;
typedef std::list <CThreadInfo*>::iterator listPosition;

/*
 * queue<pthread_t, list<pthread_t> > threadQ;
 */

sem_t m_sem;

void logfunc(const char *s) 
{
    log_history(0,0,"[%s]",s);
}

int main(int argc, char* argv[])
{
	int ret;
	int i;
	int hNewSocket;
	
	char logMsg[SOCKET_BUFF];
	char buff[SOCKET_BUFF];
	
	struct timeval outtime;
	
	CKSConfig conf;
	CKSSocket svrSockfd;
	CKSSocket newSockfd;
	CKSThread ksthread;
	
	CThreadInfo* pThreadInfo;
	TypeMsgBindSnd* pLogonData;

	int nThreadCount = 0;
	listPosition pos;
	listPosition posPrev;
	


	EXEC SQL BEGIN DECLARE SECTION;
		sql_context db[MAX_DB_CONNECTION];
	EXEC SQL END DECLARE SECTION;

	if ( ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0 ) 
	{
		perror("ml_sub_init Error.");
		ml_sub_end();
		exit(1);
	}

	ret = configParse(argv[1]);
	if( ret != 0 )
	{
		exit(1);
	}
    
	log_history(0,0,"[INF] conf senderDBName - [%s]",gConf.senderDBName);
    
	sprintf(logMsg,"[INF] start [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);

	Init_Server();
    
	EXEC SQL ENABLE THREADS;
    
	for(i=0;i<MAX_DB_CONNECTION;i++)
	{
		EXEC SQL CONTEXT ALLOCATE :db[i];

		Init_Oracle(db[i]);

		if( sqlca.sqlcode !=0 )
		{
			sprintf(logMsg,"[ERR] db connect failed [%d]",sqlca.sqlcode);
			monitoring(logMsg,0,errno);
			ml_sub_end();
    
			return -1;
		}
    
		log_history(0,0,"[INF] db sql_context - value[%x]",db[i]);
		dbConnQ.push(db[i]);
	}
    
	sem_init(&m_sem,0,1);
    
	ret = svrSockfd.createDomainNon(gConf.senderDBName);
    
	if( ret !=0 ) 
	{
		log_history(0,0,"[ERR] socket_domain create failed - errno[%s]",strerror(errno),gConf.senderDBName);
		goto ENDMAIN;
	}
    
	while(activeProcess)
	{
		wait_a_moment(100);
		pos = listThreadHandle.begin();
		nThreadCount = 0;
    
		while( pos != listThreadHandle.end() )
		{
			nThreadCount++;
			posPrev = pos++;
    
			if( (*posPrev)->tid <= 0 )
			{
				log_history(0,0,"[INF] thread - tid [%d]",(*posPrev)->tid);
				listThreadHandle.erase(posPrev);
				continue;
			}
    
			ret =  pthread_kill((*posPrev)->tid,0);
    
			switch (ret) {
				case 0 : /* thread is alive */
					break;
				case ESRCH:
				case EINVAL:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				default:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
			}
		}
    
		hNewSocket = svrSockfd.accept();
    
		if( hNewSocket <= 0 )
		{
			continue;
		}
		pThreadInfo = NULL;
		pThreadInfo = new CThreadInfo;
		if( pThreadInfo == NULL )
		{
			log_history(0,0,"[ERR] thread object create failed - [%s]",strerror(errno));
			close(hNewSocket);
			continue;
		}

		pThreadInfo->sock = hNewSocket;
    
		ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
		if( ret != 0 )
		{
			log_history(0,0,"[ERR] thread create failed - [%s]",strerror(errno));
			close(pThreadInfo->sock);
			delete pThreadInfo;
			continue;
		}
    
		listThreadHandle.push_back( pThreadInfo);

	}

ENDMAIN:

	svrSockfd.close();

	pos = listThreadHandle.begin();
	nThreadCount = 0;
	while( pos != listThreadHandle.end() )
	{
		nThreadCount++;
		posPrev = pos++;
    
		ret =  pthread_kill((*posPrev)->tid,0);

		switch (ret) 
		{
			case 0 : /* thread is alive */
				log_history(0,0,"[INF] thread closing - listThreadHandle[%d]",(pthread_t)(*posPrev));
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			case ESRCH:
			case EINVAL:
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			default:
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
		}
	}
	
	sem_destroy(&m_sem);

	EXEC SQL BEGIN DECLARE SECTION;
    
		sql_context pDB;
    
	EXEC SQL END DECLARE SECTION;

	sleep(2);
    
	while( dbConnQ.size()>0 )
	{
		pDB = dbConnQ.front();

		if( pDB == NULL )
		{
			break;
		}

		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL COMMIT WORK RELEASE;
		EXEC SQL CONTEXT FREE :pDB;
		dbConnQ.pop();
	}

	sprintf(logMsg,"[INF] close [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);

	ml_sub_end();

	return 0;
}


void* doService(void* param)
{
	int ret;
	void* pDB;
	bool bFirst=true;
	char buff[SOCKET_BUFF];
	struct sqlca sqlca;
	
	CKSSocket newSockfd;
	CThreadInfo* info = (CThreadInfo*)param;
	CSenderDbInfoAck ack;

	time_t ThisT,LastT;

    /* classify */
	CSenderDbMMSID* pSenderDbMMSID ;
	CSenderDbMMSTBL* pSenderDbMMSTBL ;
	CSenderDbMMSMSG* pSenderDbMMSMSG ;
	CSenderDbMMSCTNTBL* pSenderDbMMSCTNTBL;
	CSenderDbMMSRPTTBL* pSenderDbMMSRPTTBL;
	Header* pHeader ;

	int type ;
	char test[10];

	memset((char*)&ack,0x00,sizeof(ack));

	newSockfd.attach(info->sock);
	memset(buff, 0x00, sizeof(buff)); //CCL(buff);
	ret = newSockfd.rcvmsg(buff);
	if( ret == 0 ) 
	{
		log_history(0,0,"[ERR] socket_domain time out");
		goto ENDDOTHREAD;
	}

	if( ret < 0 ) 
	{
		log_history(0,0,"[ERR] socket_domain recv failed");
		goto ENDDOTHREAD;
	}
//#ifdef DEBUG
    viewPack(buff,ret);
//#endif

	if( memcmp(buff,"getInfo",7) == 0 )
	{
		ret = offerInfo(newSockfd);
		// 20140217 add if
		if( ret < 0 )
		{
			log_history(0,0,"[ERR] socket_domain send failed ret [%d]",ret);
		}
		goto ENDDOTHREAD;
	}

	/* classify */
	pSenderDbMMSID = (CSenderDbMMSID*)buff;
	pSenderDbMMSTBL = (CSenderDbMMSTBL*)buff;
	pSenderDbMMSMSG = (CSenderDbMMSMSG*)buff;
	pSenderDbMMSCTNTBL = (CSenderDbMMSCTNTBL*)buff;
	pSenderDbMMSRPTTBL = (CSenderDbMMSRPTTBL*)buff;
	pHeader = (Header*)buff;
	type = pHeader->type;
	time(&LastT);
	//int type = pSenderDbMMSID->type;
	//int type = (int)buff;
	
	log_history(0,0,"[DBG] doService MMSID[%lld]",pSenderDbMMSID->mmsid);

REGETDB:
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{
			log_history(0,0,"[ERR] semaphore wait failed");
			goto ENDDOTHREAD;
		}
    
	}
	/*   크리티컬 섹션
       값을 가져올때까지 반복하기(세마포어 풀고)  timeout 5 sec
  */
  
	if( dbConnQ.size() > 0 )
	{
		pDB = dbConnQ.front();
		if( pDB != NULL )
		{
			//log_history(0,0,"pop NOT NULL pDB[%x] [%d]", pDB,dbConnQ.size());
			dbConnQ.pop();
		}
	} 
	else 
	{
		pDB = NULL;
	}

	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed [%s]",strerror(errno));
		goto ENDDOTHREAD;
	}

	if( pDB == NULL ) 
	{
		time(&ThisT);
		if( (ret = (int)difftime(ThisT,LastT)) > gConf.dbFindTimeOut )
		{
			log_history(0,0,"[ERR] db handle time out - [%d]sec",gConf.dbFindTimeOut);
			goto ENDDOTHREAD;
		}
		if( ret > 2 && bFirst )
		{
			bFirst = false;
			log_history(0,0,"[INF] timeout [%d]sec",ret);
		}
		wait_a_moment(1000);
		goto REGETDB;
	}

	/* DB 작업 : pDB */
	/*  log_history(0,0,"pDB [%x]",pDB); */
	alarm(15);
	switch( type ) {
		case GETMMSID: /* get MMS ID */
			ret = getMMSIDDB(pDB,sqlca,buff,ack);
			//log_history(0,0,"get MMSIDDB");
			if( ret < 0 )
			{
				log_history(0,0,"[ERR] db get mms id select failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;
		case GETCTNID: /* get CTN ID */
			ret = getCTNIDDB(pDB,sqlca,buff,ack);
			//log_history(0,0,"get CTNIDDB");
			if( ret < 0 )
			{
				log_history(0,0,"[ERR] db get ctn id select failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;
		case SETSENDCTNTBL: /* set MMS CTN TBL */
			ret = setMMSCTNTBL(pDB,sqlca,buff,ack);
			log_history(0,0,"[INF] db set ctn tbl insert - ctnid[%d] ctnseq[%d]",
			pSenderDbMMSCTNTBL->nCtnId,
			pSenderDbMMSCTNTBL->nCtnSeq);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR] db set ctn tbl insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;
		case SETSENDTBL: /* set MMS TBL */
			ret = setMMSTBL(pDB,sqlca,buff,ack);
			log_history(0,0,"[INF] db set mms tbl insert - dstaddr[%s]cid[%s]",
			pSenderDbMMSTBL->szDstAddr,
			pSenderDbMMSTBL->szCid);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR] db set mms tbl insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;
		case SETSENDQUE: /* set MMS MSG */
			ret = setMMSMSG(pDB,sqlca,buff,ack);
			log_history(0,0,"[INF] db set mms msg insert - dstaddr[%s] cid[%s]",
			pSenderDbMMSTBL->szDstAddr,
			pSenderDbMMSTBL->szCid);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR] db set mms msg insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;
		case SETRPTTBL: /* set MMS MSG */
			ret = setRPTTBL(pDB, sqlca, buff, ack);
			log_history(0,0,"[INF] db set mms rpt insert - dstaddr[%s] mmsid[%ld]",
			pSenderDbMMSRPTTBL->szDstAddr,
			pSenderDbMMSRPTTBL->nMMSId);

			if( ret < 0 )
			{
				log_history(0,0,"[ERR] db set mms rpt insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}

			break;
		default: /* etc - error */
			break;
	}
	alarm(0);

	sprintf(ack.szResult, "%02d", ret);

	ret = newSockfd.send((char*)&ack,sizeof(ack));
	if( ret == sizeof(ack))
	{
		/* commit */
		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL COMMIT;

	} 
	else 
	{
		/* rollback */
		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL ROLLBACK;
		log_history(0,0,"[ERR] socket_domain send ack failed ",strerror(errno));
	}


	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{
			log_history(0,0,"[ERR] semaphore wait failed");
			goto ENDDOTHREAD;
		}
  }
    /* 크리티컬 섹션 */
	dbConnQ.push(pDB);

	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed - [%s]",strerror(errno));
		goto ENDDOTHREAD;
	}

ENDDOTHREAD:
	newSockfd.close();
	/* delete info; */
	return NULL;
}

int setMMSMSG(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSMSG* data = (CSenderDbMMSMSG*)buff;
	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		int nSqlCode = -999;
	
		char szQName[32+1];
		int nQNum;
		int nPriority;
		int nCtnId;
		char szCallBack[16+1];
		char szDstAddr[16+1];
		char szMsgTitle[100+1];
		int nCntType;
		char szTxtPath[256+1];
		int nRgnRate;
		int nInterval;
		long long nMMSId = 0;
		
		char cMMSId[20];

	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg	,0x00,sizeof(szSqlErrorMsg	));					//CCL(szSqlErrorMsg);
	memset(szQName			,0x00,sizeof(szQName		));					//CCL(szQName);
	memset(szCallBack		,0x00,sizeof(szCallBack		));					//CCL(szCallBack);
	memset(szDstAddr		,0x00,sizeof(szDstAddr		));					//CCL(szDstAddr);
	memset(szMsgTitle		,0x00,sizeof(szMsgTitle		));					//CCL(szMsgTitle);
	memset(szTxtPath		,0x00,sizeof(szTxtPath		));					//CCL(szTxtPath);
	memset(cMMSId				,0x00,sizeof(cMMSId				));	

	nQNum=1;
	//strcpy(szQName,data->szQName);
	nQNum = atoi(data->szQName);
	nPriority = data->nPriority;
	nCtnId = data->nCtnId;
	strcpy(szCallBack,data->szCallBack);
	strcpy(szDstAddr,data->szDstAddr);
	strcpy(szMsgTitle,data->szMsgTitle);
	nCntType = data->nCntType;
	strcpy(szTxtPath,data->szTxtPath);
	nRgnRate = data->nRgnRate;
	nInterval = data->nInterval;
//	TEST
//	nMMSId = 24183456814140ll;
	
	sprintf(cMMSId,"%lld",data->nMMSId);
	
	//nMMSId = ;
	log_history(0,0,"[DBGAAA] setMMSMSG MMSID[%lld]",nMMSId);
	
	
	
#ifdef TIME
    /* gettimeofday */
    struct timeval timefirst, timesecond;
    struct timezone tzp;
    int secBuf, microsecBuf;
    float timeBuf;
	gettimeofday(&timefirst,&tzp);
    /* gettimeofday */
#endif

	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_msg2(in_q_num =>:nQNum
												,in_priority =>:nPriority
												,in_ctn_id =>:nCtnId
												,in_callback =>:szCallBack
												,in_dst_addr =>:szDstAddr
												,in_msgtitle =>:szMsgTitle
												,in_cnt_type =>:nCntType
												,in_txt_path =>:szTxtPath
												,in_rgn_rate =>:nRgnRate
												,in_interval => :nInterval
												,in_mms_id => :cMMSId
												,ot_sqlcode =>:nSqlCode
												,ot_sqlmsg =>:szSqlErrorMsg
												);

		END;
	END-EXEC;
	//log_history(0,0,"-proc_set_mms_msg2-[%lld][%d][%s]",nMMSId,nSqlCode,"");
	log_history(0,0,"[INF] db call proc_set_mms_msg2 - CallBack[%s]DstAddr[%s]TxtPath[%s]",szCallBack, szDstAddr, szTxtPath);

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call proc_set_mms_msg2 exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call proc_set_mms_msg2 invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	
	ack.mmsid = nMMSId;
	
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSMSG proc_set_mms_msg2 time [%f]",timeBuf);
#endif

    return 55;
}


int setMMSCTNTBL(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSCTNTBL* data = (CSenderDbMMSCTNTBL*)buff;
	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		int nSqlCode = -999;
		int nCtnId=0;
		char szCtnName[50+1];
		char szCtnMime[50+1];
		int nCtnSeq=0;
		char szCtnSvc[5+1];

	EXEC SQL END DECLARE SECTION;
	
	memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));			//CCL(szSqlErrorMsg);
	memset(szCtnName		,0x00,sizeof(szCtnName		));							//CCL(szCtnName);
	memset(szCtnMime		,0x00,sizeof(szCtnMime		));							//CCL(szCtnMime);
	memset(szCtnSvc			,0x00,sizeof(szCtnSvc			));								//CCL(szCtnSvc);

	memcpy(szCtnName,data->szCtnName,sizeof(data->szCtnName));
	memcpy(szCtnMime,data->szCtnMime,sizeof(data->szCtnMime));
	memcpy(szCtnSvc,data->szCtnSvc,sizeof(data->szCtnSvc));
	
	nCtnId = data->nCtnId;
	nCtnSeq = data->nCtnSeq;

#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif

	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_ctn(in_cid => :nCtnId
											,in_ctn_seq => :nCtnSeq
											,in_ctn_name => :szCtnName
											,in_ctn_mime => :szCtnMime
											,in_ctn_svc => :szCtnSvc
											,ot_sqlcode =>:nSqlCode
											,ot_sqlmsg =>:szSqlErrorMsg
											);
		END;
	END-EXEC;
	/* here~ */
	log_history(0,0,"-proc_set_mms_ctn-[%d][%d] [%s][%s][%s] [%d][%s]",nCtnId,nCtnSeq,szCtnName,szCtnMime,szCtnSvc,nSqlCode,"");

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call proc_set_mms_ctn exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call proc_set_mms_ctn invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	//ack.mmsid = nMMSId;
	
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSCTNTBL proc_set_mms_ctn time [%f]",timeBuf);
#endif

	return 55;
}


int setMMSTBL(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSTBL* data = (CSenderDbMMSTBL*)buff;

	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		int nSqlCode = -999;

		char szDstAddr[16+1];
		char szCallBack[16+1];
		char szMsgTitle[200+1];
		char szPtnSn[16+1];
		char szResvData[200+1];
		char szCid[10+1];
		int nMsgType = 0;
		int nPriority = 0;
		int nCtnId = 0;
		int nCtnType = 0;
		int nRgnRate = 0;
		int nInterval = 0;
		int nTextCnt = 0;
		int nImgCnt = 0;
		int nAugCnt = 0;
		int nMpCnt = 0;
		long long nMMSId = 0;
		char cMMSId[20];

	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg ,0x00, sizeof(szSqlErrorMsg ));   //CCL(szSqlErrorMsg);
	memset(szCid         ,0x00, sizeof(szCid         ));   //CCL(szCid);        
	memset(szDstAddr     ,0x00, sizeof(szDstAddr     ));   //CCL(szDstAddr);    
	memset(szCallBack    ,0x00, sizeof(szCallBack    ));   //CCL(szCallBack);   
	memset(szMsgTitle    ,0x00, sizeof(szMsgTitle    ));   //CCL(szMsgTitle);   
	memset(szPtnSn       ,0x00, sizeof(szPtnSn       ));   //CCL(szPtnSn);      
	memset(szResvData    ,0x00, sizeof(szResvData    ));   //CCL(szResvData);   
	memset(szCid         ,0x00, sizeof(szCid         ));   //CCL(szCid);        
	memset(cMMSId				 ,0x00, sizeof(cMMSId				 ));
	
	strcpy(szDstAddr,data->szDstAddr);
	strcpy(szCallBack,data->szCallBack);
	strcpy(szMsgTitle,data->szMsgTitle);
	strcpy(szPtnSn,data->szPtnSn);
	strcpy(szResvData,data->szResvData);
	memcpy(szCid,data->szCid,10);
	
	sprintf(cMMSId,"%lld",data->nMMSId);
	
	nMsgType 	= data->nMsgType;
	nPriority 	= data->nPriority;
	nCtnId 		= data->nCtnId;
	nCtnType 	= data->nCtnType;
	nRgnRate 	= data->nRgnRate;
	nInterval 	= data->nInterval;
	nTextCnt 	= data->nTextCnt;
	nImgCnt 	= data->nImgCnt;
	nAugCnt 	= data->nAugCnt;
	nMpCnt 		= data->nMpCnt;
	nMMSId 		= data->nMMSId;
	
	
	log_history(0,0,"[DBGBBB] setMMSTBL1 MMSID[%lld]",data->nMMSId);
	log_history(0,0,"[DBGBBB] setMMSTBL2 MMSID[%lld]",nMMSId);
	log_history(0,0,"[DBGBBB] setMMSTBL3 MMSID[%s]",cMMSId);

//	log_history(0,0,"[DBG] MMSID[%d]",data->nMMSId);

#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_tbl(in_dstaddr =>:szDstAddr
											,in_callback =>:szCallBack
											,in_msgtitle =>:szMsgTitle
											,in_ptn_sn =>:szPtnSn
											,in_resv_data =>:szResvData
											,in_cid =>:szCid
											,in_msg_type =>:nMsgType
											,in_priority =>:nPriority
											,in_ctn_id =>:nCtnId
											,in_ctn_type =>:nCtnType
											,in_rgn_rate =>:nRgnRate
											,in_interval => :nInterval
											,in_text_cnt => :nTextCnt
											,in_img_cnt => :nImgCnt
											,in_aud_cnt => :nAugCnt
											,in_mp_cnt => :nMpCnt
											,in_mms_id => :cMMSId
											,ot_sqlcode =>:nSqlCode
											,ot_sqlmsg =>:szSqlErrorMsg
               				);
		END;
	END-EXEC;

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call proc_set_mms_tbl exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call proc_set_mms_tbl invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	
	ack.mmsid = nMMSId;
    
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSTBL proc_set_mms_tbl time [%f]",timeBuf);
#endif
	return 55;
}


int getCTNIDDB(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSID* data = (CSenderDbMMSID*)buff;
	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		char szCid[10+1];
		long long nMMSID = 0;
		int nCTNID = 0;
		int nSqlCode = -999;


	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));		//CCL(szSqlErrorMsg);
	memset(szCid        ,0x00,sizeof(szCid        ));		//CCL(szCid);
	
	memcpy(szCid,data->szCid,10);
	
#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_get_ctn_id(in_cid =>:szCid
										,ot_cnt_id =>:nCTNID
										,ot_sqlcode =>:nSqlCode
										,ot_sqlmsg =>:szSqlErrorMsg
               							);
		END;
	END-EXEC;
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call proc_get_mms_id exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call proc_get_mms_id invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	
	ack.ctnid = nCTNID;
	
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"getCTNIDDB proc_get_ctn_id time [%f]",timeBuf);
#endif
	return 55;
}

int getMMSIDDB(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSID* data = (CSenderDbMMSID*)buff;
	
	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		char szCid[10+1];
		long long nMMSID = 0;
		int nCTNID = 0;
		int nSqlCode = -999;

	EXEC SQL END DECLARE SECTION;
	
	memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));		//CCL(szSqlEr
	memset(szCid        ,0x00,sizeof(szCid        ));		//CCL(szCid);

	memcpy(szCid,data->szCid,10);
    
#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif

	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_get_mms_id(in_cid =>:szCid
								,ot_mms_id =>:nMMSID
								,ot_sqlcode =>:nSqlCode
								,ot_sqlmsg =>:szSqlErrorMsg
								);
		END;
	END-EXEC;

	log_history(0,0,"[DBG] setMMSTBL MMSID[%lld]",nMMSID);

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call proc_get_mms_id exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call proc_get_mms_id invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	ack.mmsid = nMMSID;

#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"getMMSIDDB proc_get_mms_id time [%f]",timeBuf);
#endif

	return 55;
}




void Init_Oracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;

		VARCHAR Username[10];
		VARCHAR Password[10];
		VARCHAR dbstring[10];

	EXEC SQL END DECLARE SECTION;

	strcpy((char*)Username.arr, gConf.dbID);
	Username.len = strlen((char*)Username.arr);
	strcpy((char*)Password.arr, gConf.dbPASS);
	Password.len = strlen((char*)Password.arr);
	strcpy((char*)dbstring.arr, gConf.dbSID);
	dbstring.len = strlen((char*)dbstring.arr);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
}


int errorDBprocess(void* pDB)
{
	CKSThread dbErr;
	int ret;
	pthread_t tid;

	ret = dbErr.create(&(tid),NULL,doDBError,(void*)pDB);
	if( ret != 0 )
	{
		log_history(0,0,"[ERR] thread dberr process create failed - errno[%s] DBqueSize[%d]",strerror(errno),dbConnQ.size());
	}
	return -1;
}


void* doDBError(void* param)
{
	pthread_detach(pthread_self());
	struct sqlca sqlca;

	EXEC SQL BEGIN DECLARE SECTION;

		sql_context pDB = (sql_context)param;

	EXEC SQL END DECLARE SECTION;

DBERRCONN:

	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL COMMIT WORK RELEASE;
	EXEC SQL CONTEXT FREE :pDB;

	if( activeProcess == false )
	{
		return NULL;
	}

	EXEC SQL CONTEXT ALLOCATE :pDB;

	Init_Oracle(pDB);
    
	if( sqlca.sqlcode !=0 )
	{
		monitoring("[INF] db connection - failed",0,errno);
		wait_a_moment(900000);
		goto DBERRCONN;
	}

	log_history(0,0,"[INF] db sql_context - [%x]",pDB);
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{
			log_history(0,0,"[ERR] semaphore wait failed");
			wait_a_moment(900000);
			goto DBERRCONN;
		}
	}
	/* 크리티컬 섹션 */
	dbConnQ.push(pDB);

	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed - errno[%s]pDB[%x]",strerror(errno),pDB);
		return NULL;
	}

	log_history(0,0,"[INF] db connection - current queSize[%d]",dbConnQ.size());

	return NULL;
}

int getTelcoId(char* szTelco,char* szDstaddr)
{
	char* p;
	int telcoArray[7];
	char CID[4];
	int i=0;
	memset(telcoArray,0x00,sizeof(telcoArray));
	memset(CID,0x00,sizeof(CID));	//CCL(CID);

	p = strtok(szTelco,"|");
	if( p == NULL )
	{
		return gConf.telcoDefaultID;
	}
	telcoArray[0] = atoi(p);

	while(p = strtok(NULL,"|") )
	{
		telcoArray[++i]= atoi(p);
		if( i > 6 )
		{
			break;
		}
	}

	memcpy(CID,szDstaddr,3);

	switch(atoi(CID))
	{
		case 11 :
			return telcoArray[0];
		case 16 :
			return telcoArray[1];
		case 17 :
			return telcoArray[2];
		case 18 :
			return telcoArray[3];
		case 19 :
			return telcoArray[4];
		case 10 :
			return telcoArray[5];
		case 50 :
			return telcoArray[6];
	}

	return gConf.telcoDefaultID;
}

int offerInfo(CKSSocket& newSockfd)
{
	char szQueSize[4];
	memset(szQueSize,0x00,sizeof(szQueSize));//  CCL(szQueSize);
	sprintf(szQueSize,"%d",dbConnQ.size());

//	return mod
//	newSockfd.send(szQueSize,strlen(szQueSize));
//	return 0;
	return newSockfd.send(szQueSize,strlen(szQueSize));
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;

	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}
	conf.strncpy2(gConf.senderDBName , conf.FetchEntry("domain.senderdb"),64);
	if( gConf.senderDBName == NULL )
	{
		strcpy(gConf.senderDBName,"");
	}

	gConf.telcoDefaultID = conf.FetchEntryInt("telco.defaultid");
	gConf.dbFindTimeOut = conf.FetchEntryInt("db.findtimeout");
	
	if( gConf.dbFindTimeOut <= 0 )
	{
		gConf.dbFindTimeOut = 2;
	}

	conf.strncpy2(gConf.dbID , conf.FetchEntry("db.id"),16);
	if( gConf.dbID == NULL )
	{
		strcpy(gConf.dbID,"");
	}

	conf.strncpy2(gConf.dbPASS , conf.FetchEntry("db.pass"),16);
	if( gConf.dbPASS == NULL )
	{
		strcpy(gConf.dbPASS,"");
	}

	conf.strncpy2(gConf.dbSID , conf.FetchEntry("db.sid"),16);
	if( gConf.dbSID == NULL )
	{
		strcpy(gConf.dbSID,"");
	}

	return 0;
}

int setRPTTBL(sql_context pDB, struct sqlca sqlca, char* buff, CSenderDbInfoAck& ack)
{
	CSenderDbMMSRPTTBL* data = (CSenderDbMMSRPTTBL*)buff;

	EXEC SQL BEGIN DECLARE SECTION;
	
	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	long long mms_id;
	char msg_id[4096] = {0x00,};
	char snd_numb[4096] = {0x00,};
	char rcv_numb[4096] = {0x00,};
	int res_code;
	char res_text[4096] = {0x00,};
	int telco_id;
	int res_type;
	char end_telco[4096] = {0x00,};

	EXEC SQL END DECLARE SECTION;

	//초기화
	mms_id = 0;
	memset(msg_id, 0x00, sizeof(msg_id));   
	memset(snd_numb, 0x00, sizeof(snd_numb));
	memset(rcv_numb, 0x00, sizeof(rcv_numb));
	res_code = 0;
	memset(res_text, 0x00, sizeof(res_text));
	telco_id = 0;
	res_type = 0;
	memset(end_telco, 0x00, sizeof(end_telco));

	//값 세팅	
	mms_id = data->nMMSId;
	sprintf(msg_id, "%ld", data->nMMSId);
	sprintf(snd_numb, data->szCallBack);
	sprintf(rcv_numb, data->szDstAddr);
	res_code = data->res_code;
	sprintf(res_text, data->res_text);
	telco_id = 0;

#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
       	PROC_SET_RPT_FORCE(:mms_id, :msg_id, :snd_numb, :rcv_numb, :res_code, 
							:res_text, :telco_id, :res_type, :end_telco, :nSqlCode, :szSqlErrorMsg);
		END;
	END-EXEC;

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call PROC_SET_RPT_FORCE exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call PROC_SET_RPT_FORCE invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	
	ack.mmsid = data->nMMSId;
    
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSTBL proc_set_mms_tbl time [%f]",timeBuf);
#endif
	return 55;
}



