#include "logonUtil.h"

void CLogonUtil::displayLogonDbInfo(const CLogonDbInfo logonDbInfo,char* filename)
{
    char buff[1024];
    sprintf(buff,"logonDbInfo[%s][%s][%s][%d][%d][%d][%d][%d][%d]",
            logonDbInfo.szCID,
            logonDbInfo.szSIP,
            logonDbInfo.szAPPName,
            logonDbInfo.nmPID,
            logonDbInfo.nmJOB,
            logonDbInfo.nmPRT,
            logonDbInfo.nmCNT,
            logonDbInfo.nUrlJob,
            logonDbInfo.nRptWait
    );




    _monPrint(filename,buff);

}

/** @brief smsPacketStruct 에 있는 구조체를 헤더 & 바디 나눠서 read 
 */
int CLogonUtil::recvPacket(CKSSocket& hRemoteSock,char* buff,int sec,int usec)
{
    int ret; /** return code */
    int recvLen=0; /** 받은 recv length */
    int nHead=0; /** 받아야 하는 Head length */
    int nBody=0; /* 받아야 하는 recv Body length */
    int nRecvBody=0; /* 받은  recv Body length */


    ret = hRemoteSock.select(sec,usec);
    if( ret > 0)
    {
        nHead = sizeof(TypeHeader);
        recvLen = hRemoteSock.recv(buff,nHead); 
        if( recvLen == 0 )
        {
            sprintf(m_ErrorMsg,"H:Socket Error [close by peer][%d/%d]\n",
                    recvLen,
                    nHead);
            return -1;

        }
        if( recvLen < 0 )
        {
            sprintf(m_ErrorMsg,"H:Socket Error [%s][%d][%d]\n",
                    strerror(errno),
                    recvLen,
                    nHead);
            return -1;

        }

        if( recvLen != sizeof(TypeHeader) )
        {
            recvLen += hRemoteSock.recv(buff,nHead-recvLen);
            if( recvLen != nHead )
            {
                sprintf(m_ErrorMsg,"H:Socket Error [%s][%d/%d]\n",
                        strerror(errno),
                        recvLen,
                        nHead);
                viewPack(buff,recvLen);
                return -1;

            }

        }

        recvLen=0;
        char szBodyLen[8];
        int nErrCnt=0;

        TypeHeader* pHeader = (TypeHeader*)buff;
        CCL(szBodyLen);
        memcpy(szBodyLen,pHeader->msgLeng,sizeof(pHeader->msgLeng));
        nBody = atoi(szBodyLen);
REREAD:
        recvLen = hRemoteSock.recv(buff+nHead+nRecvBody,nBody-nRecvBody); 
        nRecvBody += recvLen;
        if( recvLen == 0 )
        {
            sprintf(m_ErrorMsg,"B:Socket Error [close by peer][%d/%d]\n",
                    recvLen,
                    nBody-nRecvBody);
            return -1;
        }

        if( recvLen < 0 )
        {
            sprintf(m_ErrorMsg,"B:Socket Error --  [%s]ret[%d][%d]\n",
                    strerror(errno),
                    recvLen,
                    nBody);

            viewPackPrint(buff,nBody+nHead);
            printf("[%s]\n",buff);
            fflush(stdout);
            
			return -1;
        }

        if( recvLen != nBody )
        {
            if( nErrCnt == 0 )
            {
                sprintf(m_ErrorMsg,"B:Socket re read [%s][%d/%d]\n",
                        strerror(errno),
                        recvLen,
                        nBody-nRecvBody);
                nErrCnt++;
                goto REREAD;

            }
            sprintf(m_ErrorMsg,"B:Socket Error [%s][%d/%d]\n",
                    strerror(errno),
                    recvLen,
                    nBody-nRecvBody);
            viewPack(buff,nBody-nRecvBody);

            return -1;
        }

        //      viewPack(buff,recvLen);
    } else if( ret < 0 ) {
        sprintf(m_ErrorMsg,"Socket select Error [%s]ret[%d]\n",strerror(errno),ret);
        recvLen = -1;
    }

    return nHead+nBody;

}





char* CLogonUtil::getErrorMsg()
{
    return m_ErrorMsg;
}


char* CLogonUtil::findValueParse(char* szOrg, char* szTag, char* szVal)
{
    char* pStart;
    char* pEnd;
    if ((pStart=strstr(szOrg,szTag))) 
	{
        pStart=pStart+strlen(szTag)+1;
        pEnd=strstr(pStart,"&");
        memcpy(szVal,pStart,pEnd-pStart);
    }
    return szVal;
}

