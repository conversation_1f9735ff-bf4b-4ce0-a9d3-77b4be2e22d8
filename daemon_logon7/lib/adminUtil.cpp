#include "adminUtil.h"
#include <iostream>
using namespace std;

int CAdminUtil::createDomainID(char*id,char classify, char* path,int domainDupMaxCount)
{
    // create doamin ID
    char szDomainName[256];
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];
    int  cnt=0;


RETRY:

    memset(szDomainName,0x00,sizeof(szDomainName));
    if( cnt == 0 ) 
	{
        sprintf(szDomainName,"%s/ID_%c_%s",
                path,
                classify,
                id
               );
    } 
	else 
	{
        sprintf(szDomainName,"%s/ID_%c_%s%d",
                path,
                classify,
                id,
                cnt
               );
    }


    printf("domain [%s]\n",szDomainName);
    fflush(stdout);

    /* connect check */
    ret = conn.connectDomain(szDomainName);
    if( ret == 0 )
    {
        // 기존 프로세스가 있다~

       // Get MaxConnection,
       // Check dup if dup 이면 conn 에 종료 command 후 conn.close 할수 있게 한다. 
       // if 추가 연결일경우 MaxConnection까지 숫자 증가 하면서 접속 시도 
       // if MaxConnection 초과시 return -1;

        if( domainDupMaxCount < 2 ) 
		{
            // 중복 접속이 안되는 경우 기존 프로세스 종료
			printf("기존현재 도메인 중복제한  [%s]\n",szDomainName);
			fflush(stdout);

            memset(buff,0x00,sizeof(buff));
            sprintf(buff,"%d    ",3 );
            ret = conn.send(buff,strlen(buff));
            if( ret != strlen(buff) )
            {
                conn.close();
                return -1;
            }

            memset(buff,0x00,sizeof(buff));
            ret = conn.rcvmsg(buff);
            if( ret <=0 )
            {
                conn.close();
                return -1;
            }

            conn.close();
            remove(szDomainName);

        } 
		else 
		{
            // 다음 가능 한지 체크
			printf("현재 도메인 사용중임 다음 도메인 체크    [%s]\n",szDomainName);
			fflush(stdout);

            cnt++;
            conn.close();
            if( cnt >= domainDupMaxCount )
                return -1;

            goto RETRY;
        }
    } 
	else 
	{
		printf("없음 새로 만들기  [%s]\n",szDomainName);
		fflush(stdout);

        conn.close();
    }


    ret = m_svrSock.createDomainNon(szDomainName);
    if( ret != 0 )
        return -1;
    
	return cnt;
}



int CAdminUtil::createDomainID(char*id,char classify, char* path)
{
    // create doamin ID
    char szDomainName[256];
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];

    memset(szDomainName,0x00,sizeof(szDomainName));
    sprintf(szDomainName,"%s/ID_%c_%s",
            path,
            classify,
            id
            );
    /* connect check */
    ret = conn.connectDomain(szDomainName);
    if( ret == 0 )
    {
        memset(buff,0x00,sizeof(buff));
        sprintf(buff,"%d    ",3 );
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            conn.close();
            return -1;
        }

        memset(buff,0x00,sizeof(buff));
        ret = conn.rcvmsg(buff);
        if( ret <=0 )
        {
            conn.close();
            return -1;
        }
        conn.close();
        remove(szDomainName);

    }
 

    ret = m_svrSock.createDomainNon(szDomainName);
    return ret;
}


int CAdminUtil::deleteDomainID(char*id,char classify, char* path)
{
    // create doamin ID
    char szDomainName[256];
    int ret;

    memset(szDomainName,0x00,sizeof(szDomainName));
    sprintf(szDomainName,"%s/ID_%c_%s",
            path,
            classify,
            id
            );
    ret = remove(szDomainName);
   
   	return ret;
}



char* CAdminUtil::getErrMsg()
{
    return szErrMsg;
}

int CAdminUtil::checkPacket(CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo,int sum)
{
    // check admin packet
    int ret;
    int nType = 0;
    CAdminInfo* pAdminInfo;
    CLogonDbInfo* pLogonDbInfo;
    CKSSocket conn;

    m_hNewSocket = m_svrSock.accept2();
    if( m_hNewSocket == 0 )
    {
        return 0;
    }

    if( m_hNewSocket > 0 )
    {
        m_newSock.attach(m_hNewSocket);
        memset(buff,0x00,sizeof(buff));
        ret = m_newSock.rcvmsg(buff);
        if( ret <= 0 )
        {
            memset(szErrMsg,0x00,sizeof(szErrMsg));
            sprintf(szErrMsg,"Domain recv Error[%s]",strerror(errno)); 
            m_newSock.close();
            return 0;
        }

        pAdminInfo = (CAdminInfo*)buff;
        nType =  atoi(pAdminInfo->szType);
        switch( nType ) 
		{
            case 1: // display
                memset(buff,0x00,sizeof(buff));
                memset(&m_adminInfo,0x00,sizeof(m_adminInfo));
                strcpy(m_adminInfo.szType,"2"); // display ack
                memcpy(&(m_adminInfo.logonDbInfo),&logonDbInfo,sizeof(logonDbInfo));
                sprintf(processInfo.dataCnt,"%d",sum); // data sum
                memcpy(&(m_adminInfo.processInfo),&processInfo,sizeof(processInfo));
                strcpy(m_adminInfo.szResult,"OK");
                memcpy(buff,&m_adminInfo,sizeof(m_adminInfo));

                ret = m_newSock.send(buff,sizeof(m_adminInfo));
                if( ret != sizeof(m_adminInfo))
                {
                    sprintf(szErrMsg,"Domain send Error [%s][%d]",strerror(errno),ret);
                    nType = -1;
                }
                break;
            case 3: // end
                memset(buff,0x00,sizeof(buff));
                memset(&m_adminInfo,0x00,sizeof(m_adminInfo));
                strcpy(m_adminInfo.szType,"4"); // end ack
                memcpy(&(m_adminInfo.logonDbInfo),&logonDbInfo,sizeof(logonDbInfo));
                memcpy(&(m_adminInfo.processInfo),&processInfo,sizeof(processInfo));
                strcpy(m_adminInfo.szResult,"OK");
                memcpy(buff,&m_adminInfo,sizeof(m_adminInfo));

                ret = m_newSock.send(buff,sizeof(m_adminInfo));
                if( ret != sizeof(m_adminInfo))
                {
                    sprintf(szErrMsg,"Domain send Error [%s][%d]",strerror(errno),ret);
                    nType = -1;
                }

                break;
            case 5: // info modify
			case 7:
				
                TypeMsgBindSnd Send;
                TypeMsgBindAck ack;
                int nRecvLen;
                memset(&Send,0x00,sizeof(Send));
                memset(&ack,0x00,sizeof(ack));

                strcpy(Send.szCID,logonDbInfo.szCID);
                strcpy(Send.szPWD,logonDbInfo.szPWD);

				printf("Send.szCID:%s\n",Send.szCID);
				printf("Send.szPWD:%s\n",Send.szPWD);

                ret = conn.connectDomain(processInfo.logonDBName);
                if( ret != 0 )
                {
                    sprintf(szErrMsg,"LogonDB conn Error",strerror(ret));
                    return -1;
                }

                ret = conn.send((char*)&Send,sizeof(Send));
                if( ret != sizeof(Send) )
                {
                    sprintf(szErrMsg,"LogonDB send Error",strerror(ret));
                    conn.close();
                    return -1;
                }


                nRecvLen = conn.recv((char*)&ack,sizeof(ack));
                if( nRecvLen == 0 ) 
				{
                    sprintf(szErrMsg,"Close by Logon Domain(%s)",strerror(ret));
                    conn.close();
                    return -1;
                }

                if( nRecvLen < 0 ) 
				{
                    sprintf(szErrMsg,"Logon Domain Socket Recv Error(%s)",strerror(ret));
                    conn.close();
                    return -1;
                }

                if( memcmp(ack.szResult,"00",2) != 0 )
                {
                    sprintf(szErrMsg,"Logon Error ret[%.2s]",ack.szResult);
                    conn.close();
                    return -1;
                }


                conn.send("OK",2);


                memset(buff,0x00,SOCKET_BUFF);
                nRecvLen = conn.recv(buff,SOCKET_BUFF);
                if( nRecvLen == 0 ) 
				{
                    sprintf(szErrMsg,"Close by Logon Domain(%s)",strerror(ret));
                    conn.close();
                    return -1;
                }

                if( nRecvLen < 0 ) 
				{
                    sprintf(szErrMsg,"Logon Domain Socket Recv Error(%s)",strerror(ret));
                    conn.close();
                    return -1;
                }

/*                CLogonDbInfo* pLogonDbInfo = (CLogonDbInfo*)buff; */
                pLogonDbInfo = (CLogonDbInfo*)buff;
/*                memcpy(&logonDbInfo,buff,sizeof(CLogonDbInfo));
 */
                memcpy(logonDbInfo.szSIP,pLogonDbInfo->szSIP,sizeof(logonDbInfo.szSIP));
                memcpy(logonDbInfo.szAPPName,pLogonDbInfo->szAPPName,sizeof(logonDbInfo.szAPPName));
                memcpy(logonDbInfo.szServerInfo,pLogonDbInfo->szServerInfo,sizeof(logonDbInfo.szServerInfo));
                memcpy(logonDbInfo.szSenderDBName,pLogonDbInfo->szSenderDBName,sizeof(logonDbInfo.szSenderDBName));
                memcpy(logonDbInfo.szReportDBName,pLogonDbInfo->szReportDBName,sizeof(logonDbInfo.szReportDBName));
                memcpy(logonDbInfo.szLogFilePath,pLogonDbInfo->szLogFilePath,sizeof(logonDbInfo.szLogFilePath));
                memcpy(logonDbInfo.szReserve,pLogonDbInfo->szReserve,sizeof(logonDbInfo.szReserve));
                logonDbInfo.nRptNoDataSleep = pLogonDbInfo->nRptNoDataSleep;
                logonDbInfo.nmPID = pLogonDbInfo->nmPID;
                logonDbInfo.nmJOB = pLogonDbInfo->nmJOB;
                logonDbInfo.nmPRT = pLogonDbInfo->nmPRT;
                logonDbInfo.nmCNT = pLogonDbInfo->nmCNT;
                logonDbInfo.nmRST = pLogonDbInfo->nmRST;
                logonDbInfo.nUrlJob = pLogonDbInfo->nUrlJob;
                logonDbInfo.nRptWait = pLogonDbInfo->nRptWait;
                printf("[%d][%d][%d][%d][%d][%d][%d]\n",
                        pLogonDbInfo->nmPID,
                        pLogonDbInfo->nmJOB,
                        pLogonDbInfo->nmPRT,
                        pLogonDbInfo->nmCNT,
                        pLogonDbInfo->nmRST,
                        pLogonDbInfo->nUrlJob,
                        pLogonDbInfo->nRptWait);

                conn.close();

                break;
            default:
                memset(szErrMsg,0x00,sizeof(szErrMsg));
                sprintf(szErrMsg,"AdminInfo.szType Error Not Found szType[%d]",nType); 
                break;
        }

        m_newSock.close();

    } else {
        printf("-------------[%d][%s]\n",errno,strerror(errno));
        if( m_hNewSocket != -1 )
        {
            printf("----------- to Alert  chozo99 [%d]\n",m_hNewSocket);
            fflush(stdout);
            sleep(1);
        }
    }

    return nType;
}

int CAdminUtil::checkPacket(CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo)
{
    return checkPacket(processInfo,logonDbInfo,0);
}



int CAdminUtil::checkPacket(CLogonDbInfo& logonDbInfo)
{
    // check admin packet
    int ret;
    int nType = 0;
    CAdminInfo* pAdminInfo;
    m_hNewSocket = m_svrSock.accept();
    if( m_hNewSocket > 0 )
    {
        m_newSock.attach(m_hNewSocket);
        memset(buff,0x00,sizeof(buff));
        ret = m_newSock.rcvmsg(buff);
        if( ret <= 0 )
        {
            memset(szErrMsg,0x00,sizeof(szErrMsg));
            sprintf(szErrMsg,"Domain recv Error[%s]",strerror(errno)); 
            m_newSock.close();
            return 0;
        }

        pAdminInfo = (CAdminInfo*)buff;
        nType =  atoi(pAdminInfo->szType);
        switch( nType ) 
		{
            case 1: // display
                memset(buff,0x00,sizeof(buff));
                memset(&m_adminInfo,0x00,sizeof(m_adminInfo));
                strcpy(m_adminInfo.szType,"2"); // display ack
                memcpy(&(m_adminInfo.logonDbInfo),&logonDbInfo,sizeof(logonDbInfo));
                strcpy(m_adminInfo.szResult,"OK");
                memcpy(buff,&m_adminInfo,sizeof(m_adminInfo));

                ret = m_newSock.send(buff,sizeof(m_adminInfo));
                if( ret != sizeof(m_adminInfo))
                {
                    sprintf(szErrMsg,"Domain send Error [%s][%d]",strerror(errno),ret);
                    nType = -1;
                }
                break;
            case 3: // end
                memset(buff,0x00,sizeof(buff));
                memset(&m_adminInfo,0x00,sizeof(m_adminInfo));
                strcpy(m_adminInfo.szType,"4"); // end ack
                memcpy(&(m_adminInfo.logonDbInfo),&logonDbInfo,sizeof(logonDbInfo));
                strcpy(m_adminInfo.szResult,"OK");
                memcpy(buff,&m_adminInfo,sizeof(m_adminInfo));

                ret = m_newSock.send(buff,sizeof(m_adminInfo));
                if( ret != sizeof(m_adminInfo))
                {
                    sprintf(szErrMsg,"Domain send Error [%s][%d]",strerror(errno),ret);
                    nType = -1;
                }

                break;
            default:
                memset(szErrMsg,0x00,sizeof(szErrMsg));
                sprintf(szErrMsg,"AdminInfo.szType Error Not Found szType[%d]",nType); 
                break;
        }

       m_newSock.close();

    }
   	else 
	{
        printf("프로세스 도메인 체크 accept 에러 return [%d]\n",m_hNewSocket);
        fflush(stdout);
    }


    return nType;
}

int CAdminUtil::findFileInit(char* path)
{
    dp = NULL;
    dp=opendir(path); 
    if( dp == NULL )
        return -1;

    seekdir(dp,0);        
    return 0;

}

/** @return 성공시 해당 파일 이름을 리턴하고 실패시 NULL 을 리턴한다. 
*/
char* CAdminUtil::findFileNext()
{
    struct dirent *dirp;  
    struct stat statbuf;  
     while ( (dirp=readdir(dp))!=NULL ) { 
        if ( strcmp(dirp->d_name,".")==0||strcmp(dirp->d_name,"..")==0 ) continue;
/*
        lstat(dirp->d_name,&statbuf);  
        if ( S_ISDIR(statbuf.st_mode) )
            continue; // skip directory 
        else {        
*/
            if( memcmp(dirp->d_name,"ID_",2) == 0 )
            {
                return dirp->d_name;
            }
 //       }     
     } // end while

     return NULL;
}


char* CAdminUtil::findFileNext(char* pattern)
{
    struct dirent *dirp;  
    struct stat statbuf;  
     while ( (dirp=readdir(dp))!=NULL ) { 
        if ( strcmp(dirp->d_name,".")==0||strcmp(dirp->d_name,"..")==0 ) continue;
/*
        lstat(dirp->d_name,&statbuf);  
        if ( S_ISDIR(statbuf.st_mode) )
{
printf("is dir\n");
            continue; // skip directory 
}
        else {        
*/
            if( memcmp(dirp->d_name,pattern,strlen(pattern)) == 0 )
            {

                return dirp->d_name;
            }
 //       }     
     } // end while

     return NULL;
}

int CAdminUtil::findFileClose()
{
    closedir(dp);     
    return 0;
}



