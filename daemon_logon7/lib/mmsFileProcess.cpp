#include "mmsFileProcess.h"
#include "ksbase64.h"
#include <sys/stat.h>
#include <sys/types.h>
#include <regex.h>

/*
2008-10-07 TXT , TXT_LGT 파일 생성하지 않도록 수정함
*/
#define MAX_PACKET_SZ	1024*540

int CMMSFileProcess::write(CKSSocket& db
        									,CMMSPacketSend& mmsPacketSend
        									,char* path
        									,long long mmsid
        									,char* szYYYYMM
        									,char* senderID
        									,int ctnid
        									,int timeout
        									,char* senderDbDomainName
        									,int bBcastFlag
        									)
{
    CMData mData;
    string strData;
    char* pData;
    int size;
    int ret;
    char szType[50+1];
    char szCtnSvc[5+1];
    ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
	{
        return 0;
	}
    bKTFTXT = false;

    clearListMMSCtnTbl();
    memset(szTxtPath,0x00,sizeof(szTxtPath));

    pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(),
            mData.strData.length(), &size);
	
/* 20131122 LSY comment 처리
    printf("decode [%s]------ [%s]\n",
            szYYYYMM,
            pData);
    fflush(stdout);
*/
    memset(szType,0x00,sizeof(szType));
    memset(szCtnSvc,0x00,sizeof(szCtnSvc));

    sprintf(szType,(char*)mData.contentType.strType.c_str());
    sprintf(szCtnSvc,(char*)mData.strSvc.c_str());
    
	/* 20150310
	 * IMG 사이즈 1M 이상일경우 오류 처리
	 */
	if (size > MAX_PACKET_SZ && strcmp(szType, "IMG") == 0)
	{
		return -2;
	}
    classifyMData(path,mmsid,szYYYYMM,pData,size,szType,(char*)mData.contentType.strName.c_str(),senderID,
            ctnid,
            mData.ctnseq,
            db,
            timeout,
            senderDbDomainName,szCtnSvc,
            bBcastFlag);
    free(pData);

    while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
    {
        pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), 
                mData.strData.length(), &size);
			
        memset(szType,0x00,sizeof(szType));
        memset(szCtnSvc,0x00,sizeof(szCtnSvc));

        sprintf(szType,(char*)mData.contentType.strType.c_str());
        sprintf(szCtnSvc,(char*)mData.strSvc.c_str());

		if (size > MAX_PACKET_SZ &&	strcmp(szType, "IMG") == 0)
		{
			return -2;
		}
        classifyMData(path,mmsid,szYYYYMM,(void*)pData,size,szType,
                (char*)mData.contentType.strName.c_str(),senderID,ctnid,
                mData.ctnseq,
                db,
                timeout,
                senderDbDomainName,szCtnSvc,
                bBcastFlag);
        free(pData);
    }

/*
    if( bKTFTXT ) 
	{
        string strParsingData;
        char szFullFileName[512];

        sprintf(szFullFileName,"%d.txt",mmsid);

        ret = parsingKTF(strParsingData,(char*)strTxt.c_str(),strTxt.length());
        createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    senderID);
    }
*/
	// 2013.09.04 이미지만 전송시 임시 TEXT파일을 생성하여, 통신사프로그램에서 오류 발생을 차단함
	if(mmsPacketSend.getTextCnt() == 0 && mmsPacketSend.getImgCnt() >= 1)
	{
       	char szFullFileName[512];
		string strParsingData;
		int ret = 0;
			
       	sprintf(szFullFileName,"%lld.txt",mmsid);
			
//		ret = parsingSKT(strParsingData,(char*)"\n\n",2);
//     	createFile(path,"TXT_SKT",szYYYYMM,szFullFileName,(char*)strParsingData.c_str(),ret,senderID);
       
//     	ret = parsingKTF(strParsingData,(char*)"\n\n",2);
//     	createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,(char*)strParsingData.c_str(),ret,senderID);
      
	   	//20140212 ADD if	
       	if (createFile(path,"TXT_SSN",szYYYYMM,szFullFileName,(char*)" ",1,senderID) < 0)
		{
			return -1;
		}
       
       	sprintf(szTxtPath,"%s/%s",szYYYYMM,szFullFileName);
	}
		
    return 0;
}
int CMMSFileProcess::write(CKSSocket& db
        									,CMMSPacketSend& mmsPacketSend
        									,char* path
        									,int mmsid
        									,char* szYYYYMM
        									,char* senderID
        									,int ctnid
        									,int timeout
        									,char* senderDbDomainName
        									,int bBcastFlag
        									)
{
    CMData mData;
    string strData;
    char* pData;
    int size;
    int ret;
    char szType[50+1];
    char szCtnSvc[5+1];
    ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
	{
        return 0;
	}
    bKTFTXT = false;

    clearListMMSCtnTbl();
    memset(szTxtPath,0x00,sizeof(szTxtPath));

    pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(),
            mData.strData.length(), &size);
/* 20131122 LSY comment 처리
    printf("decode [%s]------ [%s]\n",
            szYYYYMM,
            pData);
    fflush(stdout);
*/
    memset(szType,0x00,sizeof(szType));
    memset(szCtnSvc,0x00,sizeof(szCtnSvc));

    sprintf(szType,(char*)mData.contentType.strType.c_str());
    sprintf(szCtnSvc,(char*)mData.strSvc.c_str());

    classifyMData(path,mmsid,szYYYYMM,pData,size,szType,(char*)mData.contentType.strName.c_str(),senderID,
            ctnid,
            mData.ctnseq,
            db,
            timeout,
            senderDbDomainName,szCtnSvc,
            bBcastFlag);
    free(pData);

    while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
    {
        pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), 
                mData.strData.length(), &size);

        memset(szType,0x00,sizeof(szType));
        memset(szCtnSvc,0x00,sizeof(szCtnSvc));

        sprintf(szType,(char*)mData.contentType.strType.c_str());
        sprintf(szCtnSvc,(char*)mData.strSvc.c_str());

        classifyMData(path,mmsid,szYYYYMM,(void*)pData,size,szType,
                (char*)mData.contentType.strName.c_str(),senderID,ctnid,
                mData.ctnseq,
                db,
                timeout,
                senderDbDomainName,szCtnSvc,
                bBcastFlag);
        free(pData);
    }

/*
    if( bKTFTXT ) 
	{
        string strParsingData;
        char szFullFileName[512];

        sprintf(szFullFileName,"%d.txt",mmsid);

        ret = parsingKTF(strParsingData,(char*)strTxt.c_str(),strTxt.length());
        createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    senderID);
    }
*/
	// 2013.09.04 이미지만 전송시 임시 TEXT파일을 생성하여, 통신사프로그램에서 오류 발생을 차단함
	if(mmsPacketSend.getTextCnt() == 0 && mmsPacketSend.getImgCnt() >= 1)
	{
       	char szFullFileName[512];
		string strParsingData;
		int ret = 0;
			
       	sprintf(szFullFileName,"%d.txt",mmsid);
			
//		ret = parsingSKT(strParsingData,(char*)"\n\n",2);
//     	createFile(path,"TXT_SKT",szYYYYMM,szFullFileName,(char*)strParsingData.c_str(),ret,senderID);
       
//     	ret = parsingKTF(strParsingData,(char*)"\n\n",2);
//     	createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,(char*)strParsingData.c_str(),ret,senderID);
      
	   	//20140212 ADD if	
       	if (createFile(path,"TXT_SSN",szYYYYMM,szFullFileName,(char*)" ",1,senderID) < 0)
		{
			return -1;
		}
       
       	sprintf(szTxtPath,"%s/%s",szYYYYMM,szFullFileName);
	}
		
    return 0;
}

int CMMSFileProcess::classifyMData(char* path,long long mmsid ,char* szYYYYMM,
        void* pData, int size,char* classify,char* szFileName,char* szSenderID,int ctnid,int ctnseq,
        CKSSocket& db,
        int timeout,
        char* senderDbDomainName,
        char* szCtnSvc,
        int bBcastFlag)
{
    int ret;
    char szFullFileName[512];
    CMMSCtnTbl mmsCtnTbl;
    CSenderDbMMSID senderDbMMSID;
    string strParsingData;
    int nParsingLength;

    if( strstr(classify,"TXT")  )
    {
    	/*
		//컬러 문자 유무 체크.2012.12.10.by han
		if ((strstr((char*)pData, "<#Y>") && strstr((char*)pData, "</#Y>")) || 
			(strstr((char*)pData, "<#R>") && strstr((char*)pData, "</#R>")) || 
			(strstr((char*)pData, "<#B>") && strstr((char*)pData, "</#B>")) || 
			(strstr((char*)pData, "<#G>") && strstr((char*)pData, "</#G>")))
		{
			setTxtColorYN(1);
		}
		else
		{
			setTxtColorYN(0);
		}
		//
		*/
		setTxtColorYN(0);
		sprintf(szFullFileName,"%lld.txt",
                mmsid);

        fflush(stdout);

		// 2008-10-07 TXT 파일을 생성하지 않음
        //createFile(path,"TXT",szYYYYMM,szFullFileName,pData,size,szSenderID);
/*
        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"SKT") )
        {
            ret = parsingSKT(strParsingData,pData,size);
            createFile(path,"TXT_SKT",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    szSenderID);
        }

        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"KTF") )
        {
            strTxt = "";
            strTxt.reserve(0);

            strTxt = (char*)pData;
            bKTFTXT = true;
            
			ret = parsingKTF(strParsingData,pData,size);
            createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    szSenderID);
			
        }
*/
/*
        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"LGT") )
        {
			// 2008-10-07 TXT_LGT 파일을 생성하지 않음
            createFile(path,"TXT_LGT",szYYYYMM,szFullFileName,pData,size,szSenderID);
        }
*/
        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"SSN") )
        {
            createFile(path,"TXT_SSN",szYYYYMM,szFullFileName,pData,size,szSenderID);
        }

        sprintf(szTxtPath,"%s/%s",szYYYYMM,szFullFileName);

    } else {

// get ctn id
//        getCTNID2DB(db,  senderDbMMSID,timeout, senderDbDomainName);
        if( bBcastFlag ) return 0;

        sprintf(szFullFileName,"%d_%d.%s",
                ctnid,
                ctnseq,
                getExtension(szFileName)
                );

        createFile(path,classify,szYYYYMM,szFullFileName,pData,size,szSenderID);

        // 디비 위해 list 에 입력
        memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));

        mmsCtnTbl.nCtnId = ctnid;
        sprintf(mmsCtnTbl.szCtnName,"%s/%s/%s",classify,szYYYYMM,szFullFileName);
        strcpy(mmsCtnTbl.szCtnMime,classify);
        mmsCtnTbl.nCtnSeq = ctnseq;
        strcpy(mmsCtnTbl.szCtnSvc,szCtnSvc);
        strcpy(mmsCtnTbl.szFileName,szFullFileName);

        listMMSCtnTbl.push_back(mmsCtnTbl);

    }

    return 0;
}

int CMMSFileProcess::classifyMData(char* path,int mmsid ,char* szYYYYMM,
        void* pData, int size,char* classify,char* szFileName,char* szSenderID,int ctnid,int ctnseq,
        CKSSocket& db,
        int timeout,
        char* senderDbDomainName,
        char* szCtnSvc,
        int bBcastFlag)
{
    int ret;
    char szFullFileName[512];
    CMMSCtnTbl mmsCtnTbl;
    CSenderDbMMSID senderDbMMSID;
    string strParsingData;
    int nParsingLength;

    if( strstr(classify,"TXT")  )
    {
    	/*
		//컬러 문자 유무 체크.2012.12.10.by han
		if ((strstr((char*)pData, "<#Y>") && strstr((char*)pData, "</#Y>")) || 
			(strstr((char*)pData, "<#R>") && strstr((char*)pData, "</#R>")) || 
			(strstr((char*)pData, "<#B>") && strstr((char*)pData, "</#B>")) || 
			(strstr((char*)pData, "<#G>") && strstr((char*)pData, "</#G>")))
		{
			setTxtColorYN(1);
		}
		else
		{
			setTxtColorYN(0);
		}
		//
		*/
		setTxtColorYN(0);
		sprintf(szFullFileName,"%lld.txt",
                mmsid);

        fflush(stdout);

		// 2008-10-07 TXT 파일을 생성하지 않음
        //createFile(path,"TXT",szYYYYMM,szFullFileName,pData,size,szSenderID);
/*
        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"SKT") )
        {
            ret = parsingSKT(strParsingData,pData,size);
            createFile(path,"TXT_SKT",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    szSenderID);
        }

        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"KTF") )
        {
            strTxt = "";
            strTxt.reserve(0);

            strTxt = (char*)pData;
            bKTFTXT = true;
            
			ret = parsingKTF(strParsingData,pData,size);
            createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    szSenderID);
			
        }
*/
/*
        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"LGT") )
        {
			// 2008-10-07 TXT_LGT 파일을 생성하지 않음
            createFile(path,"TXT_LGT",szYYYYMM,szFullFileName,pData,size,szSenderID);
        }
*/
        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"SSN") )
        {
            createFile(path,"TXT_SSN",szYYYYMM,szFullFileName,pData,size,szSenderID);
        }

        sprintf(szTxtPath,"%s/%s",szYYYYMM,szFullFileName);

    } else {

// get ctn id
//        getCTNID2DB(db,  senderDbMMSID,timeout, senderDbDomainName);
        if( bBcastFlag ) return 0;

        sprintf(szFullFileName,"%d_%d.%s",
                ctnid,
                ctnseq,
                getExtension(szFileName)
                );

        createFile(path,classify,szYYYYMM,szFullFileName,pData,size,szSenderID);

        // 디비 위해 list 에 입력
        memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));

        mmsCtnTbl.nCtnId = ctnid;
        sprintf(mmsCtnTbl.szCtnName,"%s/%s/%s",classify,szYYYYMM,szFullFileName);
        strcpy(mmsCtnTbl.szCtnMime,classify);
        mmsCtnTbl.nCtnSeq = ctnseq;
        strcpy(mmsCtnTbl.szCtnSvc,szCtnSvc);
        strcpy(mmsCtnTbl.szFileName,szFullFileName);

        listMMSCtnTbl.push_back(mmsCtnTbl);

    }

    return 0;
}


int CMMSFileProcess::createFile(char* szDefaultPath, char* szCtnType,char* szYYYYMM, char* szFileName,
        void* pData,int size,char* szSenderID)
{
    int ret;
    FILE* fp=NULL;
    char szFile[256];
    sprintf(szFile,"%s/%s/%s/%s",szDefaultPath,szCtnType,szYYYYMM,szFileName);
    
/* 20131122 LSY comment
	printf("---- szFile[%s]\n",szFile);
    fflush(stdout);
*/
    fp = fopen(szFile,"wb");
    if( fp == NULL )
    {
        char szDir[256];
        sprintf(szDir,"%s/%s/%s", szDefaultPath, szCtnType, szYYYYMM);

        // Ensure intermediate directories when szYYYYMM contains a slash (e.g., YYYYMM/MMDD)
        const char* pSlash = strchr(szYYYYMM, '/');
        if (pSlash) {
            char szFirst[128]; memset(szFirst, 0x00, sizeof(szFirst));
            memcpy(szFirst, szYYYYMM, pSlash - szYYYYMM); // first segment (e.g., YYYYMM)
            char szDirFirst[256];
            sprintf(szDirFirst, "%s/%s/%s", szDefaultPath, szCtnType, szFirst);
            mkdir(szDirFirst, 0777);
        }
        mkdir(szDir, 0777);

        fp = fopen(szFile, "wb");
        if( fp == NULL )
        {
            return -1;
        }
    }

    try 
	{
        ret = fwrite(pData,size,1,fp);
    } catch (char* message) {
        sprintf(szErrorMsg,"--- file write Error [%s]",message);
        printf(szErrorMsg);
        fflush(stdout);
        fclose(fp);
        return -1;
    }
    fflush(fp);
    fclose(fp);

    return 0;
}



char* CMMSFileProcess::getExtension(char* fileName)
{
    int i;

    for(i=strlen(fileName);i>0;i--)
    {
        if( fileName[i] == '.' )
            break;
    }

    return fileName+i+1;
}

int CMMSFileProcess::getCTNID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,int timeout, char* senderDbDomainName)
{
    CSenderDbInfoAck senderDbInfoAck;
    CDBUtil dbUtil;
    senderDbMMSID.header.type = GETCTNID;
    senderDbMMSID.header.leng = sizeof(CSenderDbMMSID) - sizeof(Header);
    senderDbMMSID.mmsid = 0;
//    memcpy(senderDbMMSID.szCid,cid,10);

    dbUtil.sendQuery(
            db,
            (void*)&senderDbMMSID,
            (void*)&senderDbInfoAck,
            timeout,
            senderDbDomainName);

    senderDbMMSID.mmsid = senderDbInfoAck.mmsid;
    senderDbMMSID.ctnid = senderDbInfoAck.ctnid;

    return 0;
}

void CMMSFileProcess::clearListMMSCtnTbl()
{
    listMMSCtnTblPosition pos,posPrev;

    pos = listMMSCtnTbl.begin();

    while( pos != listMMSCtnTbl.end() )
    {
        posPrev = pos++;
        // 해당 string 메모리 제거 해야되나?
        listMMSCtnTbl.erase(posPrev);
    }

    return ;
}




int CMMSFileProcess::getMMSCtnTblFirst(CMMSCtnTbl& mmsCtnTbl)
{
    m_MMSCtnTblPos = listMMSCtnTbl.begin();

    if( m_MMSCtnTblPos != listMMSCtnTbl.end() )
    {
        memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));

        mmsCtnTbl.nCtnId = (*m_MMSCtnTblPos).nCtnId;
        strcpy(mmsCtnTbl.szCtnName,(*m_MMSCtnTblPos).szCtnName);
        strcpy(mmsCtnTbl.szCtnMime,(*m_MMSCtnTblPos).szCtnMime);
        mmsCtnTbl.nCtnSeq = (*m_MMSCtnTblPos).nCtnSeq;
        strcpy(mmsCtnTbl.szCtnSvc,(*m_MMSCtnTblPos).szCtnSvc);
        strcpy(mmsCtnTbl.szFileName,(*m_MMSCtnTblPos).szFileName);

        return 0;
    }

    return -1;
}


int CMMSFileProcess::getMMSCtnTblNext(CMMSCtnTbl& mmsCtnTbl)
{

    m_MMSCtnTblPos++;

    if( m_MMSCtnTblPos != listMMSCtnTbl.end() )
    {
        memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));
        mmsCtnTbl.nCtnId = (*m_MMSCtnTblPos).nCtnId;
        strcpy(mmsCtnTbl.szCtnName,(*m_MMSCtnTblPos).szCtnName);
        strcpy(mmsCtnTbl.szCtnMime,(*m_MMSCtnTblPos).szCtnMime);
        mmsCtnTbl.nCtnSeq = (*m_MMSCtnTblPos).nCtnSeq;
        strcpy(mmsCtnTbl.szCtnSvc,(*m_MMSCtnTblPos).szCtnSvc);
        strcpy(mmsCtnTbl.szFileName,(*m_MMSCtnTblPos).szFileName);
        return 0;
    }

    return -1;
}

char* CMMSFileProcess::getTxtPath()
{
    return szTxtPath;
}

int CMMSFileProcess::getTxtColorYN()
{
    return nColorYN;
}

void CMMSFileProcess::setTxtColorYN(int nYN)
{
    nColorYN = nYN;
}
int  CMMSFileProcess::parsingKTF(string& strParsingData, void* pData, int size)
{
    char* szStt=NULL;
    string strLine;
    strParsingData = "";
    strParsingData.reserve(0);
    strParsingData = "<HTML><HEAD></HEAD><BODY>";

    CMMSCtnTbl mmsCtnTbl;
    int ret;

    ret = getMMSCtnTblFirst(mmsCtnTbl);

    while(ret == 0)
    {
        if( strstr(mmsCtnTbl.szCtnMime,"IMG") )
        {
            strParsingData += "<IMG SRC=\"cid:";
        } 
		else if( strstr(mmsCtnTbl.szCtnMime,"SND") )
        {
            strParsingData += "<BGSound SRC=\"cid:";
        }
		else 
		{
            ret = getMMSCtnTblNext(mmsCtnTbl);
            continue;
        }

        strParsingData += mmsCtnTbl.szFileName;
        strParsingData += "\"><BR>";

        ret = getMMSCtnTblNext(mmsCtnTbl);
    }

    string data = (char*)pData;
    int idx;

	//KSKYB 컬러태그 변환.2012.12.10.by han
    while( (idx=data.find("<#Y>") ) >= 0 )
        data.replace(idx,4,"<font color=yellow>");
    while( (idx=data.find("</#Y>") ) >= 0 )
        data.replace(idx,5,"</font>");
    while( (idx=data.find("<#B>") ) >= 0 )
        data.replace(idx,4,"<font color=blue>");
    while( (idx=data.find("</#B>") ) >= 0 )
        data.replace(idx,5,"</font>");
    while( (idx=data.find("<#R>") ) >= 0 )
        data.replace(idx,4,"<font color=red>");
    while( (idx=data.find("</#R>") ) >= 0 )
        data.replace(idx,5,"</font>");
    while( (idx=data.find("<#G>") ) >= 0 )
        data.replace(idx,4,"<font color=green>");
    while( (idx=data.find("</#G>") ) >= 0 )
        data.replace(idx,5,"</font>");
	//
    while( (idx=data.find("\r") ) >= 0 )
        data.replace(idx,1,"");

    szStt = (char*)data.c_str();


    while(1)
    {
        szStt = matchString2(szStt,"\n",strLine);
        if( szStt == NULL )
            break;
        strParsingData += strLine;
        strParsingData += "<BR>";
    }

    strParsingData += "</BODY></HTML>";

    return strParsingData.length();

}



/* 2010-04-08 수정 SKT black */
/*
<style type="text/css">
<!--
color1{color:0,0,0;}
-->
</style>
<div><color1>검은색</color1></div>
*/
int  CMMSFileProcess::parsingSKT(string& strParsingData, void* pData, int size)
{
    char* szStt=NULL;
    string strLine;
    strParsingData = "";
    strParsingData.reserve(0);
    strParsingData = "<?xml version=\"1.0\" encoding=\"euc-kr\" ?>\r\n";
    strParsingData += "<xt xmlns=\"http://www.w3.org/1999/xhtml\">\r\n";
    strParsingData += "<head>\r\n";
	strParsingData += "<style type=\"text/css\">\r\n";
	strParsingData += "<!--\r\n";
	strParsingData += "color1{color:0,0,0;}\r\n";
	//컬러태그추가.2012.12.10.by han
	strParsingData += "color2{color:255,255,0;}\r\n";
	strParsingData += "color3{color:0,0,255;}\r\n";
	strParsingData += "color4{color:255,0,0;}\r\n";
	strParsingData += "color5{color:0,255,0;}\r\n";
	
	strParsingData += "-->\r\n";
	strParsingData += "</style>\r\n";
    strParsingData += "</head>\r\n";
    strParsingData += "<body bgcolor=\"#FFFFFF\">\r\n";
    string data = (char*)pData;
    int idx;

	while( (idx=data.find("\r") ) >= 0 )
        data.replace(idx,1,"");

    regmatch_t match_t;
    regex_t preg;
    char szMatch[32];
    idx = regcomp(&preg,"&#[0-9]+;",REG_EXTENDED);

	if( idx != 0 )
        return idx;

    while(regexec(&preg,data.c_str(),1,&match_t,0) == 0 )
    {
        memset(szMatch,0x00,sizeof(szMatch));
        memcpy(szMatch,data.c_str()+match_t.rm_so ,  match_t.rm_eo- match_t.rm_so);
        while( (idx = data.find(szMatch) ) >= 0 )
            data.replace(idx,strlen(szMatch),"?");
    }

    regfree(&preg);

	//KSKYB 컬러태그 변환.2012.12.10.by han
    while( (idx=data.find("<#Y>") ) >= 0 )
        data.replace(idx,4,"</color1><color2>");
    while( (idx=data.find("</#Y>") ) >= 0 )
        data.replace(idx,5,"</color2><color1>");
    while( (idx=data.find("<#B>") ) >= 0 )
        data.replace(idx,4,"</color1><color3>");
    while( (idx=data.find("</#B>") ) >= 0 )
        data.replace(idx,5,"</color3><color1>");
    while( (idx=data.find("<#R>") ) >= 0 )
        data.replace(idx,4,"</color1><color4>");
    while( (idx=data.find("</#R>") ) >= 0 )
        data.replace(idx,5,"</color4><color1>");
    while( (idx=data.find("<#G>") ) >= 0 )
        data.replace(idx,4,"</color1><color5>");
    while( (idx=data.find("</#G>") ) >= 0 )
        data.replace(idx,5,"</color5><color1>");
	//
/* 주석 처리.2012.12.10.by han
    while( (idx=data.find("&") ) >= 0 )
        data.replace(idx,1,"chozo99");

    while( (idx=data.find("chozo99") ) >= 0 )
        data.replace(idx,7,"&amp;");

	while( (idx=data.find(">") ) >= 0 )
		data.replace(idx,1,"&gt;");

	while( (idx=data.find("<") ) >= 0 )
		data.replace(idx,1,"&lt;");

    while( (idx=data.find("\"") ) >= 0 )
        data.replace(idx,1,"&quot;");
*/
    szStt = (char*)data.c_str();

    while(1)
    {
        szStt = matchString2(szStt,"\n",strLine);
        if( szStt == NULL )
            break;

        strParsingData += "<div><color1>";
        strParsingData += strLine;
        // strParsingData += "</div>\r\n<p></p>\r\n";
        strParsingData += "</color1></div>\r\n";
    }

    strParsingData += "</body>\r\n</xt>\r\n";

    return strParsingData.length();

}


/** @brief 해당 태그부터 \n 까지 문자열을 가져온다.
 * @return 가져온 문자열의 다음 위치 포인트를 반환한다.
 */
char* CMMSFileProcess::matchString2(char* szOrg, char* szTag, string& strVal)
{
    char* szStt=szOrg;
    char* szEnd=NULL;
    if( szOrg == NULL )
        return NULL;
    if ((szEnd=strstr(szOrg,szTag))) {
        if( (szEnd - szStt) == 0 )
        {
            strVal = "";
            strVal.reserve(0);
//            strVal = "\r\n";
        } 
		else
		{
        //    return NULL;
            strVal = "";
            strVal.reserve(0);
            if( szEnd == NULL )
                strVal.insert(0,szStt);
            else
                strVal.insert(0,szStt,szEnd-szStt);
        }
//        memcpy(szVal,szStt,szEnd-szStt);
    } else {
        if( strlen(szStt) > 0 )
        {
            szEnd = szStt + strlen(szStt);
            strVal = "";

            strVal.reserve(0);
            strVal.insert(0,szStt,szEnd-szStt);
            return szEnd;
        }

    }
    if( szStt == NULL ) return NULL;
    if( szEnd == NULL ) return NULL;
    return szEnd+1;
}
