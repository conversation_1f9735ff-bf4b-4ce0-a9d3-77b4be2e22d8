#include "mmsPacketBase.h"

char* CMMSPacketBase::findValue(char* szOrg, char* szTag, char* szVal)
{
    char* szStt;          
    char* szEnd;          
    if ((szStt=strstr(szOrg,szTag))) 
	{     
        szStt=szStt+strlen(szTag)+1;   
        szEnd=strstr(szStt,"\r\n");    
        memcpy(szVal,szStt,szEnd-szStt);       
    }
    return szVal;
}

char* CMMSPacketBase::findValue(char* szOrg, char* szTag, string& strVal)
{
    char* szStt;          
    char* szEnd;          
    strVal = "";
    strVal.reserve(0);

    if ((szStt=strstr(szOrg,szTag))) 
	{     
        szStt=szStt+strlen(szTag)+1;   
        szEnd=strstr(szStt,"\r\n");    
        strVal.insert(0,szStt,szEnd-szStt);
//        memcpy(szVal,szStt,szEnd-szStt);       
    }
    return (char*)strVal.c_str();
}

