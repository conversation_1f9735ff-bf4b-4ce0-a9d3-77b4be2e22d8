#include "dbUtil.h"

CDBUtil::CDBUtil()
{
    strErrorMsg = "";
}

CDBUtil::~CDBUtil()
{
}


void* CDBUtil::sendQuery(
        CKSSocket& db,
        void* data,
        void* ack,
        int dbRequestTimeOut, 
        char* senderDbDomainName)
{
    int ret;
    time_t ThisT,LastT;
    Header* pHeader = (Header*)data;

    time(&LastT);

RETRYQUERY2DB:
    time(&ThisT);
    if( (ret = (int)difftime(ThisT,LastT)) > dbRequestTimeOut )
    {
        strErrorMsg = "timeout warnning ";
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "/";
        strErrorMsg += dbRequestTimeOut;
        strErrorMsg += "]";
        db.close();
        return NULL;
    }
    ret = db.connectDomain(senderDbDomainName);
    if( ret != 0 )
    {
        strErrorMsg = "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += " conn Error :";
        strErrorMsg += strerror(ret);
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    ret = db.send((char*)data,pHeader->leng + sizeof(Header));
    if( ret != pHeader->leng + sizeof(Header) )
    {
        strErrorMsg = "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += " : ";
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "/";
        strErrorMsg += pHeader->leng + sizeof(Header);
        strErrorMsg += "] : ";
        strErrorMsg += " send Error :";
        strErrorMsg += strerror(ret);
        
        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    /* gettimeofday */            
    struct timeval timefirst, timesecond; 
    struct timezone tzp;      
    int secBuf, microsecBuf;  
    float timeBuf;    

    gettimeofday(&timefirst,&tzp); 
    /* gettimeofday */   
    ret = db.select(dbRequestTimeOut,0);
    if( ret == 0 )
    {
        strErrorMsg = "recv ack timeout warnning ";
        strErrorMsg += "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "/";
        strErrorMsg += dbRequestTimeOut;
        strErrorMsg += "]";

        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    if( ret < 0 )
    {
        strErrorMsg = "recv ack timeout warnning ";
        strErrorMsg += "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += "[";
        strErrorMsg += strerror(ret);
        strErrorMsg += "]";

        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    gettimeofday(&timesecond,&tzp);

    secBuf = (timesecond.tv_sec - timefirst.tv_sec);
    microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
    timeBuf = microsecBuf;
    timeBuf = timeBuf / 1000000;
    timeBuf = timeBuf + secBuf;
//    logPrintS(0,"take time [%f]",timeBuf);

//    CSenderDbInfoAck senderDbInfoAck;

    ret = db.rcvmsg((char*)ack);
    if( ret == 0 || ret < 0 )
    {
        strErrorMsg = "rcvmsg domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += strerror(errno);
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "]";

 
        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    db.close();
    strErrorMsg = "";

    return NULL;
}
void* CDBUtil::sendQuery(
        CKSSocket& db,
        void* data,
        void* ack,
        int dbRequestTimeOut, 
        char* senderDbDomainName,
        int& nResult)
{
    int ret;
    time_t ThisT,LastT;
    Header* pHeader = (Header*)data;
		nResult = 0;
    time(&LastT);

RETRYQUERY2DB:
    time(&ThisT);
    if( (ret = (int)difftime(ThisT,LastT)) > dbRequestTimeOut )
    {
    		nResult = -1;
        strErrorMsg = "timeout warnning ";
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "/";
        strErrorMsg += dbRequestTimeOut;
        strErrorMsg += "]";
        db.close();
        return NULL;
    }
    ret = db.connectDomain(senderDbDomainName);
    if( ret != 0 )
    {
    		nResult = -2;
        strErrorMsg = "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += " conn Error :";
        strErrorMsg += strerror(ret);
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    ret = db.send((char*)data,pHeader->leng + sizeof(Header));
    if( ret != pHeader->leng + sizeof(Header) )
    {
    		nResult = -3;
        strErrorMsg = "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += " : ";
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "/";
        strErrorMsg += pHeader->leng + sizeof(Header);
        strErrorMsg += "] : ";
        strErrorMsg += " send Error :";
        strErrorMsg += strerror(ret);
        
        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    /* gettimeofday */            
    struct timeval timefirst, timesecond; 
    struct timezone tzp;      
    int secBuf, microsecBuf;  
    float timeBuf;    

    gettimeofday(&timefirst,&tzp); 
    /* gettimeofday */   
    ret = db.select(dbRequestTimeOut,0);
    if( ret == 0 )
    {
    		nResult = -4;
        strErrorMsg = "recv ack timeout warnning ";
        strErrorMsg += "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "/";
        strErrorMsg += dbRequestTimeOut;
        strErrorMsg += "]";

        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    if( ret < 0 )
    {
    		nResult = -5;
        strErrorMsg = "recv ack timeout warnning ";
        strErrorMsg += "domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += "[";
        strErrorMsg += strerror(ret);
        strErrorMsg += "]";

        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    gettimeofday(&timesecond,&tzp);

    secBuf = (timesecond.tv_sec - timefirst.tv_sec);
    microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
    timeBuf = microsecBuf;
    timeBuf = timeBuf / 1000000;
    timeBuf = timeBuf + secBuf;
//    logPrintS(0,"take time [%f]",timeBuf);

//    CSenderDbInfoAck senderDbInfoAck;

    ret = db.rcvmsg((char*)ack);
    if( ret == 0 || ret < 0 )
    {
    		nResult = -6;
        strErrorMsg = "rcvmsg domain:";
        strErrorMsg += senderDbDomainName;
        strErrorMsg += strerror(errno);
        strErrorMsg += "[";
        strErrorMsg += ret;
        strErrorMsg += "]";

 
        db.close();
        wait_a_moment(900000);
        goto RETRYQUERY2DB;
    }

    db.close();
    strErrorMsg = "";

    return NULL;
}

const char* CDBUtil::getErrorMsg()
{
    return strErrorMsg.c_str();
}

