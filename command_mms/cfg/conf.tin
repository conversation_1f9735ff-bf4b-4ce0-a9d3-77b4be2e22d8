// PROCESS CONFIGURATION .tin FILE
// 98.08.14 R1.2 -> R2.0 Up Grade
// -------------------------------------------------------------------

// -------------------------------------------------------------------------------
// MONITOR PROCESS
// -------------------------------------------------------------------------------

/= process_no           1001
 = process_type         1
 = process_name         48_MMS_SESSION_MONITOR_MMS
 = execute_directory    /user/neomms/command_mms/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             60001
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           1002
 = process_type         1
 = process_name         48_MMS_LOGONDB_MONITOR_MMS
 = execute_directory    /user/neomms/command_mms/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             60002
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           1003
 = process_type         1
 = process_name         48_MMS_SNDDB_MONITOR___MMS
 = execute_directory    /user/neomms/command_mms/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             60003
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           1004
 = process_type         1
 = process_name         48_MMS_RPTDB_MONITOR___MMS
 = execute_directory    /user/neomms/command_mms/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             60004
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        0

/= process_no           1005
 = process_type         1
 = process_name         48_MMS_ETC_MONITOR_____MMS
 = execute_directory    /user/neomms/command_mms/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             60005
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

// -------------------------------------------------------------------------------
// LOGGER PROCESS
// -------------------------------------------------------------------------------

/= process_no           2001
 = process_type         2
 = process_name         48_MMS_LOG_SMS_________MMS
 = execute_directory    /user/neomms/command_mms/bin
 = execute_program      log
 = command_line_1       /data/log_mms/
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             61001
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

// -------------------------------------------------------------------------------
// CUSTOM PROCESS

/= process_no           3001
 = process_type         3
 = process_name         48_MMS_SESSION_________MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      logonSession
 = command_line_1       /user/neomms/cfg/logon2007/logonSession.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3002
 = process_type         3
 = process_name         48_MMS_LOGON_DB________MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      logonDB
 = command_line_1       /user/neomms/cfg/logon2007/logonDB.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1002
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3003
 = process_type         3
 = process_name         48_MMS_MSND_DB_PART____MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      senderMMSDB_PART
 = command_line_1       /user/neomms/cfg/logon2007/senderMMSDB_PART.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1003
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        0

/= process_no           3004
 = process_type         3
 = process_name         48_MMS_MRPT_DB1________MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      reportMMSDB
 = command_line_1       /user/neomms/cfg/logon2007/reportMMSDB.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        0

/= process_no           3005
 = process_type         3
 = process_name         48_MMS_LOGON_MONITOR___MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      monitorProcess
 = command_line_1       /user/neomms/cfg/logon2007/monitor.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1005
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3006
 = process_type         3
 = process_name         48_MMS_LOGON_ADM_SVR___MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      adminProcess
 = command_line_1       /user/neomms/cfg/logon2007/adminProcess.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1005
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3007
 = process_type         3
 = process_name         48_MMS_SESSION_1_BM1___MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      logonSessionMMSB
 = command_line_1       /user/neomms/cfg/logon2007/logonSessionBM01.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        0

/= process_no           3008
 = process_type         3
 = process_name         48_MMS_SESSION_1_BM2___MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      logonSessionMMSB
 = command_line_1       /user/neomms/cfg/logon2007/logonSessionBM02.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        0

/= process_no           3009
 = process_type         3
 = process_name         48_MMS_SESSION_1_BM1_T_MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      logonSessionMMSB_PART
 = command_line_1       /user/neomms/cfg/logon2007/logonSessionBM01_T.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        0

/= process_no           3010
 = process_type         3
 = process_name         48_MMS_SESSION_BCMSG___MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      logonSession
 = command_line_1       /user/neomms/cfg/logon2007/logonSession_bcmsg.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    2001
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3011
 = process_type         3
 = process_name         48_MMS_SESSION_TALK____MMS
 = execute_directory    /user/neomms/daemon_logon7/bin
 = execute_program      logonSession
 = command_line_1       /user/neomms/cfg/logon2007/logonSession_talk.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        0 

// -------------------------------------------------------------------------------
// WATCH DOG
// -------------------------------------------------------------------------------

/= process_no           9999
 = process_type         5
 = process_name         48_MMS_WATCHDOG________MMS
 = execute_directory    /user/neomms/command_mms/bin
 = execute_program      dog
 = command_line_1       1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/* END */
